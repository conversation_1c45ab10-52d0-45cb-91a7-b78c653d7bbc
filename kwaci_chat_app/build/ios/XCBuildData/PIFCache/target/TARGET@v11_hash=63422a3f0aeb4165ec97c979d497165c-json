{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e627b81f2277a26392e82dc437b640b9", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9842c6b28a327201da0e3774175d95aef0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b6d27ca3f133a8809ce8858835ba048", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98e27ab479a5f322ba47f7f43385c35b96", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b6d27ca3f133a8809ce8858835ba048", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9871664924ed26e24e7bd4ce0aad3c5f0f", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98551afcd19e15e17c7b87a340494e1040", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b3cf8868f4de1c7f582ebde1261de1fb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a292699c7027eaa680bfe5da607e708f", "guid": "bfdfe7dc352907fc980b868725387e981ed11ced14ff10e6adcc49a5af2bbf8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852b8cf46dbb173cb75a3b48bbfbb8858", "guid": "bfdfe7dc352907fc980b868725387e98f1a1d8b0ec0070bb2732b20f0a0be546"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835648ce799d62afd8c7500844a76b60e", "guid": "bfdfe7dc352907fc980b868725387e9879e8bb89a51f2ad8a51235abe92e0af0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ff058d7b390b6f2003ce67e8d91cd71", "guid": "bfdfe7dc352907fc980b868725387e98a123994f45c8e6f8814f2b8b68659d29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98defb1e32e9bd8aba19d9a67f858e60b0", "guid": "bfdfe7dc352907fc980b868725387e98678d0512cfad2311f05b2fd6a7786a44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808a0a427f1edd3500994a26f3b1b4614", "guid": "bfdfe7dc352907fc980b868725387e982e0659baeab9cb654cc8e4cee80c7b91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a490e75903fb2b0a8fb899a90d4287ed", "guid": "bfdfe7dc352907fc980b868725387e98eee16a170c944c10a4658e6d1c6a1861"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c76b719348323ba9f42f0d81d70b161", "guid": "bfdfe7dc352907fc980b868725387e98ca605962211f24eb7b6e8bc2d5efdced"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980962a367e6152e96b70a125d815020af", "guid": "bfdfe7dc352907fc980b868725387e98ffd7f9185211e909f940c98dc9b758d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a11f800dc26d56426f6b37f8a283b0b", "guid": "bfdfe7dc352907fc980b868725387e98731573731aadc7244d57415b5d393637"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b08b2c243127aba3d1fc2398f7ba08f3", "guid": "bfdfe7dc352907fc980b868725387e98c56c3448d5858ad05693f3da9b86dd9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989796f8ce5b602046976a913145a66f6c", "guid": "bfdfe7dc352907fc980b868725387e98749f9aa028822af4d86eabc36ae62813"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986968906746efa810a96cf74bfa70670d", "guid": "bfdfe7dc352907fc980b868725387e9853c99821b9db48cb0b384c43dc74e9a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98936c99140c6d493cc2084861c68133a2", "guid": "bfdfe7dc352907fc980b868725387e989df5e64ee44ca8b82e2f1b62a3e0404d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9017cb198b86bc9ed0cf016c9d298fc", "guid": "bfdfe7dc352907fc980b868725387e98f3be9d37ea727879bf4d37855b605325"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98135e59325df8adc673d9b81eb08333fb", "guid": "bfdfe7dc352907fc980b868725387e986b928bd1e4d8c4ef4237c8261d64acd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edc8787438970f71c64e1d428d742b0c", "guid": "bfdfe7dc352907fc980b868725387e9832543efc5dea430c67434ac36f72fa8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878653648422e683002192a6134cd98ad", "guid": "bfdfe7dc352907fc980b868725387e98bd40cb265cabe6e7ad55d8f3d1134059"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c46c4af4d494babb82844bf0d68a4f4", "guid": "bfdfe7dc352907fc980b868725387e980e9518df3f3b92b402ace9d7d67c6500"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbca80ed18432ed6f1cbccf6775b30bb", "guid": "bfdfe7dc352907fc980b868725387e98e2dc06ea523384c50fb18d5b0a15bf88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1e46671a6db2c214e25ea65b3d2809b", "guid": "bfdfe7dc352907fc980b868725387e987dcd48a41a5fcb5e1b3783475c7871b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7b40e8c8c2cda51e62b5c5e2d2129be", "guid": "bfdfe7dc352907fc980b868725387e981d183ebbc1dfd840abbfe7ae64defb4a"}], "guid": "bfdfe7dc352907fc980b868725387e98ff3afe6e6d34115c26202edb6c7da141", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}