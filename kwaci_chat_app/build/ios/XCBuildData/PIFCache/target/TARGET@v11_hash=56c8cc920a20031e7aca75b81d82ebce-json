{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ae529f76a1aeb84aed676cbc438f2337", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e93b90f691ee2fa83cf1c49308d3fb26", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c0ad1a68339e983c25e4de21965f4a8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9860661ee4d60ec37ec408afd6be8c9868", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c0ad1a68339e983c25e4de21965f4a8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c1c69a63eb49963122fcf59ec4b426a7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e61c3706c4c5522b7ecd10e962efb922", "guid": "bfdfe7dc352907fc980b868725387e9855a5766e97d2b447c5e150d2f15db0ae", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9858570d2bdd18584de57bebdca9b5690e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986cd8b63f1efadf20d9ea1ed8f5a55c59", "guid": "bfdfe7dc352907fc980b868725387e986ebb29963fb17384b53b5cca9fb995ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d637eecdfef95bbdf30369120352bd46", "guid": "bfdfe7dc352907fc980b868725387e988e9af7512f60ac5413dba91879f457b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834be2bf990d387cbce4913d90fc0866d", "guid": "bfdfe7dc352907fc980b868725387e984daaff30f6e8abf3c861bc5c7ba8fc06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984767f7ebe824e56e6af065bd8c127fb1", "guid": "bfdfe7dc352907fc980b868725387e9891a0c966484fa2567a5a8575bee1dfd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98051e74ac2dfb008bbd683e30491aa7be", "guid": "bfdfe7dc352907fc980b868725387e98f597fff208646c71f2d3894a37308aca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982675e10da08f11651d5e552f26200541", "guid": "bfdfe7dc352907fc980b868725387e98ed4e9a04fd7f5bf31e3af883db23e47a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834f1d1fb0dd3d6538989f7502ec1644c", "guid": "bfdfe7dc352907fc980b868725387e986839754bf8fc5e364338b3c3fa101515"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825db5c0b0de22f7488309691c8541b75", "guid": "bfdfe7dc352907fc980b868725387e982f87ec781e237ee3b91d68e91a76e84e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982aa0b02535812fd0e603a89bcfb30ef5", "guid": "bfdfe7dc352907fc980b868725387e9890620fa51f1866fd6d560297a8d5f576"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b13723b07b108638ef45e70068037a0f", "guid": "bfdfe7dc352907fc980b868725387e98ec0d8e0173fa01ed02153a449996c783"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aecacec943b3b7fb13ca4f783aeb124", "guid": "bfdfe7dc352907fc980b868725387e98324afd12f8ed11423eedaf06f4040f9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982683856913254da4512b6e02f6e4f14f", "guid": "bfdfe7dc352907fc980b868725387e98cea206fbf9d8260df1c5807faa201dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9552c70edc7387c0167f655a2f9cab9", "guid": "bfdfe7dc352907fc980b868725387e98a869a838ba29d7e5fccdceda4b51c9a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0333415fb47d981a93980d7783a25bc", "guid": "bfdfe7dc352907fc980b868725387e98dd07f7aa110103948beca4f8ca4d8957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864c787672805f0b55134cb199dc71c0e", "guid": "bfdfe7dc352907fc980b868725387e98aa1e957da20d1018020b88132c15a1fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98850d9e8cfb22b221e8e383807ea38a07", "guid": "bfdfe7dc352907fc980b868725387e98e79578cd88a5c7903e4fe91668aad032"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5b79d94ff93ec63566064eb357dbe2c", "guid": "bfdfe7dc352907fc980b868725387e984c4747ffaccfb535480e7168fae73f92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830b4739ebde111a52547b4d18a503410", "guid": "bfdfe7dc352907fc980b868725387e98156b83575f753af029f4b52f400f1364"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d2693191bcef035b93392dabd29e915", "guid": "bfdfe7dc352907fc980b868725387e981d50c651eef55fef8668d15c49eb9a1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7133de307e021954fb1ea816636ae53", "guid": "bfdfe7dc352907fc980b868725387e98383ad3e4957148d42c37d49507194ff2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817c885c72c438eb48d3c624d21c32879", "guid": "bfdfe7dc352907fc980b868725387e980d17e1fbd347b611f0b5f525a29868a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986504caedde90e4b7bbf90bc3c93b56f5", "guid": "bfdfe7dc352907fc980b868725387e986f6f776f73ad7edee20d48d2e256b66b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98119755cf3c3ab367d61b1f6a2b919fbf", "guid": "bfdfe7dc352907fc980b868725387e98c85a29244a379b6c92955392fa630c3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcde9c1fdc32784294392718f0d45ce0", "guid": "bfdfe7dc352907fc980b868725387e9863712fe71da8036c7c277852e90ac278"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e630bb3219120e5c9a7785f505fc0eba", "guid": "bfdfe7dc352907fc980b868725387e98f081867fafd18e1df6fb9c5270e8fa7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3eb35d57bc96b724d16fb6a815089bd", "guid": "bfdfe7dc352907fc980b868725387e98eb0884b5743de9c5dfe404fab07c5014"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98807401bfb1caf2d2d87935ce9df53b6e", "guid": "bfdfe7dc352907fc980b868725387e9872892ce4dfc4aa480447e34dafff82ff"}], "guid": "bfdfe7dc352907fc980b868725387e98b96f2ccd6db99cd984761d9a5aeefed9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987245d663eb126d58dc3bffa3f993fb1d", "guid": "bfdfe7dc352907fc980b868725387e98debbdf07b7a355077ac2342d0d92b149"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813d077fbf01bac8382743594b271c07f", "guid": "bfdfe7dc352907fc980b868725387e9871baf35b0aed4d7bd32abc7b1369b85c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d56474ef5850c087d24d24a69093581f", "guid": "bfdfe7dc352907fc980b868725387e9858ff909d590d2def05b432d24b50cbe2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834f426257763bc4b24489402369e6806", "guid": "bfdfe7dc352907fc980b868725387e9874aa7e8e7d7215ff7c6e29565d5cac13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0372a857a824418dfb3d5a853ed269f", "guid": "bfdfe7dc352907fc980b868725387e981326446f5dc7134cc8790e02a200c3e7"}], "guid": "bfdfe7dc352907fc980b868725387e98a8e716c2cd92cc21c5ba334e133efd39", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c7210f30c8746421f4cd23d11f31cefa", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98c014e3fed9e9ae04a77ac3a400b8e049", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}