{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988d017df6eb26a1783b874214268dea26", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftyGif/SwiftyGif-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyGif/SwiftyGif.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SwiftyGif", "PRODUCT_NAME": "SwiftyGif", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98808cb512651c03ba2fce4d5de7cb810e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f191c54bfc8bd2078a4337a8bbbe4787", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftyGif/SwiftyGif-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyGif/SwiftyGif.modulemap", "PRODUCT_MODULE_NAME": "SwiftyGif", "PRODUCT_NAME": "SwiftyGif", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9891adc58a6680c679aa6def90150bdff2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f191c54bfc8bd2078a4337a8bbbe4787", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftyGif/SwiftyGif-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyGif/SwiftyGif.modulemap", "PRODUCT_MODULE_NAME": "SwiftyGif", "PRODUCT_NAME": "SwiftyGif", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98161f7b00ee26473712d183d71467298f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98841e69c77a33c992a383444bcd690833", "guid": "bfdfe7dc352907fc980b868725387e980ce94fda20249046d3a5a4b371e82ea0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b9414b84d9ae46c6cfb7c8966e95c73", "guid": "bfdfe7dc352907fc980b868725387e986940f664431fb7e93819f6e26e8b5a67", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98427aec030e66e333ed9b334e13af979e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e5ec22b9c4ec919ca16aaf1106fdfda7", "guid": "bfdfe7dc352907fc980b868725387e980237e6ced975e834f82a3d4f41c43a54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1f3f2ed47d0040183997f8bbf5e6ac1", "guid": "bfdfe7dc352907fc980b868725387e98e831e49aac5b59efe798e2eb55be2877"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b5362145f0a8c83082dc613a6cf5234", "guid": "bfdfe7dc352907fc980b868725387e98409e14ad77db031cb597dc7925ee6708"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec31ea89f37bf925e7a4e918d80427c7", "guid": "bfdfe7dc352907fc980b868725387e98ae95f4b5747a37174de647f123a055b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a09117dc7d3b1c63324941c308702cff", "guid": "bfdfe7dc352907fc980b868725387e98cd27083a2291a5637cdecbe4a4552372"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da799bba8441f8c7fb816c76aba9c6c5", "guid": "bfdfe7dc352907fc980b868725387e9812832cecb7c22abede2da0fc9c2fb553"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e813830c6fcdcb4cb8bc6548d3e992d1", "guid": "bfdfe7dc352907fc980b868725387e9828ab658f3e10125b7d6ec3d3fe51dcb3"}], "guid": "bfdfe7dc352907fc980b868725387e9849134ee32eea719e9fe5cde5e5195794", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d56474ef5850c087d24d24a69093581f", "guid": "bfdfe7dc352907fc980b868725387e986d8726f6e5cb31afa3c745cda601661d"}], "guid": "bfdfe7dc352907fc980b868725387e985f127928761806894ab8beb50b5aa6ef", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98722f046be59e7afb05d55a123ecb9b7b", "targetReference": "bfdfe7dc352907fc980b868725387e98f5cd644fc2aeb8654450a2168f52697c"}], "guid": "bfdfe7dc352907fc980b868725387e98445f0892ee9248c033f998f534f28acb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98f5cd644fc2aeb8654450a2168f52697c", "name": "SwiftyGif-SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98290968646de0d07c6f6e2ed9e146ea78", "name": "SwiftyGif.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}