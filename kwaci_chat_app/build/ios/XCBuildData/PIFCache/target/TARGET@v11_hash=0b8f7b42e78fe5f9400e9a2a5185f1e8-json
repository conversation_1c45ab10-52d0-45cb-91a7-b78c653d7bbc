{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983223560aed345a917fd33d12527d8c65", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ee49f65d26d8a0c930cc03d16e0ffcc8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d99e9f632084c2efbce5bc64d1b71f9f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987d3fcd01a34fff9e18dec0764bcc371e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d99e9f632084c2efbce5bc64d1b71f9f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984def481d54810d0de6d3335b228b2a5d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980fc7541bdb9efe28e74a331aae7a72bc", "guid": "bfdfe7dc352907fc980b868725387e98f74bfe561cdc142d140be02f934f1dd5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b1fc19541ebc1cbe25efc146045ab9b", "guid": "bfdfe7dc352907fc980b868725387e98128d41c6be7fab5e0c350afb32404ee9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfe5ddb1c2fed7625533b87ab6e84809", "guid": "bfdfe7dc352907fc980b868725387e98996eaada1ef569420cabc129d90e2da5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830259f3c1bebf0348323c7663c27350e", "guid": "bfdfe7dc352907fc980b868725387e987840c2aacd1ce55599623d2fd8219817"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3bda0ddd7cd21d0f2a0189b03c16feb", "guid": "bfdfe7dc352907fc980b868725387e9843bbc6c7e6cfcb0b409324fd055d0733"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98129870bfefd202d1ea2c595c53677fb0", "guid": "bfdfe7dc352907fc980b868725387e9808d4ef82ccec1924430403b3fad24e14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984850db1540e306a024b4ec137de2bbab", "guid": "bfdfe7dc352907fc980b868725387e9848d7f4c6b424b5eb3bda0e21cd2f4b1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c04469c23ee6c8c140a8393667520d5c", "guid": "bfdfe7dc352907fc980b868725387e98fe5a8d4fd311a1de8df3b173d547fb02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ce02dd47a784baff4ca8c7ca82c0ec6", "guid": "bfdfe7dc352907fc980b868725387e98d7926028771abe4490aea6eb057df0a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f656999e8d53dd36b43e85e072dc3f3e", "guid": "bfdfe7dc352907fc980b868725387e98ba15d3216bf17a3026096f27b47b2084"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986331568854fc44a9cc4ba15f2f510e81", "guid": "bfdfe7dc352907fc980b868725387e9866e8ad8c42705df73de9d3ea6a45d657", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc886891b29c0975adaa38d521a88cc3", "guid": "bfdfe7dc352907fc980b868725387e986ed45bb24536a4f4c65921c87272200d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f04462421eadedbf398873e882dad62", "guid": "bfdfe7dc352907fc980b868725387e9839c167ada258f3fc19ab5c4d03331d93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982190f985fa443346628818d348cfe8df", "guid": "bfdfe7dc352907fc980b868725387e9812641da7dad587b2e5c4f906d00a4d67", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cc465b98567e5be1dff8b7284a07e4e3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98724a9896274bda31ec1bbd5b71fa6590", "guid": "bfdfe7dc352907fc980b868725387e985adaa6c4e40f33315ac0cec984200988"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892545063c8fa80f41d9003fd76b45b09", "guid": "bfdfe7dc352907fc980b868725387e98b66bc2a5e8397ad7553c7550ed81a8b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891a86dbccf77997de3cedc42c3f076ad", "guid": "bfdfe7dc352907fc980b868725387e984dfb7eb30a2be401cd610bfeffd4e000"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b24253653000884949b2020974c2480a", "guid": "bfdfe7dc352907fc980b868725387e98f121c456012e84e3920b1e569a862cc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b88b180a34f8f129bd84c5145224087", "guid": "bfdfe7dc352907fc980b868725387e981a3796db56db3bbb0b570434bd6eaebc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98788db8a89d35a7bd75f9cc86bb4b0116", "guid": "bfdfe7dc352907fc980b868725387e986949c0ee9f19296b1da4b9965340e04f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e39e9e61518ad65d687bc4e4fbc61214", "guid": "bfdfe7dc352907fc980b868725387e988dfb0b3f564c83890326c01342984f36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98badf47dfa772b8048e4f110ae51eb2d4", "guid": "bfdfe7dc352907fc980b868725387e983c190223341c96acab486a2921e070db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f001d2b268e1b0f2fa2858688ae5e844", "guid": "bfdfe7dc352907fc980b868725387e9868a5700dd6e3280ea41f8cb79e1d012a"}], "guid": "bfdfe7dc352907fc980b868725387e9837c2f0a37c50e959478519168227e455", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d56474ef5850c087d24d24a69093581f", "guid": "bfdfe7dc352907fc980b868725387e982f461d51c284a55b8f869fc9092ae5dc"}], "guid": "bfdfe7dc352907fc980b868725387e98dd47f73652ff7b522b7942f6a87afd23", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d17544c34b81de618417de5f9c91b4ec", "targetReference": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8"}], "guid": "bfdfe7dc352907fc980b868725387e98e60a652c76bfee084293e97b00176921", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8", "name": "sqflite_darwin-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e981304d3d2169071b3ca365b19f5340b7c", "name": "sqflite_darwin", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98dbbec3eebed26c79cc653713be723aba", "name": "sqflite_darwin.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}