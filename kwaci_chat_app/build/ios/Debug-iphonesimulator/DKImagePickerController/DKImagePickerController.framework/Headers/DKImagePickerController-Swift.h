#if 0
#elif defined(__arm64__) && __arm64__
// Generated by Apple Swift version 6.1 effective-5.10 (swiftlang-6.1.0.110.21 clang-1700.0.13.3)
#ifndef DKIMAGEPICKERCONTROLLER_SWIFT_H
#define DKIMAGEPICKERCONTROLLER_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#include <cstring>
#include <stdlib.h>
#include <new>
#include <type_traits>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <string.h>
#endif
#if defined(__cplusplus)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wnon-modular-include-in-framework-module"
#if defined(__arm64e__) && __has_include(<ptrauth.h>)
# include <ptrauth.h>
#else
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wreserved-macro-identifier"
# ifndef __ptrauth_swift_value_witness_function_pointer
#  define __ptrauth_swift_value_witness_function_pointer(x)
# endif
# ifndef __ptrauth_swift_class_method_pointer
#  define __ptrauth_swift_class_method_pointer(x)
# endif
#pragma clang diagnostic pop
#endif
#pragma clang diagnostic pop
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef unsigned char char8_t;
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...) 
# endif
#endif
#if !defined(SWIFT_RUNTIME_NAME)
# if __has_attribute(objc_runtime_name)
#  define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
# else
#  define SWIFT_RUNTIME_NAME(X) 
# endif
#endif
#if !defined(SWIFT_COMPILE_NAME)
# if __has_attribute(swift_name)
#  define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
# else
#  define SWIFT_COMPILE_NAME(X) 
# endif
#endif
#if !defined(SWIFT_METHOD_FAMILY)
# if __has_attribute(objc_method_family)
#  define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
# else
#  define SWIFT_METHOD_FAMILY(X) 
# endif
#endif
#if !defined(SWIFT_NOESCAPE)
# if __has_attribute(noescape)
#  define SWIFT_NOESCAPE __attribute__((noescape))
# else
#  define SWIFT_NOESCAPE 
# endif
#endif
#if !defined(SWIFT_RELEASES_ARGUMENT)
# if __has_attribute(ns_consumed)
#  define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
# else
#  define SWIFT_RELEASES_ARGUMENT 
# endif
#endif
#if !defined(SWIFT_WARN_UNUSED_RESULT)
# if __has_attribute(warn_unused_result)
#  define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
# else
#  define SWIFT_WARN_UNUSED_RESULT 
# endif
#endif
#if !defined(SWIFT_NORETURN)
# if __has_attribute(noreturn)
#  define SWIFT_NORETURN __attribute__((noreturn))
# else
#  define SWIFT_NORETURN 
# endif
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA 
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA 
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA 
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif
#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif
#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER 
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility) 
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if !defined(SWIFT_DEPRECATED_OBJC)
# if __has_feature(attribute_diagnose_if_objc)
#  define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
# else
#  define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
# endif
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction 
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if !defined(SWIFT_INDIRECT_RESULT)
# define SWIFT_INDIRECT_RESULT __attribute__((swift_indirect_result))
#endif
#if !defined(SWIFT_CONTEXT)
# define SWIFT_CONTEXT __attribute__((swift_context))
#endif
#if !defined(SWIFT_ERROR_RESULT)
# define SWIFT_ERROR_RESULT __attribute__((swift_error_result))
#endif
#if defined(__cplusplus)
# define SWIFT_NOEXCEPT noexcept
#else
# define SWIFT_NOEXCEPT 
#endif
#if !defined(SWIFT_C_INLINE_THUNK)
# if __has_attribute(always_inline)
# if __has_attribute(nodebug)
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline)) __attribute__((nodebug))
# else
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline))
# endif
# else
#  define SWIFT_C_INLINE_THUNK inline
# endif
#endif
#if defined(_WIN32)
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL __declspec(dllimport)
#endif
#else
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL 
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(objc_modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import AVFoundation;
@import CoreFoundation;
@import DKPhotoGallery;
@import Foundation;
@import ObjectiveC;
@import Photos;
@import UIKit;
#endif

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"
#pragma clang diagnostic ignored "-Wunsafe-buffer-usage"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="DKImagePickerController",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)

@interface AVAsset (SWIFT_EXTENSION(DKImagePickerController))
- (float)calculateFileSize SWIFT_WARN_UNUSED_RESULT;
@end

enum DKAssetType : NSInteger;
@class NSString;
@class CLLocation;
@class PHAsset;
/// An <code>DKAsset</code> object represents a photo or a video managed by the <code>DKImagePickerController</code>.
SWIFT_CLASS("_TtC23DKImagePickerController7DKAsset")
@interface DKAsset : NSObject
@property (nonatomic, readonly) enum DKAssetType type;
@property (nonatomic, copy) NSString * _Nonnull localIdentifier;
/// Returns location, if its contained in original asser
@property (nonatomic, readonly, strong) CLLocation * _Nullable location;
/// play time duration(seconds) of a video.
@property (nonatomic, readonly) double duration;
/// The width, in pixels, of the asset’s image or video data.
@property (nonatomic, readonly) NSInteger pixelWidth;
/// The height, in pixels, of the asset’s image or video data.
@property (nonatomic, readonly) NSInteger pixelHeight;
@property (nonatomic, readonly, strong) PHAsset * _Nullable originalAsset;
- (BOOL)isEqual:(id _Nullable)object SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class NSURL;
@interface DKAsset (SWIFT_EXTENSION(DKImagePickerController))
/// The exported file will be placed in this location.
/// All exported files can be automatically cleaned by the DKImageAssetDiskPurger when appropriate.
@property (nonatomic, copy) NSURL * _Nullable localTemporaryPath;
@property (nonatomic, copy) NSString * _Nullable fileName;
/// Indicates the file’s size in bytes.
@property (nonatomic) NSUInteger fileSize;
/// If you export an asset whose data is not on the local device, and you have enabled downloading with the isNetworkAccessAllowed property, the progress indicates the progress of the download. A value of 0.0 indicates that the download has just started, and a value of 1.0 indicates the download is complete.
@property (nonatomic) double progress;
/// Describes the error that occurred if the export has failed or been cancelled.
@property (nonatomic) NSError * _Nullable error;
@end

@class PHImageRequestOptions;
@class UIImage;
@class NSData;
@class PHVideoRequestOptions;
@interface DKAsset (SWIFT_EXTENSION(DKImagePickerController))
/// Fetch an image with the specific size.
- (void)fetchImageWith:(CGSize)size options:(PHImageRequestOptions * _Nullable)options contentMode:(PHImageContentMode)contentMode completeBlock:(void (^ _Nonnull)(UIImage * _Nullable, NSDictionary * _Nullable))completeBlock;
/// Fetch an image with the current screen size.
- (void)fetchFullScreenImageWithCompleteBlock:(void (^ _Nonnull)(UIImage * _Nullable, NSDictionary * _Nullable))completeBlock;
/// Fetch an image with the original size.
- (void)fetchOriginalImageWithOptions:(PHImageRequestOptions * _Nullable)options completeBlock:(void (^ _Nonnull)(UIImage * _Nullable, NSDictionary * _Nullable))completeBlock;
/// Fetch an image data with the original size.
- (void)fetchImageDataWithOptions:(PHImageRequestOptions * _Nullable)options compressionQuality:(CGFloat)compressionQuality completeBlock:(void (^ _Nonnull)(NSData * _Nullable, NSDictionary * _Nullable))completeBlock;
/// Fetch an AVAsset with a completeBlock and PHVideoRequestOptions.
- (void)fetchAVAssetWithOptions:(PHVideoRequestOptions * _Nullable)options completeBlock:(void (^ _Nonnull)(AVAsset * _Nullable, NSDictionary * _Nullable))completeBlock;
- (void)cancelRequests;
@end

/// A representation of a Photos asset grouping, such as a moment, user-created album, or smart album.
SWIFT_CLASS("_TtC23DKImagePickerController12DKAssetGroup")
@interface DKAssetGroup : NSObject
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class UIImageView;
SWIFT_PROTOCOL("_TtP23DKImagePickerController28DKAssetGroupCellItemProtocol_")
@protocol DKAssetGroupCellItemProtocol
@property (nonatomic, weak) DKAsset * _Null_unspecified asset;
@property (nonatomic) NSInteger selectedIndex;
@property (nonatomic, strong) UIImage * _Nullable thumbnailImage;
@property (nonatomic, readonly, strong) UIImageView * _Nonnull thumbnailImageView;
@end

@class DKImageGroupDataManager;
SWIFT_PROTOCOL("_TtP23DKImagePickerController20DKAssetGroupCellType_")
@protocol DKAssetGroupCellType
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly) CGFloat preferredHeight;)
+ (CGFloat)preferredHeight SWIFT_WARN_UNUSED_RESULT;
- (void)configureWith:(DKAssetGroup * _Nonnull)assetGroup tag:(NSInteger)tag dataManager:(DKImageGroupDataManager * _Nonnull)dataManager imageRequestOptions:(PHImageRequestOptions * _Nonnull)imageRequestOptions;
@end

@class NSCoder;
SWIFT_CLASS("_TtC23DKImagePickerController26DKAssetGroupDetailBaseCell")
@interface DKAssetGroupDetailBaseCell : UICollectionViewCell <DKAssetGroupCellItemProtocol>
@property (nonatomic, weak) DKAsset * _Nullable asset;
@property (nonatomic) NSInteger selectedIndex;
@property (nonatomic, strong) UIImage * _Nullable thumbnailImage;
@property (nonatomic, readonly, strong) UIImageView * _Nonnull thumbnailImageView;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
@property (nonatomic, getter=isHighlighted) BOOL highlighted;
@end

SWIFT_CLASS("_TtC23DKImagePickerController28DKAssetGroupDetailCameraCell")
@interface DKAssetGroupDetailCameraCell : DKAssetGroupDetailBaseCell
+ (NSString * _Nonnull)cellReuseIdentifier SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithFrame:(CGRect)frame SWIFT_UNAVAILABLE;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder SWIFT_UNAVAILABLE;
@end

@class DKImageCheckView;
SWIFT_CLASS("_TtC23DKImagePickerController27DKAssetGroupDetailImageCell")
@interface DKAssetGroupDetailImageCell : DKAssetGroupDetailBaseCell
+ (NSString * _Nonnull)cellReuseIdentifier SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithFrame:(CGRect)frame SWIFT_UNAVAILABLE;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder SWIFT_UNAVAILABLE;
@property (nonatomic, strong) UIImage * _Nullable thumbnailImage;
@property (nonatomic) NSInteger selectedIndex;
@property (nonatomic, readonly, strong) UIImageView * _Nonnull thumbnailImageView;
@property (nonatomic, readonly, strong) DKImageCheckView * _Nonnull checkView;
@property (nonatomic, getter=isSelected) BOOL selected;
@end

@class DKImagePickerController;
/// /////////////////////////////////////////////////////////////////////
SWIFT_PROTOCOL("_TtP23DKImagePickerController28DKImagePickerControllerAware_")
@protocol DKImagePickerControllerAware
@property (nonatomic, weak) DKImagePickerController * _Null_unspecified imagePickerController;
- (void)reload;
@end

@class UIGestureRecognizer;
@class UICollectionView;
@class NSIndexPath;
@class UIScrollView;
@class NSBundle;
/// /////////////////////////////////////////////////////////
SWIFT_CLASS("_TtC23DKImagePickerController20DKAssetGroupDetailVC")
@interface DKAssetGroupDetailVC : UIViewController <DKImagePickerControllerAware, UICollectionViewDataSource, UICollectionViewDelegate, UIGestureRecognizerDelegate>
@property (nonatomic, weak) DKImagePickerController * _Nullable imagePickerController;
- (void)viewDidLoad;
- (void)viewDidAppear:(BOOL)animated;
- (void)viewWillLayoutSubviews;
- (void)viewDidLayoutSubviews;
- (void)reload;
- (BOOL)gestureRecognizer:(UIGestureRecognizer * _Nonnull)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer * _Nonnull)otherGestureRecognizer SWIFT_WARN_UNUSED_RESULT;
- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer * _Nonnull)gestureRecognizer SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)collectionView:(UICollectionView * _Nonnull)collectionView numberOfItemsInSection:(NSInteger)section SWIFT_WARN_UNUSED_RESULT;
- (UICollectionViewCell * _Nonnull)collectionView:(UICollectionView * _Nonnull)collectionView cellForItemAtIndexPath:(NSIndexPath * _Nonnull)indexPath SWIFT_WARN_UNUSED_RESULT;
- (void)collectionView:(UICollectionView * _Nonnull)collectionView willDisplayCell:(UICollectionViewCell * _Nonnull)cell forItemAtIndexPath:(NSIndexPath * _Nonnull)indexPath;
- (void)collectionView:(UICollectionView * _Nonnull)collectionView didEndDisplayingCell:(UICollectionViewCell * _Nonnull)cell forItemAtIndexPath:(NSIndexPath * _Nonnull)indexPath;
- (BOOL)collectionView:(UICollectionView * _Nonnull)collectionView shouldSelectItemAtIndexPath:(NSIndexPath * _Nonnull)indexPath SWIFT_WARN_UNUSED_RESULT;
- (void)collectionView:(UICollectionView * _Nonnull)collectionView didSelectItemAtIndexPath:(NSIndexPath * _Nonnull)indexPath;
- (void)collectionView:(UICollectionView * _Nonnull)collectionView didDeselectItemAtIndexPath:(NSIndexPath * _Nonnull)indexPath;
- (void)scrollViewDidScroll:(UIScrollView * _Nonnull)scrollView;
- (nonnull instancetype)initWithNibName:(NSString * _Nullable)nibNameOrNil bundle:(NSBundle * _Nullable)nibBundleOrNil OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC23DKImagePickerController27DKAssetGroupDetailVideoCell")
@interface DKAssetGroupDetailVideoCell : DKAssetGroupDetailImageCell
+ (NSString * _Nonnull)cellReuseIdentifier SWIFT_WARN_UNUSED_RESULT;
- (void)layoutSubviews;
@property (nonatomic, weak) DKAsset * _Nullable asset;
@property (nonatomic, getter=isSelected) BOOL selected;
@end

SWIFT_CLASS("_TtC23DKImagePickerController22DKAssetGroupGridLayout")
@interface DKAssetGroupGridLayout : UICollectionViewFlowLayout
- (void)prepareLayout;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
@end

typedef SWIFT_ENUM(NSInteger, DKAssetType, closed) {
  DKAssetTypePhoto = 0,
  DKAssetTypeVideo = 1,
};

/// ///////////////////////////////////////////////////////////////////
SWIFT_CLASS("_TtC23DKImagePickerController18DKImageBaseManager")
@interface DKImageBaseManager : NSObject
- (void)addWithObserver:(id _Nonnull)object;
- (void)removeWithObserver:(id _Nonnull)object;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@class DKImageAssetExporterConfiguration;
SWIFT_CLASS("_TtC23DKImagePickerController20DKImageAssetExporter")
@interface DKImageAssetExporter : DKImageBaseManager
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) DKImageAssetExporter * _Nonnull sharedInstance;)
+ (DKImageAssetExporter * _Nonnull)sharedInstance SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithConfiguration:(DKImageAssetExporterConfiguration * _Nonnull)configuration OBJC_DESIGNATED_INITIALIZER;
/// This method starts an asynchronous export operation of a batch of asset.
- (int32_t)exportAssetsAsynchronouslyWithAssets:(NSArray<DKAsset *> * _Nonnull)assets completion:(void (^ _Nullable)(NSDictionary * _Nonnull))completion;
- (void)cancelWithRequestID:(int32_t)requestID;
- (void)cancelAll;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

enum DKImageExportPresent : NSInteger;
SWIFT_CLASS("_TtC23DKImagePickerController33DKImageAssetExporterConfiguration")
@interface DKImageAssetExporterConfiguration : NSObject <NSCopying>
@property (nonatomic) enum DKImageExportPresent imageExportPreset;
/// videoExportPreset can be used to specify the transcoding quality for videos (via a AVAssetExportPreset* string).
@property (nonatomic, copy) NSString * _Nonnull videoExportPreset;
@property (nonatomic) AVFileType _Nonnull avOutputFileType;
@property (nonatomic, copy) NSURL * _Nonnull exportDirectory;
@property (nonatomic) CGFloat compressionQuality;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
@end

/// //////////////////////////////////////////////////////////////////////////
typedef SWIFT_ENUM(NSInteger, DKImageAssetExporterError, closed) {
  DKImageAssetExporterErrorCancelled = 0,
  DKImageAssetExporterErrorExportFailed = 1,
};

SWIFT_PROTOCOL("_TtP23DKImagePickerController28DKImageAssetExporterObserver_")
@protocol DKImageAssetExporterObserver
@optional
- (void)exporterWillBeginExportingWithExporter:(DKImageAssetExporter * _Nonnull)exporter asset:(DKAsset * _Nonnull)asset;
/// The progress can be obtained from the DKAsset.
- (void)exporterDidUpdateProgressWithExporter:(DKImageAssetExporter * _Nonnull)exporter asset:(DKAsset * _Nonnull)asset;
/// When the asset’s error is not nil, it indicates that an error occurred while exporting.
- (void)exporterDidEndExportingWithExporter:(DKImageAssetExporter * _Nonnull)exporter asset:(DKAsset * _Nonnull)asset;
@end

/// This is the base class for all extensions.
SWIFT_CLASS("_TtC23DKImagePickerController20DKImageBaseExtension")
@interface DKImageBaseExtension : NSObject
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

typedef SWIFT_ENUM(NSInteger, DKImageExportPresent, closed) {
  DKImageExportPresentCompatible = 0,
  DKImageExportPresentCurrent = 1,
};

enum DKImageExtensionType : NSInteger;
/// The class handles the loading of extensions.
SWIFT_CLASS("_TtC23DKImagePickerController26DKImageExtensionController")
@interface DKImageExtensionController : NSObject
- (void)performWithExtensionType:(enum DKImageExtensionType)extensionType with:(NSDictionary * _Nonnull)extraInfo;
- (void)finishWithExtensionType:(enum DKImageExtensionType)extensionType;
- (void)enableWithExtensionType:(enum DKImageExtensionType)extensionType;
- (void)disableWithExtensionType:(enum DKImageExtensionType)extensionType;
- (BOOL)isExtensionTypeAvailable:(enum DKImageExtensionType)extensionType SWIFT_WARN_UNUSED_RESULT;
/// Registers an extension for the specified type.
+ (void)registerExtensionWithExtensionClass:(SWIFT_METATYPE(DKImageBaseExtension) _Nonnull)extensionClass for:(enum DKImageExtensionType)type;
+ (void)unregisterExtensionFor:(enum DKImageExtensionType)type;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class DKPhotoGallery;
@class UIButton;
SWIFT_CLASS("_TtC23DKImagePickerController23DKImageExtensionGallery")
@interface DKImageExtensionGallery : DKImageBaseExtension <DKPhotoGalleryDelegate>
- (void)photoGallery:(DKPhotoGallery * _Nonnull)gallery didShow:(NSInteger)index;
- (void)selectAssetFromGalleryWithButton:(UIButton * _Nonnull)button;
- (void)dismissGallery;
@end

/// A placeholder object used to represent no any action for the certain ExtensionType.
SWIFT_CLASS("_TtC23DKImagePickerController20DKImageExtensionNone")
@interface DKImageExtensionNone : DKImageBaseExtension
@end

/// /////////////////////////////////////////////////////////////////////
typedef SWIFT_ENUM(NSInteger, DKImageExtensionType, closed) {
  DKImageExtensionTypeGallery = 0,
  DKImageExtensionTypeCamera = 1,
  DKImageExtensionTypeInlineCamera = 2,
  DKImageExtensionTypePhotoEditor = 3,
};

@class PHChange;
/// //////////////////////////////////////////////////////////////////////////////////
SWIFT_CLASS("_TtC23DKImagePickerController23DKImageGroupDataManager")
@interface DKImageGroupDataManager : DKImageBaseManager <PHPhotoLibraryChangeObserver>
- (void)photoLibraryDidChange:(PHChange * _Nonnull)changeInstance;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class PHFetchOptions;
SWIFT_CLASS("_TtC23DKImagePickerController36DKImageGroupDataManagerConfiguration")
@interface DKImageGroupDataManagerConfiguration : NSObject <NSCopying>
/// Options that specify a filter predicate and sort order for the fetched assets, or nil to use default options.
@property (nonatomic, strong) PHFetchOptions * _Nullable assetFetchOptions;
/// Limits the maximum number of objects displayed on the UI, a value of 0 means no limit.  Defaults to 0.
@property (nonatomic) NSInteger fetchLimit;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
@end

/// /////////////////////////////////////////////////////////////////////
SWIFT_CLASS("_TtC23DKImagePickerController24DKUINavigationController")
@interface DKUINavigationController : UINavigationController
- (nonnull instancetype)initWithNavigationBarClass:(Class _Nullable)navigationBarClass toolbarClass:(Class _Nullable)toolbarClass OBJC_DESIGNATED_INITIALIZER SWIFT_AVAILABILITY(ios,introduced=5.0);
- (nonnull instancetype)initWithRootViewController:(UIViewController * _Nonnull)rootViewController OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithNibName:(NSString * _Nullable)nibNameOrNil bundle:(NSBundle * _Nullable)nibBundleOrNil OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
@end

@class DKImagePickerControllerBaseUIDelegate;
enum DKImagePickerControllerAssetType : NSInteger;
enum DKImagePickerControllerSourceType : NSInteger;
@class DKPermissionViewColors;
enum DKImagePickerControllerExportStatus : NSInteger;
@class UIPresentationController;
SWIFT_CLASS("_TtC23DKImagePickerController23DKImagePickerController")
@interface DKImagePickerController : DKUINavigationController <UIAdaptivePresentationControllerDelegate>
/// Use UIDelegate to Customize the picker UI.
@property (nonatomic, strong) DKImagePickerControllerBaseUIDelegate * _Null_unspecified UIDelegate;
/// false to prevent dismissal of the picker when the presentation controller will dismiss in response to user action.
@property (nonatomic, readonly) BOOL shouldDismissViaUserAction SWIFT_AVAILABILITY(ios,introduced=13.0);
/// Forces deselect of previous selected image. allowSwipeToSelect will be ignored.
@property (nonatomic) BOOL singleSelect;
/// Auto close picker on single select
@property (nonatomic) BOOL autoCloseOnSingleSelect;
/// The maximum count of assets which the user will be able to select, a value of 0 means no limit.
@property (nonatomic) NSInteger maxSelectableCount;
/// Allow swipe to select images.
@property (nonatomic) BOOL allowSwipeToSelect;
/// Allow select all
@property (nonatomic) BOOL allowSelectAll;
/// A Bool value indicating whether the inline mode is enabled.
@property (nonatomic, getter=inline, setter=setInline:) BOOL inline_;
/// The type of picker interface to be displayed by the controller.
@property (nonatomic) enum DKImagePickerControllerAssetType assetType;
/// If sourceType is Camera will cause the assetType & maxSelectableCount & allowMultipleTypes to be ignored.
@property (nonatomic) enum DKImagePickerControllerSourceType sourceType;
/// A Bool value indicating whether to allow the picker to select photos and videos at the same time.
@property (nonatomic) BOOL allowMultipleTypes;
/// A Bool value indicating whether to allow the picker auto-rotate the screen.
@property (nonatomic) BOOL allowsLandscape;
/// Set the showsEmptyAlbums to specify whether or not the empty albums is shown in the picker.
@property (nonatomic) BOOL showsEmptyAlbums;
/// A Bool value indicating whether to allow the picker shows the cancel button.
@property (nonatomic) BOOL showsCancelButton;
/// Limits the maximum number of objects displayed on the UI, a value of 0 means no limit.  Defaults to 0.
@property (nonatomic) NSInteger fetchLimit;
/// The block is executed when the user presses the cancel button.
@property (nonatomic, copy) void (^ _Nullable didCancel)(void);
/// The block is executed when the user presses the select button.
@property (nonatomic, copy) void (^ _Nullable didSelectAssets)(NSArray<DKAsset *> * _Nonnull);
/// The block is executed when the number of the selected assets is changed.
@property (nonatomic, copy) void (^ _Nullable selectedChanged)(void);
/// Colors applied to the permission view when access needs to be granted by the user
@property (nonatomic, strong) DKPermissionViewColors * _Nonnull permissionViewColors;
/// A Bool value indicating whether to allow the picker to auto-export the selected assets to the specified directory when done is called.
/// picker will creating a default exporter if exportsWhenCompleted is true and the exporter is nil.
@property (nonatomic) BOOL exportsWhenCompleted;
@property (nonatomic, strong) DKImageAssetExporter * _Nullable exporter;
/// Select view request thumbnail size
@property (nonatomic) CGSize thumbnailSize;
/// Indicates the status of the exporter.
@property (nonatomic, readonly) enum DKImagePickerControllerExportStatus exportStatus;
/// The block is executed when the exportStatus is changed.
@property (nonatomic, copy) void (^ _Nullable exportStatusChanged)(enum DKImagePickerControllerExportStatus);
/// The object that acts as the data source of the picker.
@property (nonatomic, readonly, strong) DKImageGroupDataManager * _Nonnull groupDataManager;
- (void)viewWillAppear:(BOOL)animated;
- (UIViewController <DKImagePickerControllerAware> * _Nonnull)makeRootVC SWIFT_WARN_UNUSED_RESULT;
- (void)presentCamera;
- (void)presentViewController:(UIViewController * _Nonnull)viewControllerToPresent animated:(BOOL)flag completion:(void (^ _Nullable)(void))completion;
- (void)dismissViewControllerAnimated:(BOOL)flag completion:(void (^ _Nullable)(void))completion;
- (void)dismissCamera;
- (void)dismiss;
- (void)done;
/// Reload this picker with a new DKImageGroupDataManager.
- (void)reloadWith:(DKImageGroupDataManager * _Nonnull)dataManager;
- (void)saveImage:(UIImage * _Nonnull)image :(NSDictionary * _Nullable)metadata :(void (^ _Nonnull)(DKAsset * _Nonnull))completeBlock;
- (void)saveImageToAlbum:(UIImage * _Nonnull)image :(void (^ _Nonnull)(DKAsset * _Nonnull))completeBlock;
- (void)saveImageDataToAlbumForiOS9:(NSData * _Nonnull)imageDataWithMetadata :(void (^ _Nonnull)(DKAsset * _Nonnull))completeBlock;
- (NSData * _Nullable)writeMetadata:(NSDictionary * _Nonnull)metadata into:(NSData * _Nonnull)imageData SWIFT_WARN_UNUSED_RESULT;
- (void)selectWithAsset:(DKAsset * _Nonnull)asset;
- (void)selectWithAssets:(NSArray<DKAsset *> * _Nonnull)assets;
- (void)handleSelectAll;
- (void)deselectWithAsset:(DKAsset * _Nonnull)asset;
- (void)removeSelectionWithAsset:(DKAsset * _Nonnull)asset;
- (void)deselectAll;
- (void)setSelectedAssetsWithAssets:(NSArray<DKAsset *> * _Nonnull)assets;
- (BOOL)containsWithAsset:(DKAsset * _Nonnull)asset SWIFT_WARN_UNUSED_RESULT;
- (void)scrollTo:(NSIndexPath * _Nonnull)indexPath animated:(BOOL)animated;
- (void)scrollToLastTappedIndexPathWithAnimated:(BOOL)animated;
@property (nonatomic, readonly, copy) NSArray<DKAsset *> * _Nonnull selectedAssets;
- (BOOL)canSelectWithAsset:(DKAsset * _Nonnull)asset showAlert:(BOOL)showAlert SWIFT_WARN_UNUSED_RESULT;
@property (nonatomic, readonly) BOOL shouldAutorotate;
@property (nonatomic, readonly) UIInterfaceOrientationMask supportedInterfaceOrientations;
- (BOOL)presentationControllerShouldDismiss:(UIPresentationController * _Nonnull)presentationController SWIFT_WARN_UNUSED_RESULT SWIFT_AVAILABILITY(ios,introduced=13.0);
- (void)presentationControllerDidDismiss:(UIPresentationController * _Nonnull)presentationController;
- (nonnull instancetype)initWithNavigationBarClass:(Class _Nullable)navigationBarClass toolbarClass:(Class _Nullable)toolbarClass OBJC_DESIGNATED_INITIALIZER SWIFT_AVAILABILITY(ios,introduced=5.0);
- (nonnull instancetype)initWithRootViewController:(UIViewController * _Nonnull)rootViewController OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithNibName:(NSString * _Nullable)nibNameOrNil bundle:(NSBundle * _Nullable)nibBundleOrNil OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
@end

/// <ul>
///   <li>
///     AllPhotos: Get all photos assets in the assets group.
///   </li>
///   <li>
///     AllVideos: Get all video assets in the assets group.
///   </li>
///   <li>
///     AllAssets: Get all assets in the group.
///   </li>
/// </ul>
typedef SWIFT_ENUM(NSInteger, DKImagePickerControllerAssetType, closed) {
  DKImagePickerControllerAssetTypeAllPhotos = 0,
  DKImagePickerControllerAssetTypeAllVideos = 1,
  DKImagePickerControllerAssetTypeAllAssets = 2,
};

@class UICollectionViewLayout;
@class UIView;
@class UIColor;
enum DKImagePickerGroupListPresentationStyle : NSInteger;
@class UITableViewController;
SWIFT_PROTOCOL("_TtP23DKImagePickerController33DKImagePickerControllerUIDelegate_")
@protocol DKImagePickerControllerUIDelegate
/// The picker calls -prepareLayout once at its first layout as the first message to the UIDelegate instance.
- (void)prepareLayout:(DKImagePickerController * _Nonnull)imagePickerController vc:(UIViewController * _Nonnull)vc;
/// The layout is to provide information about the position and visual state of items in the collection view.
- (SWIFT_METATYPE(UICollectionViewLayout) _Nonnull)layoutForImagePickerController:(DKImagePickerController * _Nonnull)imagePickerController SWIFT_WARN_UNUSED_RESULT;
/// Called when the user needs to show the cancel button.
- (void)imagePickerController:(DKImagePickerController * _Nonnull)imagePickerController showsCancelButtonForVC:(UIViewController * _Nonnull)vc;
/// Called when the user needs to hide the cancel button.
- (void)imagePickerController:(DKImagePickerController * _Nonnull)imagePickerController hidesCancelButtonForVC:(UIViewController * _Nonnull)vc;
/// Called after the user changes the selection.
- (void)imagePickerController:(DKImagePickerController * _Nonnull)imagePickerController didSelectAssets:(NSArray<DKAsset *> * _Nonnull)didSelectAssets;
/// Called after the user changes the selection.
- (void)imagePickerController:(DKImagePickerController * _Nonnull)imagePickerController didDeselectAssets:(NSArray<DKAsset *> * _Nonnull)didDeselectAssets;
/// Called when the count of the selectedAssets did reach <code>maxSelectableCount</code>.
- (void)imagePickerControllerDidReachMaxLimit:(DKImagePickerController * _Nonnull)imagePickerController;
/// Accessory view below content. default is nil.
- (UIView * _Nullable)imagePickerControllerFooterView:(DKImagePickerController * _Nonnull)imagePickerController SWIFT_WARN_UNUSED_RESULT;
/// Accessory view above content. default is nil.
- (UIView * _Nullable)imagePickerControllerHeaderView:(DKImagePickerController * _Nonnull)imagePickerController SWIFT_WARN_UNUSED_RESULT;
/// Set the color of the background of the collection view.
- (UIColor * _Nonnull)imagePickerControllerCollectionViewBackgroundColor SWIFT_WARN_UNUSED_RESULT;
- (SWIFT_METATYPE(DKAssetGroupDetailBaseCell) _Nonnull)imagePickerControllerCollectionImageCell SWIFT_WARN_UNUSED_RESULT;
- (SWIFT_METATYPE(DKAssetGroupDetailBaseCell) _Nonnull)imagePickerControllerCollectionCameraCell SWIFT_WARN_UNUSED_RESULT;
- (SWIFT_METATYPE(DKAssetGroupDetailBaseCell) _Nonnull)imagePickerControllerCollectionVideoCell SWIFT_WARN_UNUSED_RESULT;
- (Class <DKAssetGroupCellType> _Nonnull)imagePickerControllerGroupCell SWIFT_WARN_UNUSED_RESULT;
/// Provide a custom button to be used as the titleView for imagePickerController’s navigationItem.
- (UIButton * _Nonnull)imagePickerControllerSelectGroupButton:(DKImagePickerController * _Nonnull)imagePickerController selectedGroup:(DKAssetGroup * _Nonnull)selectedGroup SWIFT_WARN_UNUSED_RESULT;
/// Specify how asset group list view controller should be presented, default is .popover
- (enum DKImagePickerGroupListPresentationStyle)imagePickerControllerGroupListPresentationStyle SWIFT_WARN_UNUSED_RESULT;
/// Use to customize the group list table view controller before presentation.
- (void)imagePickerControllerPrepareGroupListViewController:(UITableViewController * _Nonnull)listViewController;
@end

/// /////////////////////////////////////////////////////////////////////////////////////////////////
SWIFT_CLASS("_TtC23DKImagePickerController37DKImagePickerControllerBaseUIDelegate")
@interface DKImagePickerControllerBaseUIDelegate : NSObject <DKImagePickerControllerUIDelegate>
- (void)prepareLayout:(DKImagePickerController * _Nonnull)imagePickerController vc:(UIViewController * _Nonnull)vc;
- (SWIFT_METATYPE(UICollectionViewLayout) _Nonnull)layoutForImagePickerController:(DKImagePickerController * _Nonnull)imagePickerController SWIFT_WARN_UNUSED_RESULT;
- (void)imagePickerController:(DKImagePickerController * _Nonnull)imagePickerController showsCancelButtonForVC:(UIViewController * _Nonnull)vc;
- (void)imagePickerController:(DKImagePickerController * _Nonnull)imagePickerController hidesCancelButtonForVC:(UIViewController * _Nonnull)vc;
- (void)imagePickerController:(DKImagePickerController * _Nonnull)imagePickerController didSelectAssets:(NSArray<DKAsset *> * _Nonnull)didSelectAssets;
- (void)imagePickerController:(DKImagePickerController * _Nonnull)imagePickerController didDeselectAssets:(NSArray<DKAsset *> * _Nonnull)didDeselectAssets;
- (void)imagePickerControllerDidReachMaxLimit:(DKImagePickerController * _Nonnull)imagePickerController;
- (UIView * _Nullable)imagePickerControllerFooterView:(DKImagePickerController * _Nonnull)imagePickerController SWIFT_WARN_UNUSED_RESULT;
- (UIView * _Nullable)imagePickerControllerHeaderView:(DKImagePickerController * _Nonnull)imagePickerController SWIFT_WARN_UNUSED_RESULT;
- (UIColor * _Nonnull)imagePickerControllerCollectionViewBackgroundColor SWIFT_WARN_UNUSED_RESULT;
- (SWIFT_METATYPE(DKAssetGroupDetailBaseCell) _Nonnull)imagePickerControllerCollectionImageCell SWIFT_WARN_UNUSED_RESULT;
- (SWIFT_METATYPE(DKAssetGroupDetailBaseCell) _Nonnull)imagePickerControllerCollectionCameraCell SWIFT_WARN_UNUSED_RESULT;
- (SWIFT_METATYPE(DKAssetGroupDetailBaseCell) _Nonnull)imagePickerControllerCollectionVideoCell SWIFT_WARN_UNUSED_RESULT;
- (Class <DKAssetGroupCellType> _Nonnull)imagePickerControllerGroupCell SWIFT_WARN_UNUSED_RESULT;
- (UIButton * _Nonnull)imagePickerControllerSelectGroupButton:(DKImagePickerController * _Nonnull)imagePickerController selectedGroup:(DKAssetGroup * _Nonnull)selectedGroup SWIFT_WARN_UNUSED_RESULT;
- (enum DKImagePickerGroupListPresentationStyle)imagePickerControllerGroupListPresentationStyle SWIFT_WARN_UNUSED_RESULT;
- (void)imagePickerControllerPrepareGroupListViewController:(UITableViewController * _Nonnull)listViewController;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

typedef SWIFT_ENUM(NSInteger, DKImagePickerControllerExportStatus, closed) {
  DKImagePickerControllerExportStatusNone = 0,
  DKImagePickerControllerExportStatusExporting = 1,
};

SWIFT_CLASS("_TtC23DKImagePickerController31DKImagePickerControllerResource")
@interface DKImagePickerControllerResource : NSObject
/// Add a hook for custom localization.
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, copy) NSString * _Nullable (^ _Nullable customLocalizationBlock)(NSString * _Nonnull);)
+ (NSString * _Nullable (^ _Nullable)(NSString * _Nonnull))customLocalizationBlock SWIFT_WARN_UNUSED_RESULT;
+ (void)setCustomLocalizationBlock:(NSString * _Nullable (^ _Nullable)(NSString * _Nonnull))value;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, copy) UIImage * _Nullable (^ _Nullable customImageBlock)(NSString * _Nonnull);)
+ (UIImage * _Nullable (^ _Nullable)(NSString * _Nonnull))customImageBlock SWIFT_WARN_UNUSED_RESULT;
+ (void)setCustomImageBlock:(UIImage * _Nullable (^ _Nullable)(NSString * _Nonnull))value;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

typedef SWIFT_ENUM(NSInteger, DKImagePickerControllerSourceType, closed) {
  DKImagePickerControllerSourceTypeCamera = 0,
  DKImagePickerControllerSourceTypePhoto = 1,
  DKImagePickerControllerSourceTypeBoth = 2,
};

typedef SWIFT_ENUM(NSInteger, DKImagePickerGroupListPresentationStyle, closed) {
  DKImagePickerGroupListPresentationStylePopover = 0,
  DKImagePickerGroupListPresentationStylePresented = 1,
};

SWIFT_CLASS("_TtC23DKImagePickerController16DKPermissionView")
@interface DKPermissionView : UIView
- (void)didMoveToWindow;
- (void)gotoSettings;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC23DKImagePickerController22DKPermissionViewColors")
@interface DKPermissionViewColors : NSObject
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

SWIFT_CLASS("_TtC23DKImagePickerController23DKPopoverViewController")
@interface DKPopoverViewController : UIViewController
+ (void)popoverViewController:(UIViewController * _Nonnull)viewController fromView:(UIView * _Nonnull)fromView arrowColor:(UIColor * _Nonnull)arrowColor;
+ (void)dismissPopoverViewController;
@property (nonatomic, strong) UIColor * _Nonnull arrowColor;
- (void)loadView;
- (void)viewDidLoad;
- (void)didRotateFromInterfaceOrientation:(UIInterfaceOrientation)fromInterfaceOrientation SWIFT_AVAILABILITY(ios,deprecated=8.0);
- (nonnull instancetype)initWithNibName:(NSString * _Nullable)nibNameOrNil bundle:(NSBundle * _Nullable)nibBundleOrNil OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
@end

#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#if defined(__cplusplus)
#endif
#pragma clang diagnostic pop
#endif

#elif defined(__x86_64__) && __x86_64__
// Generated by Apple Swift version 6.1 effective-5.10 (swiftlang-6.1.0.110.21 clang-1700.0.13.3)
#ifndef DKIMAGEPICKERCONTROLLER_SWIFT_H
#define DKIMAGEPICKERCONTROLLER_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#include <cstring>
#include <stdlib.h>
#include <new>
#include <type_traits>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <string.h>
#endif
#if defined(__cplusplus)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wnon-modular-include-in-framework-module"
#if defined(__arm64e__) && __has_include(<ptrauth.h>)
# include <ptrauth.h>
#else
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wreserved-macro-identifier"
# ifndef __ptrauth_swift_value_witness_function_pointer
#  define __ptrauth_swift_value_witness_function_pointer(x)
# endif
# ifndef __ptrauth_swift_class_method_pointer
#  define __ptrauth_swift_class_method_pointer(x)
# endif
#pragma clang diagnostic pop
#endif
#pragma clang diagnostic pop
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef unsigned char char8_t;
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...) 
# endif
#endif
#if !defined(SWIFT_RUNTIME_NAME)
# if __has_attribute(objc_runtime_name)
#  define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
# else
#  define SWIFT_RUNTIME_NAME(X) 
# endif
#endif
#if !defined(SWIFT_COMPILE_NAME)
# if __has_attribute(swift_name)
#  define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
# else
#  define SWIFT_COMPILE_NAME(X) 
# endif
#endif
#if !defined(SWIFT_METHOD_FAMILY)
# if __has_attribute(objc_method_family)
#  define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
# else
#  define SWIFT_METHOD_FAMILY(X) 
# endif
#endif
#if !defined(SWIFT_NOESCAPE)
# if __has_attribute(noescape)
#  define SWIFT_NOESCAPE __attribute__((noescape))
# else
#  define SWIFT_NOESCAPE 
# endif
#endif
#if !defined(SWIFT_RELEASES_ARGUMENT)
# if __has_attribute(ns_consumed)
#  define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
# else
#  define SWIFT_RELEASES_ARGUMENT 
# endif
#endif
#if !defined(SWIFT_WARN_UNUSED_RESULT)
# if __has_attribute(warn_unused_result)
#  define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
# else
#  define SWIFT_WARN_UNUSED_RESULT 
# endif
#endif
#if !defined(SWIFT_NORETURN)
# if __has_attribute(noreturn)
#  define SWIFT_NORETURN __attribute__((noreturn))
# else
#  define SWIFT_NORETURN 
# endif
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA 
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA 
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA 
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif
#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif
#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER 
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility) 
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if !defined(SWIFT_DEPRECATED_OBJC)
# if __has_feature(attribute_diagnose_if_objc)
#  define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
# else
#  define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
# endif
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction 
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if !defined(SWIFT_INDIRECT_RESULT)
# define SWIFT_INDIRECT_RESULT __attribute__((swift_indirect_result))
#endif
#if !defined(SWIFT_CONTEXT)
# define SWIFT_CONTEXT __attribute__((swift_context))
#endif
#if !defined(SWIFT_ERROR_RESULT)
# define SWIFT_ERROR_RESULT __attribute__((swift_error_result))
#endif
#if defined(__cplusplus)
# define SWIFT_NOEXCEPT noexcept
#else
# define SWIFT_NOEXCEPT 
#endif
#if !defined(SWIFT_C_INLINE_THUNK)
# if __has_attribute(always_inline)
# if __has_attribute(nodebug)
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline)) __attribute__((nodebug))
# else
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline))
# endif
# else
#  define SWIFT_C_INLINE_THUNK inline
# endif
#endif
#if defined(_WIN32)
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL __declspec(dllimport)
#endif
#else
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL 
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(objc_modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import AVFoundation;
@import CoreFoundation;
@import DKPhotoGallery;
@import Foundation;
@import ObjectiveC;
@import Photos;
@import UIKit;
#endif

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"
#pragma clang diagnostic ignored "-Wunsafe-buffer-usage"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="DKImagePickerController",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)

@interface AVAsset (SWIFT_EXTENSION(DKImagePickerController))
- (float)calculateFileSize SWIFT_WARN_UNUSED_RESULT;
@end

enum DKAssetType : NSInteger;
@class NSString;
@class CLLocation;
@class PHAsset;
/// An <code>DKAsset</code> object represents a photo or a video managed by the <code>DKImagePickerController</code>.
SWIFT_CLASS("_TtC23DKImagePickerController7DKAsset")
@interface DKAsset : NSObject
@property (nonatomic, readonly) enum DKAssetType type;
@property (nonatomic, copy) NSString * _Nonnull localIdentifier;
/// Returns location, if its contained in original asser
@property (nonatomic, readonly, strong) CLLocation * _Nullable location;
/// play time duration(seconds) of a video.
@property (nonatomic, readonly) double duration;
/// The width, in pixels, of the asset’s image or video data.
@property (nonatomic, readonly) NSInteger pixelWidth;
/// The height, in pixels, of the asset’s image or video data.
@property (nonatomic, readonly) NSInteger pixelHeight;
@property (nonatomic, readonly, strong) PHAsset * _Nullable originalAsset;
- (BOOL)isEqual:(id _Nullable)object SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class NSURL;
@interface DKAsset (SWIFT_EXTENSION(DKImagePickerController))
/// The exported file will be placed in this location.
/// All exported files can be automatically cleaned by the DKImageAssetDiskPurger when appropriate.
@property (nonatomic, copy) NSURL * _Nullable localTemporaryPath;
@property (nonatomic, copy) NSString * _Nullable fileName;
/// Indicates the file’s size in bytes.
@property (nonatomic) NSUInteger fileSize;
/// If you export an asset whose data is not on the local device, and you have enabled downloading with the isNetworkAccessAllowed property, the progress indicates the progress of the download. A value of 0.0 indicates that the download has just started, and a value of 1.0 indicates the download is complete.
@property (nonatomic) double progress;
/// Describes the error that occurred if the export has failed or been cancelled.
@property (nonatomic) NSError * _Nullable error;
@end

@class PHImageRequestOptions;
@class UIImage;
@class NSData;
@class PHVideoRequestOptions;
@interface DKAsset (SWIFT_EXTENSION(DKImagePickerController))
/// Fetch an image with the specific size.
- (void)fetchImageWith:(CGSize)size options:(PHImageRequestOptions * _Nullable)options contentMode:(PHImageContentMode)contentMode completeBlock:(void (^ _Nonnull)(UIImage * _Nullable, NSDictionary * _Nullable))completeBlock;
/// Fetch an image with the current screen size.
- (void)fetchFullScreenImageWithCompleteBlock:(void (^ _Nonnull)(UIImage * _Nullable, NSDictionary * _Nullable))completeBlock;
/// Fetch an image with the original size.
- (void)fetchOriginalImageWithOptions:(PHImageRequestOptions * _Nullable)options completeBlock:(void (^ _Nonnull)(UIImage * _Nullable, NSDictionary * _Nullable))completeBlock;
/// Fetch an image data with the original size.
- (void)fetchImageDataWithOptions:(PHImageRequestOptions * _Nullable)options compressionQuality:(CGFloat)compressionQuality completeBlock:(void (^ _Nonnull)(NSData * _Nullable, NSDictionary * _Nullable))completeBlock;
/// Fetch an AVAsset with a completeBlock and PHVideoRequestOptions.
- (void)fetchAVAssetWithOptions:(PHVideoRequestOptions * _Nullable)options completeBlock:(void (^ _Nonnull)(AVAsset * _Nullable, NSDictionary * _Nullable))completeBlock;
- (void)cancelRequests;
@end

/// A representation of a Photos asset grouping, such as a moment, user-created album, or smart album.
SWIFT_CLASS("_TtC23DKImagePickerController12DKAssetGroup")
@interface DKAssetGroup : NSObject
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class UIImageView;
SWIFT_PROTOCOL("_TtP23DKImagePickerController28DKAssetGroupCellItemProtocol_")
@protocol DKAssetGroupCellItemProtocol
@property (nonatomic, weak) DKAsset * _Null_unspecified asset;
@property (nonatomic) NSInteger selectedIndex;
@property (nonatomic, strong) UIImage * _Nullable thumbnailImage;
@property (nonatomic, readonly, strong) UIImageView * _Nonnull thumbnailImageView;
@end

@class DKImageGroupDataManager;
SWIFT_PROTOCOL("_TtP23DKImagePickerController20DKAssetGroupCellType_")
@protocol DKAssetGroupCellType
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly) CGFloat preferredHeight;)
+ (CGFloat)preferredHeight SWIFT_WARN_UNUSED_RESULT;
- (void)configureWith:(DKAssetGroup * _Nonnull)assetGroup tag:(NSInteger)tag dataManager:(DKImageGroupDataManager * _Nonnull)dataManager imageRequestOptions:(PHImageRequestOptions * _Nonnull)imageRequestOptions;
@end

@class NSCoder;
SWIFT_CLASS("_TtC23DKImagePickerController26DKAssetGroupDetailBaseCell")
@interface DKAssetGroupDetailBaseCell : UICollectionViewCell <DKAssetGroupCellItemProtocol>
@property (nonatomic, weak) DKAsset * _Nullable asset;
@property (nonatomic) NSInteger selectedIndex;
@property (nonatomic, strong) UIImage * _Nullable thumbnailImage;
@property (nonatomic, readonly, strong) UIImageView * _Nonnull thumbnailImageView;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
@property (nonatomic, getter=isHighlighted) BOOL highlighted;
@end

SWIFT_CLASS("_TtC23DKImagePickerController28DKAssetGroupDetailCameraCell")
@interface DKAssetGroupDetailCameraCell : DKAssetGroupDetailBaseCell
+ (NSString * _Nonnull)cellReuseIdentifier SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithFrame:(CGRect)frame SWIFT_UNAVAILABLE;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder SWIFT_UNAVAILABLE;
@end

@class DKImageCheckView;
SWIFT_CLASS("_TtC23DKImagePickerController27DKAssetGroupDetailImageCell")
@interface DKAssetGroupDetailImageCell : DKAssetGroupDetailBaseCell
+ (NSString * _Nonnull)cellReuseIdentifier SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithFrame:(CGRect)frame SWIFT_UNAVAILABLE;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder SWIFT_UNAVAILABLE;
@property (nonatomic, strong) UIImage * _Nullable thumbnailImage;
@property (nonatomic) NSInteger selectedIndex;
@property (nonatomic, readonly, strong) UIImageView * _Nonnull thumbnailImageView;
@property (nonatomic, readonly, strong) DKImageCheckView * _Nonnull checkView;
@property (nonatomic, getter=isSelected) BOOL selected;
@end

@class DKImagePickerController;
/// /////////////////////////////////////////////////////////////////////
SWIFT_PROTOCOL("_TtP23DKImagePickerController28DKImagePickerControllerAware_")
@protocol DKImagePickerControllerAware
@property (nonatomic, weak) DKImagePickerController * _Null_unspecified imagePickerController;
- (void)reload;
@end

@class UIGestureRecognizer;
@class UICollectionView;
@class NSIndexPath;
@class UIScrollView;
@class NSBundle;
/// /////////////////////////////////////////////////////////
SWIFT_CLASS("_TtC23DKImagePickerController20DKAssetGroupDetailVC")
@interface DKAssetGroupDetailVC : UIViewController <DKImagePickerControllerAware, UICollectionViewDataSource, UICollectionViewDelegate, UIGestureRecognizerDelegate>
@property (nonatomic, weak) DKImagePickerController * _Nullable imagePickerController;
- (void)viewDidLoad;
- (void)viewDidAppear:(BOOL)animated;
- (void)viewWillLayoutSubviews;
- (void)viewDidLayoutSubviews;
- (void)reload;
- (BOOL)gestureRecognizer:(UIGestureRecognizer * _Nonnull)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer * _Nonnull)otherGestureRecognizer SWIFT_WARN_UNUSED_RESULT;
- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer * _Nonnull)gestureRecognizer SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)collectionView:(UICollectionView * _Nonnull)collectionView numberOfItemsInSection:(NSInteger)section SWIFT_WARN_UNUSED_RESULT;
- (UICollectionViewCell * _Nonnull)collectionView:(UICollectionView * _Nonnull)collectionView cellForItemAtIndexPath:(NSIndexPath * _Nonnull)indexPath SWIFT_WARN_UNUSED_RESULT;
- (void)collectionView:(UICollectionView * _Nonnull)collectionView willDisplayCell:(UICollectionViewCell * _Nonnull)cell forItemAtIndexPath:(NSIndexPath * _Nonnull)indexPath;
- (void)collectionView:(UICollectionView * _Nonnull)collectionView didEndDisplayingCell:(UICollectionViewCell * _Nonnull)cell forItemAtIndexPath:(NSIndexPath * _Nonnull)indexPath;
- (BOOL)collectionView:(UICollectionView * _Nonnull)collectionView shouldSelectItemAtIndexPath:(NSIndexPath * _Nonnull)indexPath SWIFT_WARN_UNUSED_RESULT;
- (void)collectionView:(UICollectionView * _Nonnull)collectionView didSelectItemAtIndexPath:(NSIndexPath * _Nonnull)indexPath;
- (void)collectionView:(UICollectionView * _Nonnull)collectionView didDeselectItemAtIndexPath:(NSIndexPath * _Nonnull)indexPath;
- (void)scrollViewDidScroll:(UIScrollView * _Nonnull)scrollView;
- (nonnull instancetype)initWithNibName:(NSString * _Nullable)nibNameOrNil bundle:(NSBundle * _Nullable)nibBundleOrNil OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC23DKImagePickerController27DKAssetGroupDetailVideoCell")
@interface DKAssetGroupDetailVideoCell : DKAssetGroupDetailImageCell
+ (NSString * _Nonnull)cellReuseIdentifier SWIFT_WARN_UNUSED_RESULT;
- (void)layoutSubviews;
@property (nonatomic, weak) DKAsset * _Nullable asset;
@property (nonatomic, getter=isSelected) BOOL selected;
@end

SWIFT_CLASS("_TtC23DKImagePickerController22DKAssetGroupGridLayout")
@interface DKAssetGroupGridLayout : UICollectionViewFlowLayout
- (void)prepareLayout;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
@end

typedef SWIFT_ENUM(NSInteger, DKAssetType, closed) {
  DKAssetTypePhoto = 0,
  DKAssetTypeVideo = 1,
};

/// ///////////////////////////////////////////////////////////////////
SWIFT_CLASS("_TtC23DKImagePickerController18DKImageBaseManager")
@interface DKImageBaseManager : NSObject
- (void)addWithObserver:(id _Nonnull)object;
- (void)removeWithObserver:(id _Nonnull)object;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@class DKImageAssetExporterConfiguration;
SWIFT_CLASS("_TtC23DKImagePickerController20DKImageAssetExporter")
@interface DKImageAssetExporter : DKImageBaseManager
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) DKImageAssetExporter * _Nonnull sharedInstance;)
+ (DKImageAssetExporter * _Nonnull)sharedInstance SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithConfiguration:(DKImageAssetExporterConfiguration * _Nonnull)configuration OBJC_DESIGNATED_INITIALIZER;
/// This method starts an asynchronous export operation of a batch of asset.
- (int32_t)exportAssetsAsynchronouslyWithAssets:(NSArray<DKAsset *> * _Nonnull)assets completion:(void (^ _Nullable)(NSDictionary * _Nonnull))completion;
- (void)cancelWithRequestID:(int32_t)requestID;
- (void)cancelAll;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

enum DKImageExportPresent : NSInteger;
SWIFT_CLASS("_TtC23DKImagePickerController33DKImageAssetExporterConfiguration")
@interface DKImageAssetExporterConfiguration : NSObject <NSCopying>
@property (nonatomic) enum DKImageExportPresent imageExportPreset;
/// videoExportPreset can be used to specify the transcoding quality for videos (via a AVAssetExportPreset* string).
@property (nonatomic, copy) NSString * _Nonnull videoExportPreset;
@property (nonatomic) AVFileType _Nonnull avOutputFileType;
@property (nonatomic, copy) NSURL * _Nonnull exportDirectory;
@property (nonatomic) CGFloat compressionQuality;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
@end

/// //////////////////////////////////////////////////////////////////////////
typedef SWIFT_ENUM(NSInteger, DKImageAssetExporterError, closed) {
  DKImageAssetExporterErrorCancelled = 0,
  DKImageAssetExporterErrorExportFailed = 1,
};

SWIFT_PROTOCOL("_TtP23DKImagePickerController28DKImageAssetExporterObserver_")
@protocol DKImageAssetExporterObserver
@optional
- (void)exporterWillBeginExportingWithExporter:(DKImageAssetExporter * _Nonnull)exporter asset:(DKAsset * _Nonnull)asset;
/// The progress can be obtained from the DKAsset.
- (void)exporterDidUpdateProgressWithExporter:(DKImageAssetExporter * _Nonnull)exporter asset:(DKAsset * _Nonnull)asset;
/// When the asset’s error is not nil, it indicates that an error occurred while exporting.
- (void)exporterDidEndExportingWithExporter:(DKImageAssetExporter * _Nonnull)exporter asset:(DKAsset * _Nonnull)asset;
@end

/// This is the base class for all extensions.
SWIFT_CLASS("_TtC23DKImagePickerController20DKImageBaseExtension")
@interface DKImageBaseExtension : NSObject
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

typedef SWIFT_ENUM(NSInteger, DKImageExportPresent, closed) {
  DKImageExportPresentCompatible = 0,
  DKImageExportPresentCurrent = 1,
};

enum DKImageExtensionType : NSInteger;
/// The class handles the loading of extensions.
SWIFT_CLASS("_TtC23DKImagePickerController26DKImageExtensionController")
@interface DKImageExtensionController : NSObject
- (void)performWithExtensionType:(enum DKImageExtensionType)extensionType with:(NSDictionary * _Nonnull)extraInfo;
- (void)finishWithExtensionType:(enum DKImageExtensionType)extensionType;
- (void)enableWithExtensionType:(enum DKImageExtensionType)extensionType;
- (void)disableWithExtensionType:(enum DKImageExtensionType)extensionType;
- (BOOL)isExtensionTypeAvailable:(enum DKImageExtensionType)extensionType SWIFT_WARN_UNUSED_RESULT;
/// Registers an extension for the specified type.
+ (void)registerExtensionWithExtensionClass:(SWIFT_METATYPE(DKImageBaseExtension) _Nonnull)extensionClass for:(enum DKImageExtensionType)type;
+ (void)unregisterExtensionFor:(enum DKImageExtensionType)type;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class DKPhotoGallery;
@class UIButton;
SWIFT_CLASS("_TtC23DKImagePickerController23DKImageExtensionGallery")
@interface DKImageExtensionGallery : DKImageBaseExtension <DKPhotoGalleryDelegate>
- (void)photoGallery:(DKPhotoGallery * _Nonnull)gallery didShow:(NSInteger)index;
- (void)selectAssetFromGalleryWithButton:(UIButton * _Nonnull)button;
- (void)dismissGallery;
@end

/// A placeholder object used to represent no any action for the certain ExtensionType.
SWIFT_CLASS("_TtC23DKImagePickerController20DKImageExtensionNone")
@interface DKImageExtensionNone : DKImageBaseExtension
@end

/// /////////////////////////////////////////////////////////////////////
typedef SWIFT_ENUM(NSInteger, DKImageExtensionType, closed) {
  DKImageExtensionTypeGallery = 0,
  DKImageExtensionTypeCamera = 1,
  DKImageExtensionTypeInlineCamera = 2,
  DKImageExtensionTypePhotoEditor = 3,
};

@class PHChange;
/// //////////////////////////////////////////////////////////////////////////////////
SWIFT_CLASS("_TtC23DKImagePickerController23DKImageGroupDataManager")
@interface DKImageGroupDataManager : DKImageBaseManager <PHPhotoLibraryChangeObserver>
- (void)photoLibraryDidChange:(PHChange * _Nonnull)changeInstance;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class PHFetchOptions;
SWIFT_CLASS("_TtC23DKImagePickerController36DKImageGroupDataManagerConfiguration")
@interface DKImageGroupDataManagerConfiguration : NSObject <NSCopying>
/// Options that specify a filter predicate and sort order for the fetched assets, or nil to use default options.
@property (nonatomic, strong) PHFetchOptions * _Nullable assetFetchOptions;
/// Limits the maximum number of objects displayed on the UI, a value of 0 means no limit.  Defaults to 0.
@property (nonatomic) NSInteger fetchLimit;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
@end

/// /////////////////////////////////////////////////////////////////////
SWIFT_CLASS("_TtC23DKImagePickerController24DKUINavigationController")
@interface DKUINavigationController : UINavigationController
- (nonnull instancetype)initWithNavigationBarClass:(Class _Nullable)navigationBarClass toolbarClass:(Class _Nullable)toolbarClass OBJC_DESIGNATED_INITIALIZER SWIFT_AVAILABILITY(ios,introduced=5.0);
- (nonnull instancetype)initWithRootViewController:(UIViewController * _Nonnull)rootViewController OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithNibName:(NSString * _Nullable)nibNameOrNil bundle:(NSBundle * _Nullable)nibBundleOrNil OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
@end

@class DKImagePickerControllerBaseUIDelegate;
enum DKImagePickerControllerAssetType : NSInteger;
enum DKImagePickerControllerSourceType : NSInteger;
@class DKPermissionViewColors;
enum DKImagePickerControllerExportStatus : NSInteger;
@class UIPresentationController;
SWIFT_CLASS("_TtC23DKImagePickerController23DKImagePickerController")
@interface DKImagePickerController : DKUINavigationController <UIAdaptivePresentationControllerDelegate>
/// Use UIDelegate to Customize the picker UI.
@property (nonatomic, strong) DKImagePickerControllerBaseUIDelegate * _Null_unspecified UIDelegate;
/// false to prevent dismissal of the picker when the presentation controller will dismiss in response to user action.
@property (nonatomic, readonly) BOOL shouldDismissViaUserAction SWIFT_AVAILABILITY(ios,introduced=13.0);
/// Forces deselect of previous selected image. allowSwipeToSelect will be ignored.
@property (nonatomic) BOOL singleSelect;
/// Auto close picker on single select
@property (nonatomic) BOOL autoCloseOnSingleSelect;
/// The maximum count of assets which the user will be able to select, a value of 0 means no limit.
@property (nonatomic) NSInteger maxSelectableCount;
/// Allow swipe to select images.
@property (nonatomic) BOOL allowSwipeToSelect;
/// Allow select all
@property (nonatomic) BOOL allowSelectAll;
/// A Bool value indicating whether the inline mode is enabled.
@property (nonatomic, getter=inline, setter=setInline:) BOOL inline_;
/// The type of picker interface to be displayed by the controller.
@property (nonatomic) enum DKImagePickerControllerAssetType assetType;
/// If sourceType is Camera will cause the assetType & maxSelectableCount & allowMultipleTypes to be ignored.
@property (nonatomic) enum DKImagePickerControllerSourceType sourceType;
/// A Bool value indicating whether to allow the picker to select photos and videos at the same time.
@property (nonatomic) BOOL allowMultipleTypes;
/// A Bool value indicating whether to allow the picker auto-rotate the screen.
@property (nonatomic) BOOL allowsLandscape;
/// Set the showsEmptyAlbums to specify whether or not the empty albums is shown in the picker.
@property (nonatomic) BOOL showsEmptyAlbums;
/// A Bool value indicating whether to allow the picker shows the cancel button.
@property (nonatomic) BOOL showsCancelButton;
/// Limits the maximum number of objects displayed on the UI, a value of 0 means no limit.  Defaults to 0.
@property (nonatomic) NSInteger fetchLimit;
/// The block is executed when the user presses the cancel button.
@property (nonatomic, copy) void (^ _Nullable didCancel)(void);
/// The block is executed when the user presses the select button.
@property (nonatomic, copy) void (^ _Nullable didSelectAssets)(NSArray<DKAsset *> * _Nonnull);
/// The block is executed when the number of the selected assets is changed.
@property (nonatomic, copy) void (^ _Nullable selectedChanged)(void);
/// Colors applied to the permission view when access needs to be granted by the user
@property (nonatomic, strong) DKPermissionViewColors * _Nonnull permissionViewColors;
/// A Bool value indicating whether to allow the picker to auto-export the selected assets to the specified directory when done is called.
/// picker will creating a default exporter if exportsWhenCompleted is true and the exporter is nil.
@property (nonatomic) BOOL exportsWhenCompleted;
@property (nonatomic, strong) DKImageAssetExporter * _Nullable exporter;
/// Select view request thumbnail size
@property (nonatomic) CGSize thumbnailSize;
/// Indicates the status of the exporter.
@property (nonatomic, readonly) enum DKImagePickerControllerExportStatus exportStatus;
/// The block is executed when the exportStatus is changed.
@property (nonatomic, copy) void (^ _Nullable exportStatusChanged)(enum DKImagePickerControllerExportStatus);
/// The object that acts as the data source of the picker.
@property (nonatomic, readonly, strong) DKImageGroupDataManager * _Nonnull groupDataManager;
- (void)viewWillAppear:(BOOL)animated;
- (UIViewController <DKImagePickerControllerAware> * _Nonnull)makeRootVC SWIFT_WARN_UNUSED_RESULT;
- (void)presentCamera;
- (void)presentViewController:(UIViewController * _Nonnull)viewControllerToPresent animated:(BOOL)flag completion:(void (^ _Nullable)(void))completion;
- (void)dismissViewControllerAnimated:(BOOL)flag completion:(void (^ _Nullable)(void))completion;
- (void)dismissCamera;
- (void)dismiss;
- (void)done;
/// Reload this picker with a new DKImageGroupDataManager.
- (void)reloadWith:(DKImageGroupDataManager * _Nonnull)dataManager;
- (void)saveImage:(UIImage * _Nonnull)image :(NSDictionary * _Nullable)metadata :(void (^ _Nonnull)(DKAsset * _Nonnull))completeBlock;
- (void)saveImageToAlbum:(UIImage * _Nonnull)image :(void (^ _Nonnull)(DKAsset * _Nonnull))completeBlock;
- (void)saveImageDataToAlbumForiOS9:(NSData * _Nonnull)imageDataWithMetadata :(void (^ _Nonnull)(DKAsset * _Nonnull))completeBlock;
- (NSData * _Nullable)writeMetadata:(NSDictionary * _Nonnull)metadata into:(NSData * _Nonnull)imageData SWIFT_WARN_UNUSED_RESULT;
- (void)selectWithAsset:(DKAsset * _Nonnull)asset;
- (void)selectWithAssets:(NSArray<DKAsset *> * _Nonnull)assets;
- (void)handleSelectAll;
- (void)deselectWithAsset:(DKAsset * _Nonnull)asset;
- (void)removeSelectionWithAsset:(DKAsset * _Nonnull)asset;
- (void)deselectAll;
- (void)setSelectedAssetsWithAssets:(NSArray<DKAsset *> * _Nonnull)assets;
- (BOOL)containsWithAsset:(DKAsset * _Nonnull)asset SWIFT_WARN_UNUSED_RESULT;
- (void)scrollTo:(NSIndexPath * _Nonnull)indexPath animated:(BOOL)animated;
- (void)scrollToLastTappedIndexPathWithAnimated:(BOOL)animated;
@property (nonatomic, readonly, copy) NSArray<DKAsset *> * _Nonnull selectedAssets;
- (BOOL)canSelectWithAsset:(DKAsset * _Nonnull)asset showAlert:(BOOL)showAlert SWIFT_WARN_UNUSED_RESULT;
@property (nonatomic, readonly) BOOL shouldAutorotate;
@property (nonatomic, readonly) UIInterfaceOrientationMask supportedInterfaceOrientations;
- (BOOL)presentationControllerShouldDismiss:(UIPresentationController * _Nonnull)presentationController SWIFT_WARN_UNUSED_RESULT SWIFT_AVAILABILITY(ios,introduced=13.0);
- (void)presentationControllerDidDismiss:(UIPresentationController * _Nonnull)presentationController;
- (nonnull instancetype)initWithNavigationBarClass:(Class _Nullable)navigationBarClass toolbarClass:(Class _Nullable)toolbarClass OBJC_DESIGNATED_INITIALIZER SWIFT_AVAILABILITY(ios,introduced=5.0);
- (nonnull instancetype)initWithRootViewController:(UIViewController * _Nonnull)rootViewController OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithNibName:(NSString * _Nullable)nibNameOrNil bundle:(NSBundle * _Nullable)nibBundleOrNil OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
@end

/// <ul>
///   <li>
///     AllPhotos: Get all photos assets in the assets group.
///   </li>
///   <li>
///     AllVideos: Get all video assets in the assets group.
///   </li>
///   <li>
///     AllAssets: Get all assets in the group.
///   </li>
/// </ul>
typedef SWIFT_ENUM(NSInteger, DKImagePickerControllerAssetType, closed) {
  DKImagePickerControllerAssetTypeAllPhotos = 0,
  DKImagePickerControllerAssetTypeAllVideos = 1,
  DKImagePickerControllerAssetTypeAllAssets = 2,
};

@class UICollectionViewLayout;
@class UIView;
@class UIColor;
enum DKImagePickerGroupListPresentationStyle : NSInteger;
@class UITableViewController;
SWIFT_PROTOCOL("_TtP23DKImagePickerController33DKImagePickerControllerUIDelegate_")
@protocol DKImagePickerControllerUIDelegate
/// The picker calls -prepareLayout once at its first layout as the first message to the UIDelegate instance.
- (void)prepareLayout:(DKImagePickerController * _Nonnull)imagePickerController vc:(UIViewController * _Nonnull)vc;
/// The layout is to provide information about the position and visual state of items in the collection view.
- (SWIFT_METATYPE(UICollectionViewLayout) _Nonnull)layoutForImagePickerController:(DKImagePickerController * _Nonnull)imagePickerController SWIFT_WARN_UNUSED_RESULT;
/// Called when the user needs to show the cancel button.
- (void)imagePickerController:(DKImagePickerController * _Nonnull)imagePickerController showsCancelButtonForVC:(UIViewController * _Nonnull)vc;
/// Called when the user needs to hide the cancel button.
- (void)imagePickerController:(DKImagePickerController * _Nonnull)imagePickerController hidesCancelButtonForVC:(UIViewController * _Nonnull)vc;
/// Called after the user changes the selection.
- (void)imagePickerController:(DKImagePickerController * _Nonnull)imagePickerController didSelectAssets:(NSArray<DKAsset *> * _Nonnull)didSelectAssets;
/// Called after the user changes the selection.
- (void)imagePickerController:(DKImagePickerController * _Nonnull)imagePickerController didDeselectAssets:(NSArray<DKAsset *> * _Nonnull)didDeselectAssets;
/// Called when the count of the selectedAssets did reach <code>maxSelectableCount</code>.
- (void)imagePickerControllerDidReachMaxLimit:(DKImagePickerController * _Nonnull)imagePickerController;
/// Accessory view below content. default is nil.
- (UIView * _Nullable)imagePickerControllerFooterView:(DKImagePickerController * _Nonnull)imagePickerController SWIFT_WARN_UNUSED_RESULT;
/// Accessory view above content. default is nil.
- (UIView * _Nullable)imagePickerControllerHeaderView:(DKImagePickerController * _Nonnull)imagePickerController SWIFT_WARN_UNUSED_RESULT;
/// Set the color of the background of the collection view.
- (UIColor * _Nonnull)imagePickerControllerCollectionViewBackgroundColor SWIFT_WARN_UNUSED_RESULT;
- (SWIFT_METATYPE(DKAssetGroupDetailBaseCell) _Nonnull)imagePickerControllerCollectionImageCell SWIFT_WARN_UNUSED_RESULT;
- (SWIFT_METATYPE(DKAssetGroupDetailBaseCell) _Nonnull)imagePickerControllerCollectionCameraCell SWIFT_WARN_UNUSED_RESULT;
- (SWIFT_METATYPE(DKAssetGroupDetailBaseCell) _Nonnull)imagePickerControllerCollectionVideoCell SWIFT_WARN_UNUSED_RESULT;
- (Class <DKAssetGroupCellType> _Nonnull)imagePickerControllerGroupCell SWIFT_WARN_UNUSED_RESULT;
/// Provide a custom button to be used as the titleView for imagePickerController’s navigationItem.
- (UIButton * _Nonnull)imagePickerControllerSelectGroupButton:(DKImagePickerController * _Nonnull)imagePickerController selectedGroup:(DKAssetGroup * _Nonnull)selectedGroup SWIFT_WARN_UNUSED_RESULT;
/// Specify how asset group list view controller should be presented, default is .popover
- (enum DKImagePickerGroupListPresentationStyle)imagePickerControllerGroupListPresentationStyle SWIFT_WARN_UNUSED_RESULT;
/// Use to customize the group list table view controller before presentation.
- (void)imagePickerControllerPrepareGroupListViewController:(UITableViewController * _Nonnull)listViewController;
@end

/// /////////////////////////////////////////////////////////////////////////////////////////////////
SWIFT_CLASS("_TtC23DKImagePickerController37DKImagePickerControllerBaseUIDelegate")
@interface DKImagePickerControllerBaseUIDelegate : NSObject <DKImagePickerControllerUIDelegate>
- (void)prepareLayout:(DKImagePickerController * _Nonnull)imagePickerController vc:(UIViewController * _Nonnull)vc;
- (SWIFT_METATYPE(UICollectionViewLayout) _Nonnull)layoutForImagePickerController:(DKImagePickerController * _Nonnull)imagePickerController SWIFT_WARN_UNUSED_RESULT;
- (void)imagePickerController:(DKImagePickerController * _Nonnull)imagePickerController showsCancelButtonForVC:(UIViewController * _Nonnull)vc;
- (void)imagePickerController:(DKImagePickerController * _Nonnull)imagePickerController hidesCancelButtonForVC:(UIViewController * _Nonnull)vc;
- (void)imagePickerController:(DKImagePickerController * _Nonnull)imagePickerController didSelectAssets:(NSArray<DKAsset *> * _Nonnull)didSelectAssets;
- (void)imagePickerController:(DKImagePickerController * _Nonnull)imagePickerController didDeselectAssets:(NSArray<DKAsset *> * _Nonnull)didDeselectAssets;
- (void)imagePickerControllerDidReachMaxLimit:(DKImagePickerController * _Nonnull)imagePickerController;
- (UIView * _Nullable)imagePickerControllerFooterView:(DKImagePickerController * _Nonnull)imagePickerController SWIFT_WARN_UNUSED_RESULT;
- (UIView * _Nullable)imagePickerControllerHeaderView:(DKImagePickerController * _Nonnull)imagePickerController SWIFT_WARN_UNUSED_RESULT;
- (UIColor * _Nonnull)imagePickerControllerCollectionViewBackgroundColor SWIFT_WARN_UNUSED_RESULT;
- (SWIFT_METATYPE(DKAssetGroupDetailBaseCell) _Nonnull)imagePickerControllerCollectionImageCell SWIFT_WARN_UNUSED_RESULT;
- (SWIFT_METATYPE(DKAssetGroupDetailBaseCell) _Nonnull)imagePickerControllerCollectionCameraCell SWIFT_WARN_UNUSED_RESULT;
- (SWIFT_METATYPE(DKAssetGroupDetailBaseCell) _Nonnull)imagePickerControllerCollectionVideoCell SWIFT_WARN_UNUSED_RESULT;
- (Class <DKAssetGroupCellType> _Nonnull)imagePickerControllerGroupCell SWIFT_WARN_UNUSED_RESULT;
- (UIButton * _Nonnull)imagePickerControllerSelectGroupButton:(DKImagePickerController * _Nonnull)imagePickerController selectedGroup:(DKAssetGroup * _Nonnull)selectedGroup SWIFT_WARN_UNUSED_RESULT;
- (enum DKImagePickerGroupListPresentationStyle)imagePickerControllerGroupListPresentationStyle SWIFT_WARN_UNUSED_RESULT;
- (void)imagePickerControllerPrepareGroupListViewController:(UITableViewController * _Nonnull)listViewController;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

typedef SWIFT_ENUM(NSInteger, DKImagePickerControllerExportStatus, closed) {
  DKImagePickerControllerExportStatusNone = 0,
  DKImagePickerControllerExportStatusExporting = 1,
};

SWIFT_CLASS("_TtC23DKImagePickerController31DKImagePickerControllerResource")
@interface DKImagePickerControllerResource : NSObject
/// Add a hook for custom localization.
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, copy) NSString * _Nullable (^ _Nullable customLocalizationBlock)(NSString * _Nonnull);)
+ (NSString * _Nullable (^ _Nullable)(NSString * _Nonnull))customLocalizationBlock SWIFT_WARN_UNUSED_RESULT;
+ (void)setCustomLocalizationBlock:(NSString * _Nullable (^ _Nullable)(NSString * _Nonnull))value;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, copy) UIImage * _Nullable (^ _Nullable customImageBlock)(NSString * _Nonnull);)
+ (UIImage * _Nullable (^ _Nullable)(NSString * _Nonnull))customImageBlock SWIFT_WARN_UNUSED_RESULT;
+ (void)setCustomImageBlock:(UIImage * _Nullable (^ _Nullable)(NSString * _Nonnull))value;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

typedef SWIFT_ENUM(NSInteger, DKImagePickerControllerSourceType, closed) {
  DKImagePickerControllerSourceTypeCamera = 0,
  DKImagePickerControllerSourceTypePhoto = 1,
  DKImagePickerControllerSourceTypeBoth = 2,
};

typedef SWIFT_ENUM(NSInteger, DKImagePickerGroupListPresentationStyle, closed) {
  DKImagePickerGroupListPresentationStylePopover = 0,
  DKImagePickerGroupListPresentationStylePresented = 1,
};

SWIFT_CLASS("_TtC23DKImagePickerController16DKPermissionView")
@interface DKPermissionView : UIView
- (void)didMoveToWindow;
- (void)gotoSettings;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC23DKImagePickerController22DKPermissionViewColors")
@interface DKPermissionViewColors : NSObject
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

SWIFT_CLASS("_TtC23DKImagePickerController23DKPopoverViewController")
@interface DKPopoverViewController : UIViewController
+ (void)popoverViewController:(UIViewController * _Nonnull)viewController fromView:(UIView * _Nonnull)fromView arrowColor:(UIColor * _Nonnull)arrowColor;
+ (void)dismissPopoverViewController;
@property (nonatomic, strong) UIColor * _Nonnull arrowColor;
- (void)loadView;
- (void)viewDidLoad;
- (void)didRotateFromInterfaceOrientation:(UIInterfaceOrientation)fromInterfaceOrientation SWIFT_AVAILABILITY(ios,deprecated=8.0);
- (nonnull instancetype)initWithNibName:(NSString * _Nullable)nibNameOrNil bundle:(NSBundle * _Nullable)nibBundleOrNil OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
@end

#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#if defined(__cplusplus)
#endif
#pragma clang diagnostic pop
#endif

#else
#error unsupported Swift architecture
#endif
