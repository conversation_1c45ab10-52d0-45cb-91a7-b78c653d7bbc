<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>DKImagePickerController.bundle/Assets.car</key>
		<data>
		zjO/d3Bo0oUBwTIsDRgYpXP+gsQ=
		</data>
		<key>DKImagePickerController.bundle/Base.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			4xGD8Rwvi93HvaqnO7+vDZWDMBM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/Info.plist</key>
		<data>
		PezX71luNIeIgHzeV07LKKBEU48=
		</data>
		<key>DKImagePickerController.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YLYZhRSzZVHV+D7U/AQzGNwF8Ug=
		</data>
		<key>DKImagePickerController.bundle/ar.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hmgKVe/ELj2gURZGT+GZDwZaP7Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/da.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			D/EoHjvTwHAWEzVuC2G6vBsJnu4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/de.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aBIMF8wyDvY06aV2Bd4D1ivcRU4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/en.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9Q/DfMw3bDYmzyQL9Dp2TQkyUvM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/es.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NevVA8t5NN7C0rKXChImH4O3YfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/fr.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6cJMOe8JrYF03L3W9F79fIYPtE8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/hu.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0eCEwKbFu38BHkg5r6N8402dkXs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/it.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TAlKCVsmH6cihPALHZhnFoQs5LE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/ja.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PIZnwQ5EdXn9Sde2m08gpbjHacs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/ko.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kJxeSwpa45LW72gQKW/iC9Jn4zU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/nb-NO.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GT/zie43E+HBEWl2kTAJRkDdE9s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/nl.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lAVKBUVMnJkxVl9jrus2i2k1oIk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/pt_BR.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			tUPVite8WGcESkhEVtVsN+8aEx8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/ru.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0rIAuu7v039vnJh99/NkEhlYpXI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/tr.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			558fSIGiBHUJCg2iZQvHWlivw1Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/ur.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6EYiiFsDKyAlJwepmIqOnx80kwA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/vi.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			001MdN+6p4Wj2GxyuQi9jI2OymI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/zh-Hans.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Qfv+Sv1HN+PP6w3kSD8UxfCs8m8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/zh-Hant.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xDMDoCCnErpChkw3aU6nZGMhrMw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Headers/DKImagePickerController-Swift.h</key>
		<data>
		FMga66T5IB0iinO4BSBrr1kfXBo=
		</data>
		<key>Headers/DKImagePickerController-umbrella.h</key>
		<data>
		P3y7Cb0eviwsWR61EiFZjrBikh8=
		</data>
		<key>Info.plist</key>
		<data>
		yzDxNK47l5qWIUXKCFtw1W+B51U=
		</data>
		<key>Modules/DKImagePickerController.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		KxGrbTokG4DG5eQS9Reqw9Y4lzw=
		</data>
		<key>Modules/DKImagePickerController.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		gzFqc9XPwrWSIZcy0qHW/NWvLf4=
		</data>
		<key>Modules/DKImagePickerController.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/DKImagePickerController.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		rptgL4eEPfSiWznG9g26TkWqbcg=
		</data>
		<key>Modules/DKImagePickerController.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		lKnGeQCvDAGO7i9Nu40FAnyt574=
		</data>
		<key>Modules/DKImagePickerController.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/DKImagePickerController.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		IhOP8ghONK1qW8UZUIFp7C7YoJk=
		</data>
		<key>Modules/DKImagePickerController.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		Z/hhF3zoAoa13yb24cB00Jlj+b0=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		xy0JY9bUExo4PNO+rnFYmBuocHA=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>DKImagePickerController.bundle/Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			zjO/d3Bo0oUBwTIsDRgYpXP+gsQ=
			</data>
			<key>hash2</key>
			<data>
			667HLSbSeU9hkV1L2MLVFTFt/YuVyKx23zcSID23kTQ=
			</data>
		</dict>
		<key>DKImagePickerController.bundle/Base.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			4xGD8Rwvi93HvaqnO7+vDZWDMBM=
			</data>
			<key>hash2</key>
			<data>
			ELjMRLCkLklsKSO6rFYxrtU1AXPGCM/jJcf6s27mQfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			PezX71luNIeIgHzeV07LKKBEU48=
			</data>
			<key>hash2</key>
			<data>
			uSSU3BNE4dZeleD2nNTJHfa4aAE4kZYQyAER6Ikb77Y=
			</data>
		</dict>
		<key>DKImagePickerController.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			YLYZhRSzZVHV+D7U/AQzGNwF8Ug=
			</data>
			<key>hash2</key>
			<data>
			S5/UHVcXm+A0szmeblzHSHm8fdkVgUuu5mWh9E35hUE=
			</data>
		</dict>
		<key>DKImagePickerController.bundle/ar.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hmgKVe/ELj2gURZGT+GZDwZaP7Q=
			</data>
			<key>hash2</key>
			<data>
			8EBEfhbg9Wp48uZkSBXh83/CjuaZbY0Pl5MP0h7k198=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/da.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			D/EoHjvTwHAWEzVuC2G6vBsJnu4=
			</data>
			<key>hash2</key>
			<data>
			Se5q7h7rJFaZACyuxQHvVnbMpZuFEPY2ghUK5n4o08I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/de.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aBIMF8wyDvY06aV2Bd4D1ivcRU4=
			</data>
			<key>hash2</key>
			<data>
			H7oPc9FDMsyBTv1ge4zzQ9mwsX0FoBuLZ9b7IesFuOM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/en.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9Q/DfMw3bDYmzyQL9Dp2TQkyUvM=
			</data>
			<key>hash2</key>
			<data>
			wRktIAnVAnqy13yInBFBAY0DrBHpEtI9oKtzLeXD3RI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/es.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NevVA8t5NN7C0rKXChImH4O3YfE=
			</data>
			<key>hash2</key>
			<data>
			TYau/PSO1F3K80YkOSGGsuUdKr+GbcbO0xkmsoVguEE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/fr.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6cJMOe8JrYF03L3W9F79fIYPtE8=
			</data>
			<key>hash2</key>
			<data>
			XeqMTtiKSK1J6ujdx7yhXp7BWuvKJYI+RQnYiYny4D8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/hu.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0eCEwKbFu38BHkg5r6N8402dkXs=
			</data>
			<key>hash2</key>
			<data>
			koXD1oSMQ6BAgGJmcCgH62mWYajI2uBMC9WX+USGCWU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/it.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TAlKCVsmH6cihPALHZhnFoQs5LE=
			</data>
			<key>hash2</key>
			<data>
			nhSK2bw/+PLGTtW27tPjkOk71T+MncfskpqPwK2lhZI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/ja.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PIZnwQ5EdXn9Sde2m08gpbjHacs=
			</data>
			<key>hash2</key>
			<data>
			KeL0orI52PODaxhrV4RvgbgC4Q6krDkO/sprxqlRPfU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/ko.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kJxeSwpa45LW72gQKW/iC9Jn4zU=
			</data>
			<key>hash2</key>
			<data>
			FCrA4Gc0Mj90bRXELikqH38ISm+VZn/fTFdAWqFNWgw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/nb-NO.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GT/zie43E+HBEWl2kTAJRkDdE9s=
			</data>
			<key>hash2</key>
			<data>
			djVG/NpWtRWjSA3G1iIg53ggesmLPWxj3L7oc2E6h8M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/nl.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lAVKBUVMnJkxVl9jrus2i2k1oIk=
			</data>
			<key>hash2</key>
			<data>
			wObmt9BBd0IFYimw+uGyKGjKEALrJsjZa+4YtbpoR5g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/pt_BR.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			tUPVite8WGcESkhEVtVsN+8aEx8=
			</data>
			<key>hash2</key>
			<data>
			n6iu95SLuBRFRQbd6crMWsW4I6N5HJDoNF0YNzoYpBw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/ru.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0rIAuu7v039vnJh99/NkEhlYpXI=
			</data>
			<key>hash2</key>
			<data>
			M12gTcd21LAS83vmg70IxBhMrkr9SUy+lMNgqQqgJn0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/tr.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			558fSIGiBHUJCg2iZQvHWlivw1Q=
			</data>
			<key>hash2</key>
			<data>
			CuIDAgluqD/JKtE9pTTvnu9rGSot//IS3XV5HYlNKFU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/ur.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6EYiiFsDKyAlJwepmIqOnx80kwA=
			</data>
			<key>hash2</key>
			<data>
			OX1UoR/0JwgfTkhVAuVLC8rm8pkso6MssB+Tb+Kemxg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/vi.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			001MdN+6p4Wj2GxyuQi9jI2OymI=
			</data>
			<key>hash2</key>
			<data>
			N5fi2LsY43ka7PJFMBqTc+GjU5z417xGXSmegrxxUJc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/zh-Hans.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Qfv+Sv1HN+PP6w3kSD8UxfCs8m8=
			</data>
			<key>hash2</key>
			<data>
			gW58yg4AYSSFypzzxa1sQ8teATRYb34fi4Jrfwasbuo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/zh-Hant.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xDMDoCCnErpChkw3aU6nZGMhrMw=
			</data>
			<key>hash2</key>
			<data>
			VoNMNcj3ugOpQ10JjxUvPX5MoSBLftKJdiq9sJ1ijsA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Headers/DKImagePickerController-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			FMga66T5IB0iinO4BSBrr1kfXBo=
			</data>
			<key>hash2</key>
			<data>
			KyUk8Kb38cz5DaIrNZ8ZjHgyT3123GhtOLXXdxIIXu0=
			</data>
		</dict>
		<key>Headers/DKImagePickerController-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			P3y7Cb0eviwsWR61EiFZjrBikh8=
			</data>
			<key>hash2</key>
			<data>
			AfwLPIUdCyV/OAQTW4dK3G7CLL0QXdLGqCS1k4oX5m0=
			</data>
		</dict>
		<key>Modules/DKImagePickerController.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			KxGrbTokG4DG5eQS9Reqw9Y4lzw=
			</data>
			<key>hash2</key>
			<data>
			Y7kuVyMZ2tsl5pgT9pQU6QutC56Od+QpP24BjnWV1D4=
			</data>
		</dict>
		<key>Modules/DKImagePickerController.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			gzFqc9XPwrWSIZcy0qHW/NWvLf4=
			</data>
			<key>hash2</key>
			<data>
			iRvmjAnWxbYFAnAXzdnBYthFoT9OZOKaXm/NPw3UdPY=
			</data>
		</dict>
		<key>Modules/DKImagePickerController.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/DKImagePickerController.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			rptgL4eEPfSiWznG9g26TkWqbcg=
			</data>
			<key>hash2</key>
			<data>
			bUFkjXuxuNSgFlBTtPejcDMyOuh45XA25lcRoUhK548=
			</data>
		</dict>
		<key>Modules/DKImagePickerController.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			lKnGeQCvDAGO7i9Nu40FAnyt574=
			</data>
			<key>hash2</key>
			<data>
			oFWV7yOex3qOhnlY/MeamqoPu/WAzyBgXVa6TUmhexY=
			</data>
		</dict>
		<key>Modules/DKImagePickerController.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/DKImagePickerController.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			IhOP8ghONK1qW8UZUIFp7C7YoJk=
			</data>
			<key>hash2</key>
			<data>
			57ZOfcFZG0B6mt8lK1oTOoJPQ7q5gxKhPKpXA/cFi2I=
			</data>
		</dict>
		<key>Modules/DKImagePickerController.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			Z/hhF3zoAoa13yb24cB00Jlj+b0=
			</data>
			<key>hash2</key>
			<data>
			LLqefZP/UAl0/8e9v4VgNGdidQoL/B5QVi+Ei974Y0g=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			xy0JY9bUExo4PNO+rnFYmBuocHA=
			</data>
			<key>hash2</key>
			<data>
			+HmK2MZ1xP8PqS++pGNJSrF/p6z21XnecKKDmd8fafU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
