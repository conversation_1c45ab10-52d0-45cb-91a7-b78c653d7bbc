<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>DKPhotoGallery.bundle/Assets.car</key>
		<data>
		JUkORikzNj7YklXkfmB1dLBQw+g=
		</data>
		<key>DKPhotoGallery.bundle/Base.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NA0j4SwR2AsLRfnHuNxL8Z1By44=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKPhotoGallery.bundle/Info.plist</key>
		<data>
		ldmOcoZsZGGf+ltfHtFdkgeaG+Y=
		</data>
		<key>DKPhotoGallery.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YLYZhRSzZVHV+D7U/AQzGNwF8Ug=
		</data>
		<key>DKPhotoGallery.bundle/en.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lBygwcHp7X/0n1lfhAX4rhrabvk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKPhotoGallery.bundle/zh-Hans.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TC+NpTOWlgfdj3gzJygtTnl0V0o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Headers/DKPhotoGallery-Swift.h</key>
		<data>
		Nmza001OCQ7pEgEFFWQ1HXMS8Mo=
		</data>
		<key>Headers/DKPhotoGallery-umbrella.h</key>
		<data>
		i/W3yiWZBmU3ULsoVY0zdevcZok=
		</data>
		<key>Info.plist</key>
		<data>
		dQFZfnSExC5Fxke7Y1X6BRruo0Y=
		</data>
		<key>Modules/DKPhotoGallery.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		Q3dt8WJiPGC/mOkxXnCRlNRLuXk=
		</data>
		<key>Modules/DKPhotoGallery.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		ShObK68xUFed/m1eGjsajv5owtI=
		</data>
		<key>Modules/DKPhotoGallery.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/DKPhotoGallery.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		oQqwbazPrsE4c6VQuFpLqZ1pWoQ=
		</data>
		<key>Modules/DKPhotoGallery.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		bSOXrpc8tttLVRYyxBS+5cZmvXg=
		</data>
		<key>Modules/DKPhotoGallery.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/DKPhotoGallery.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		lRD/0g2BlYlEtvDjCV2zsHwETAY=
		</data>
		<key>Modules/DKPhotoGallery.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		8yP/qTzP/fzA7HMYm6obnDakrbw=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		NYYQR8QvEEu1gT4bK6EEPx15tqw=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>DKPhotoGallery.bundle/Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			JUkORikzNj7YklXkfmB1dLBQw+g=
			</data>
			<key>hash2</key>
			<data>
			QphuPfxSxEjCer1xILrbDv+orb4LZ21H6UGzncmNNDs=
			</data>
		</dict>
		<key>DKPhotoGallery.bundle/Base.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NA0j4SwR2AsLRfnHuNxL8Z1By44=
			</data>
			<key>hash2</key>
			<data>
			SDI8mZH3KxLV35hSqjP1DaoT/Ur7RH3bmV+MnjMnx54=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKPhotoGallery.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			ldmOcoZsZGGf+ltfHtFdkgeaG+Y=
			</data>
			<key>hash2</key>
			<data>
			wCyvf8JR0k8izhplEN1RDaQRxIcdbYDV+4ykiSv058E=
			</data>
		</dict>
		<key>DKPhotoGallery.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			YLYZhRSzZVHV+D7U/AQzGNwF8Ug=
			</data>
			<key>hash2</key>
			<data>
			S5/UHVcXm+A0szmeblzHSHm8fdkVgUuu5mWh9E35hUE=
			</data>
		</dict>
		<key>DKPhotoGallery.bundle/en.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lBygwcHp7X/0n1lfhAX4rhrabvk=
			</data>
			<key>hash2</key>
			<data>
			79OWR8uzUiipYvXTl3V4OfE8HGNgQXp4Is5CjRpErmE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKPhotoGallery.bundle/zh-Hans.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TC+NpTOWlgfdj3gzJygtTnl0V0o=
			</data>
			<key>hash2</key>
			<data>
			KI058+XFexomjnRqlnWcg5B3sueg9C1fAlugBgmGNzs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Headers/DKPhotoGallery-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			Nmza001OCQ7pEgEFFWQ1HXMS8Mo=
			</data>
			<key>hash2</key>
			<data>
			mH1dskeje8imWs1PzqtRRvLU2qD0Ad7TgFS0Tt1P/To=
			</data>
		</dict>
		<key>Headers/DKPhotoGallery-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			i/W3yiWZBmU3ULsoVY0zdevcZok=
			</data>
			<key>hash2</key>
			<data>
			OYM3qSiWMgTBmHRUhkoDqYXhnBRCsRBn3PS4CfDmCGM=
			</data>
		</dict>
		<key>Modules/DKPhotoGallery.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			Q3dt8WJiPGC/mOkxXnCRlNRLuXk=
			</data>
			<key>hash2</key>
			<data>
			7OmChgGqO0VfvS9911Vab2LVyr0SnrXYMBVXFmOzBNY=
			</data>
		</dict>
		<key>Modules/DKPhotoGallery.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			ShObK68xUFed/m1eGjsajv5owtI=
			</data>
			<key>hash2</key>
			<data>
			/yn5KBSBy1/dwWH6Qh8zQCqLSdKrqft3r6jqUKB/iwQ=
			</data>
		</dict>
		<key>Modules/DKPhotoGallery.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/DKPhotoGallery.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			oQqwbazPrsE4c6VQuFpLqZ1pWoQ=
			</data>
			<key>hash2</key>
			<data>
			H535ocIh/fujgHPUGZYFKdtSXQinQ3Y4N2xgrCMIYi8=
			</data>
		</dict>
		<key>Modules/DKPhotoGallery.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			bSOXrpc8tttLVRYyxBS+5cZmvXg=
			</data>
			<key>hash2</key>
			<data>
			GmfPaoh6kO8VOe9p7nt66bgpgTMg9VNkTMbLrD4kMhs=
			</data>
		</dict>
		<key>Modules/DKPhotoGallery.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/DKPhotoGallery.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			lRD/0g2BlYlEtvDjCV2zsHwETAY=
			</data>
			<key>hash2</key>
			<data>
			ByzcXJA96I6/gRWbQ24JbZfNRIPYtaMvaCLHxKC7Qic=
			</data>
		</dict>
		<key>Modules/DKPhotoGallery.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			8yP/qTzP/fzA7HMYm6obnDakrbw=
			</data>
			<key>hash2</key>
			<data>
			dEdC4nFyk+HdwhM3COZvSGEWbuMfX9JDUfyFcp9RUHg=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			NYYQR8QvEEu1gT4bK6EEPx15tqw=
			</data>
			<key>hash2</key>
			<data>
			BzltYNeA5YT/iGZYIZ+47qiLVxcVT4cbVV0bB95WKg4=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
