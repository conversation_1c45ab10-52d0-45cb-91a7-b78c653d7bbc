<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<data>
		mnLbgBhrpRwdlXh4UKzYj73lYuA=
		</data>
		<key><EMAIL></key>
		<data>
		WBDcNG/0BUOtsKQgKYOLyuqAbAM=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		Bqtil6RquU1Hfn8gu0IYARWvCIM=
		</data>
		<key>Assets.car</key>
		<data>
		Il35hacUaH0JiYYcJ8LN5SRN078=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<data>
		28xWMBQ91UzszfdXY91SqhC7ecg=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		ZVgM1+KwZcZnwhgaI0F7Bt1ba2c=
		</data>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<data>
		hMnf/VIyTGR2nRcoLS3JCfeGmDs=
		</data>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<data>
		MDrKFvFWroTb0+KEbQShBcoBvo4=
		</data>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<data>
		nFC1waP0YzYOchnqa85lPwrC73s=
		</data>
		<key>Frameworks/App.framework/App</key>
		<data>
		azWY+kUfh0PjCkxohqMl8mQTbJM=
		</data>
		<key>Frameworks/App.framework/Info.plist</key>
		<data>
		h5OB7aKzS5WR9SemvZAyN6FEkJs=
		</data>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<data>
		UsDYtCWdGMkGfFiuSKQ5Uq/Lk+A=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<data>
		ME0cAg6cl/bTZmwXEVgMugDccUI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<data>
		oG5/OGn+vw7vp/nu5DUDoZJ4nFc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<data>
		vKJkVIcw+LGHFnKJGwrQwCREv68=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<data>
		mC8XySoQtoSWursrMFE/fPYRXtU=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NativeAssetsManifest.json</key>
		<data>
		re4p7E8rPLLsN+wzaPN/+AVpXTY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<data>
		gRDbYzMNgtJeT9rLZDdiOJ0RXMg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<data>
		yv4cDu1rC+xN5186URfJzE4PMJY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<data>
		Bvk+P1ykE1PGRdktwgwDyz6AOqM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		VoVnsu58hN7wrwazX0nyAopiB0Q=
		</data>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<data>
		6f33nJbLQOxwLNQ7Pq7TZR9ENuo=
		</data>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController</key>
		<data>
		sXbNkomg1PVRmkNwAuuLCOie8ao=
		</data>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/Assets.car</key>
		<data>
		zjO/d3Bo0oUBwTIsDRgYpXP+gsQ=
		</data>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/Base.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			4xGD8Rwvi93HvaqnO7+vDZWDMBM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/Info.plist</key>
		<data>
		PezX71luNIeIgHzeV07LKKBEU48=
		</data>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YLYZhRSzZVHV+D7U/AQzGNwF8Ug=
		</data>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/ar.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hmgKVe/ELj2gURZGT+GZDwZaP7Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/da.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			D/EoHjvTwHAWEzVuC2G6vBsJnu4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/de.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aBIMF8wyDvY06aV2Bd4D1ivcRU4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/en.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9Q/DfMw3bDYmzyQL9Dp2TQkyUvM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/es.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NevVA8t5NN7C0rKXChImH4O3YfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/fr.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6cJMOe8JrYF03L3W9F79fIYPtE8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/hu.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0eCEwKbFu38BHkg5r6N8402dkXs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/it.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TAlKCVsmH6cihPALHZhnFoQs5LE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/ja.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PIZnwQ5EdXn9Sde2m08gpbjHacs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/ko.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kJxeSwpa45LW72gQKW/iC9Jn4zU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/nb-NO.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GT/zie43E+HBEWl2kTAJRkDdE9s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/nl.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lAVKBUVMnJkxVl9jrus2i2k1oIk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/pt_BR.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			tUPVite8WGcESkhEVtVsN+8aEx8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/ru.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0rIAuu7v039vnJh99/NkEhlYpXI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/tr.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			558fSIGiBHUJCg2iZQvHWlivw1Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/ur.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6EYiiFsDKyAlJwepmIqOnx80kwA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/vi.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			001MdN+6p4Wj2GxyuQi9jI2OymI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/zh-Hans.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Qfv+Sv1HN+PP6w3kSD8UxfCs8m8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/zh-Hant.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xDMDoCCnErpChkw3aU6nZGMhrMw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/Info.plist</key>
		<data>
		yzDxNK47l5qWIUXKCFtw1W+B51U=
		</data>
		<key>Frameworks/DKImagePickerController.framework/_CodeSignature/CodeResources</key>
		<data>
		v2TO3CkFddqw+OFlBk/57jC50e8=
		</data>
		<key>Frameworks/DKPhotoGallery.framework/DKPhotoGallery</key>
		<data>
		BvLpazaoWEztqZJ3bNq99UIE1Oo=
		</data>
		<key>Frameworks/DKPhotoGallery.framework/DKPhotoGallery.bundle/Assets.car</key>
		<data>
		JUkORikzNj7YklXkfmB1dLBQw+g=
		</data>
		<key>Frameworks/DKPhotoGallery.framework/DKPhotoGallery.bundle/Base.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NA0j4SwR2AsLRfnHuNxL8Z1By44=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKPhotoGallery.framework/DKPhotoGallery.bundle/Info.plist</key>
		<data>
		ldmOcoZsZGGf+ltfHtFdkgeaG+Y=
		</data>
		<key>Frameworks/DKPhotoGallery.framework/DKPhotoGallery.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YLYZhRSzZVHV+D7U/AQzGNwF8Ug=
		</data>
		<key>Frameworks/DKPhotoGallery.framework/DKPhotoGallery.bundle/en.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lBygwcHp7X/0n1lfhAX4rhrabvk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKPhotoGallery.framework/DKPhotoGallery.bundle/zh-Hans.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TC+NpTOWlgfdj3gzJygtTnl0V0o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKPhotoGallery.framework/Info.plist</key>
		<data>
		dQFZfnSExC5Fxke7Y1X6BRruo0Y=
		</data>
		<key>Frameworks/DKPhotoGallery.framework/_CodeSignature/CodeResources</key>
		<data>
		Mu6Y6w7RXCus1I0bNUw97/3AAf0=
		</data>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<data>
		4quGlub/IwnoqEYVFRSdPvyu8gU=
		</data>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<data>
		wTPJHICwW6wxY3b87ek7ITN5kJk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<data>
		zbvYFr9dywry0lMMrHuNOOaNgkY=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<data>
		ksjIMu5IPw+Q3rw2YkAx0KjxkdM=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<data>
		V/wkSSsyYdMoexF6wPrC3KgkL4g=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<data>
		vFsZXNqjflvqKqAzsIptQaTSJho=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<data>
		sUgX1PJzkvyinL5i7nS1ro/Kd5o=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<data>
		SpNs7IhIC7xP34Ej+LQCaEZkqik=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<data>
		AqVvCbPmgWMQKrRnib05Okrjbp0=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<data>
		bkw+DmHReHDg1PPcvmSjuLZrheA=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<data>
		UqnnVWwQEYYX56eu7lt6dpR3LIc=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<data>
		VjAwScWkWWSrDeetip3K4yhuwDU=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<data>
		crQ9782ULebLQfIR+MbBkjB7d+k=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<data>
		ocQVSiAiUMYfVtZIn48LpYTJA5w=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<data>
		EARXud6pHb7ZYP8eXPDnluMqcXk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<data>
		qWHw5VIWEa0NmJ1PMhD16nlfRKk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<data>
		31prWLso2k5PfMMSbf5hGl+VE6Y=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<data>
		LDr6kSVbUfyQFAxLwCACF5S2VEA=
		</data>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<data>
		Fn6CJcrmOY7R/UCpVvDdwVm3fwA=
		</data>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<data>
		wJV5dCKEGl+FAtDc8wJJh/fvKXs=
		</data>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<data>
		D+cqXttvC7E/uziGjFdqFabWd7A=
		</data>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<data>
		DL4bsE6gOoPUAg6JuKJAbSJtTaY=
		</data>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<data>
		ipm8hg7aB3LzsfShJfpNR0QQ4hw=
		</data>
		<key>Frameworks/SDWebImage.framework/Info.plist</key>
		<data>
		BJcJ1dKfVau6iwBEEPUshVJFzgw=
		</data>
		<key>Frameworks/SDWebImage.framework/SDWebImage</key>
		<data>
		GHorPayNYUaExGB56LClmIitY6U=
		</data>
		<key>Frameworks/SDWebImage.framework/SDWebImage.bundle/Info.plist</key>
		<data>
		fDgvkq3CZBiFRTmXisIrYs47Vz8=
		</data>
		<key>Frameworks/SDWebImage.framework/SDWebImage.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PFHYbs0V3eUFDWQyYQcwEetuqEk=
		</data>
		<key>Frameworks/SDWebImage.framework/_CodeSignature/CodeResources</key>
		<data>
		YCjuAD02EXILqkJjx+Uq6P3prhE=
		</data>
		<key>Frameworks/SwiftyGif.framework/Info.plist</key>
		<data>
		98mij3q5W0C/IWWDOmow+a0+ZNU=
		</data>
		<key>Frameworks/SwiftyGif.framework/SwiftyGif</key>
		<data>
		4SzHVxxCSSk6gyN4RMBBr1NEPJQ=
		</data>
		<key>Frameworks/SwiftyGif.framework/SwiftyGif.bundle/Info.plist</key>
		<data>
		QihogB2Uh6sxZH96UmF/U0pyDHQ=
		</data>
		<key>Frameworks/SwiftyGif.framework/SwiftyGif.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		i3YpomTpJHpRGpdt+oGaesmknBg=
		</data>
		<key>Frameworks/SwiftyGif.framework/_CodeSignature/CodeResources</key>
		<data>
		rqcpJZQ3/9eclo5roX18QTVlOVw=
		</data>
		<key>Frameworks/file_picker.framework/Info.plist</key>
		<data>
		/8RL5EjcMlQshiSbv80ui07Zrl8=
		</data>
		<key>Frameworks/file_picker.framework/_CodeSignature/CodeResources</key>
		<data>
		DLVs5IC/SS4Rkss3Xs5O1eKC40s=
		</data>
		<key>Frameworks/file_picker.framework/file_picker</key>
		<data>
		IQkJraLR5QQ76bRziOh+FGVAKK4=
		</data>
		<key>Frameworks/path_provider_foundation.framework/Info.plist</key>
		<data>
		Bf1UZJU/fB1U3eFVZR/FS9EgOO8=
		</data>
		<key>Frameworks/path_provider_foundation.framework/_CodeSignature/CodeResources</key>
		<data>
		pu/Xx1XGqi+kjxMiU9Lg/yFd9CE=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation</key>
		<data>
		BJA9M2P72JnexgoU5CgCVTGP+sc=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist</key>
		<data>
		0k4F/m5fStSsN0EWvqvYpqxNy8g=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/sqflite_darwin.framework/Info.plist</key>
		<data>
		PO72UreZXG/QWV+fN/HViQJUgD8=
		</data>
		<key>Frameworks/sqflite_darwin.framework/_CodeSignature/CodeResources</key>
		<data>
		oWWl09QtryssXKB49gbLQH+nLYc=
		</data>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin</key>
		<data>
		jQYJFnMRNEutONIM+5I8sjpK+4k=
		</data>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/Info.plist</key>
		<data>
		zxcuJuY27PHPW0cRUIvmtipcFSU=
		</data>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YIiJ5tHvqBeSpBm2mcfVZdaGz3E=
		</data>
		<key>Info.plist</key>
		<data>
		KU9BwHpvRHeaFOLlquy3QiPXFkM=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>Runner.debug.dylib</key>
		<data>
		oJu9clTb+doyaFRDPQgdExKUFVI=
		</data>
		<key>__preview.dylib</key>
		<data>
		MBPtijCeJg18LCYsHp0E7X89b24=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Zb9VR5aeuJMnm/RgXM3cr4LUNi9UZgxKD7xAgkid0NI=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Gb4XFIHccaCygD680B3YsMX9V3je40wKPKvJSMIl8k4=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			QcfUL25h+P5/MLH/oiVq7LyWgr4G0YxKMGIEPhouVHw=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			si6i09re48SJpV+zboaxyi9emFBw46u3r8izu2l66jk=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			by6WshwXWgbEYiAy2bvh0UtjSVa3EwySkNFc1FazGdY=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VPNjf2cf66XxnoLsT0p/tEi7PPwPsYDwiapXH8jwU+I=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			BY/hOMO0FcCl8mCMQqjVbFeb8Q97c1G9lHscfspHFNk=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			PpvapAjR62rl6Ym4E6hkTgpKmBICxTaQXeUqcpHmmqQ=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			y90o2JQjssm+7ysnziyWCNMNbGqdLnZ595pTgURE5T8=
			</data>
		</dict>
		<key>Frameworks/App.framework/App</key>
		<dict>
			<key>hash2</key>
			<data>
			eDS9gm/0Ippb9mCmflUsPKFsnY8zcQc1ewPqVFLdg1k=
			</data>
		</dict>
		<key>Frameworks/App.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SES+IrctFtb2ATzloiQHKMcw7x/vnPQ6XFUZixhhSAI=
			</data>
		</dict>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			Sp+npKv/bsA9gFdZIpO6vEozRnvIkJYfrudW4p+qhPQ=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			AK9VrT1vIYmP534P8JLRoc2lLJQbaGDpko1FyK+MCV0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Sps95+7JukayebvM0TLjL1LW1VXXndpKp/O8tOm9ZR8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zX4DZFvESy3Ue3y2JvUcTsv1Whl6t3JBYotHrBZfviE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			CmWCem5syuiiRl8KlteOL8HzJtKy6ytUhr4P4hMtihM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NativeAssetsManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lUijHkoEgTXB2U+Rkyi/tirix7s8q5ZVfHlB2ql3dss=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			Ut/uEhc6+hUf6S/IpyxQydpXSJV0AmyYEkKu13s4D6o=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			H3eajeeLldQZ4t/AnjV6ujTcdyg8PbRNr+qnJhzmtUs=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Z8RP6Rg7AC553ef2l34piGYcmj5KPF/OloeH79vtgjw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			4PC1vOWKHlNOnW4dGwFfD4TPwBCllniPCsOy7+1X5co=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			EkAQI7At/OAUaTSQUy3t6/4PpHWrvbPs+YdJHmzJ978=
			</data>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController</key>
		<dict>
			<key>hash2</key>
			<data>
			MXknRU/EbAm/YqvJlCcE53IGS9kZjaM9KRoNRDrMlD4=
			</data>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			667HLSbSeU9hkV1L2MLVFTFt/YuVyKx23zcSID23kTQ=
			</data>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/Base.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ELjMRLCkLklsKSO6rFYxrtU1AXPGCM/jJcf6s27mQfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			uSSU3BNE4dZeleD2nNTJHfa4aAE4kZYQyAER6Ikb77Y=
			</data>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			S5/UHVcXm+A0szmeblzHSHm8fdkVgUuu5mWh9E35hUE=
			</data>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/ar.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8EBEfhbg9Wp48uZkSBXh83/CjuaZbY0Pl5MP0h7k198=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/da.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Se5q7h7rJFaZACyuxQHvVnbMpZuFEPY2ghUK5n4o08I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/de.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			H7oPc9FDMsyBTv1ge4zzQ9mwsX0FoBuLZ9b7IesFuOM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/en.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			wRktIAnVAnqy13yInBFBAY0DrBHpEtI9oKtzLeXD3RI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/es.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			TYau/PSO1F3K80YkOSGGsuUdKr+GbcbO0xkmsoVguEE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/fr.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			XeqMTtiKSK1J6ujdx7yhXp7BWuvKJYI+RQnYiYny4D8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/hu.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			koXD1oSMQ6BAgGJmcCgH62mWYajI2uBMC9WX+USGCWU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/it.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nhSK2bw/+PLGTtW27tPjkOk71T+MncfskpqPwK2lhZI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/ja.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			KeL0orI52PODaxhrV4RvgbgC4Q6krDkO/sprxqlRPfU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/ko.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			FCrA4Gc0Mj90bRXELikqH38ISm+VZn/fTFdAWqFNWgw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/nb-NO.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			djVG/NpWtRWjSA3G1iIg53ggesmLPWxj3L7oc2E6h8M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/nl.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			wObmt9BBd0IFYimw+uGyKGjKEALrJsjZa+4YtbpoR5g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/pt_BR.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			n6iu95SLuBRFRQbd6crMWsW4I6N5HJDoNF0YNzoYpBw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/ru.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			M12gTcd21LAS83vmg70IxBhMrkr9SUy+lMNgqQqgJn0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/tr.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CuIDAgluqD/JKtE9pTTvnu9rGSot//IS3XV5HYlNKFU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/ur.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			OX1UoR/0JwgfTkhVAuVLC8rm8pkso6MssB+Tb+Kemxg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/vi.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			N5fi2LsY43ka7PJFMBqTc+GjU5z417xGXSmegrxxUJc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/zh-Hans.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			gW58yg4AYSSFypzzxa1sQ8teATRYb34fi4Jrfwasbuo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/DKImagePickerController.bundle/zh-Hant.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			VoNMNcj3ugOpQ10JjxUvPX5MoSBLftKJdiq9sJ1ijsA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			qRULhuJmiU1a6YOUxTYT3PwPOzYw2FQX+v0RItlvdMk=
			</data>
		</dict>
		<key>Frameworks/DKImagePickerController.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			57C2VfQIuMaYnNGjI74SnZSXlQai+Zdw+rQkxf9oY7g=
			</data>
		</dict>
		<key>Frameworks/DKPhotoGallery.framework/DKPhotoGallery</key>
		<dict>
			<key>hash2</key>
			<data>
			p7ECtQU+FU9/FSoBSMUawU1D8F/cjrZgqEZSfvmm6Uo=
			</data>
		</dict>
		<key>Frameworks/DKPhotoGallery.framework/DKPhotoGallery.bundle/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			QphuPfxSxEjCer1xILrbDv+orb4LZ21H6UGzncmNNDs=
			</data>
		</dict>
		<key>Frameworks/DKPhotoGallery.framework/DKPhotoGallery.bundle/Base.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			SDI8mZH3KxLV35hSqjP1DaoT/Ur7RH3bmV+MnjMnx54=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKPhotoGallery.framework/DKPhotoGallery.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			wCyvf8JR0k8izhplEN1RDaQRxIcdbYDV+4ykiSv058E=
			</data>
		</dict>
		<key>Frameworks/DKPhotoGallery.framework/DKPhotoGallery.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			S5/UHVcXm+A0szmeblzHSHm8fdkVgUuu5mWh9E35hUE=
			</data>
		</dict>
		<key>Frameworks/DKPhotoGallery.framework/DKPhotoGallery.bundle/en.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			79OWR8uzUiipYvXTl3V4OfE8HGNgQXp4Is5CjRpErmE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKPhotoGallery.framework/DKPhotoGallery.bundle/zh-Hans.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			KI058+XFexomjnRqlnWcg5B3sueg9C1fAlugBgmGNzs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/DKPhotoGallery.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			1yqRuQwdObkIBO5vnUrBAFXq/VFS8q+xgcSPAO0xp5I=
			</data>
		</dict>
		<key>Frameworks/DKPhotoGallery.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			jfHtK60uZBtRLRSSfv1xI016cqG0ty9hWVFaEgZyM2E=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<dict>
			<key>hash2</key>
			<data>
			Lc7KV1yE5k27Y+TUIs3vims36oZVZ77QJp+19LMMnP0=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			auaf7wPxiASCYD2ACy1dfbMJvmONwFvSz1BWYAQrrSw=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			o0iigVsmgwmtZfSv3X7hReDNYP5rXblslDnqq2s6UQc=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash2</key>
			<data>
			EXDk4t+7qCpyQkar+q9WHqY9bcK8eyohCwGVtBJhMy8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0h9+vK5K+r8moTsiGBfs6+TM9Qog089afHAy3gbcwDU=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<dict>
			<key>hash2</key>
			<data>
			kg195C3vZLiOn8KeFQUy7DoVuA9VZDpqoBLVn64uGaI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyqlHYuZbpFevVeny9Wdl0rVFgS7szIyssSiCyaaeFM=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash2</key>
			<data>
			U8q/0Ibt9q4O2HMsCdUwITtJdTx8Ljhlx+0aY83fH6s=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<dict>
			<key>hash2</key>
			<data>
			RAOC6nDhZdghbAzsIZgVeq6qPt+MUNTfm/vkUnhmZO4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SqzvIxqBXEJ3U9LJ32hCEXsrH2P16gumQ+gQx6Pdlf4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nmZjZpvFCXrygf4U9aPkNi8VcI7cL5AtA+CY5uUWIL0=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Q4SLFSghL/5EFJPyLg7PNi9J/xpkVVfzro0VQiQHtrY=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ebBVHSZcUnAbN4hRcYq3ttt6++z1Ybc8KVSYhVToD5k=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4hl+kRU4PNNKdAHvYrliObXzSjRzow9Z18oOMRZIa0o=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HqbvCHqKWTzs5GjLAwupqEIYVi9yf5CrMdMe31EOwUA=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			+PMn+5SDj2Vd6RU8CQIt/JYl3T+8Dhp7HImqAzocoNk=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JcpN4a9sv6xynlD3Ri611N5y+HoupUWp2hyrIXB/I8Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yEgZTlCNrK/A/QBjEwNGB6ffC+A9gorPvnNgSbYuQ7Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			v075kvkyPu8t6WRfJ+yP6wJjwWRMctNxq8i7q37e0U0=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			0VjriRpZ7AZZaP/0mMAPMJPhi6LoMB4MhXzL5j24tGs=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			n5XX54YqS1a2btkmvW1iLSplRagn0ZhHJ4tDjVcdQhI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			9dHVR01bZ2BYcHmcZg2ujaVI5AEgH+42skJ864Cy8oY=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			wSU3Ai74GJkae/7UGnbY1q6WL/vA5lEax2Kl0IRef3w=
			</data>
		</dict>
		<key>Frameworks/SDWebImage.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			pYTHjeoanL7Pw1SKtJw/wyiIC2pPamzPk4/ALw/vzPM=
			</data>
		</dict>
		<key>Frameworks/SDWebImage.framework/SDWebImage</key>
		<dict>
			<key>hash2</key>
			<data>
			OjTBZZIe0c/ecEYo+CU33ygseSl564pH08Ohm35Bm0A=
			</data>
		</dict>
		<key>Frameworks/SDWebImage.framework/SDWebImage.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ol5OS4IrOdsEGxw/b8CQi5hN8meqeyJFDBfDDerhuLs=
			</data>
		</dict>
		<key>Frameworks/SDWebImage.framework/SDWebImage.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			A7LHCDOjMaKx79Ef8WjtAqjq39Xn0fvzDuzHUJpK6kc=
			</data>
		</dict>
		<key>Frameworks/SDWebImage.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ZVrFhX2ZctnazMZu6PJpeJRrc2NG6eYPxTMClA8uxDM=
			</data>
		</dict>
		<key>Frameworks/SwiftyGif.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			bdbPs+o4KU3Iwou7u/bAiWsrci+1O62y3RJtCgR2Rgs=
			</data>
		</dict>
		<key>Frameworks/SwiftyGif.framework/SwiftyGif</key>
		<dict>
			<key>hash2</key>
			<data>
			KrZX6ELlBLfNJFgu+y6xHJjk5qY4L3hoPsJDSTDNGyA=
			</data>
		</dict>
		<key>Frameworks/SwiftyGif.framework/SwiftyGif.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Hek0uckc4FbxH5F6ZHrXiQbSzMZr3R3oM2nOAvqzvro=
			</data>
		</dict>
		<key>Frameworks/SwiftyGif.framework/SwiftyGif.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			jWXQtDoSYs/inoM5bACz25T7Eg1E0RiNknNB8UpsIjw=
			</data>
		</dict>
		<key>Frameworks/SwiftyGif.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			V/AhTOf01x2PesGFdfPo7Xxtp2OY7a4UBF6BOsAZrX0=
			</data>
		</dict>
		<key>Frameworks/file_picker.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			rFhPb97jlUem5wNq9lMzMLl+QkFNrS+tv9OXGpnYMgs=
			</data>
		</dict>
		<key>Frameworks/file_picker.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			/L6cz7yKmWjr57d9OJQ1HO2Ieh4M5edwcY0DbDsZDgU=
			</data>
		</dict>
		<key>Frameworks/file_picker.framework/file_picker</key>
		<dict>
			<key>hash2</key>
			<data>
			RjykRoc5VakvoHFxpdeDL6FQzQMtAfL1k4Td3JLqOhE=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			/mTk1NYzaqmVqAmHCuw67ZqfUKT/7jQtMQnnQm2EoiI=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			bQqW60Wl5643oIkO7eRKS2scxnjz2IBOk54Zb6UhWpc=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation</key>
		<dict>
			<key>hash2</key>
			<data>
			i6GSoAD7fsg6eYhfbqX5U08xljNKQvE60qEhEukeoPI=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			yh03P5G69n7NAIFZX5XFGdrN6Q4TxwK4mDyGnwfst/U=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			dLVyxS/2CPdqSGAkQmcI3IRTW4LTJk/HSeyaVe6L7hE=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			HOToAgchiZFTMYed1oHJbTlKcY2h5p6meCLYFBN/ASs=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin</key>
		<dict>
			<key>hash2</key>
			<data>
			xp/bwFahywg69/J52M+UbplSEXZoOUPVl16riJu2daI=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			9v9s4LTNRsS3vbd0J8p9YonPrnqZbHmeIFD1v85mzjw=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			RyJqKWCN8gatChEOav61p3/1dawd+cdr/bLW37P6/tE=
			</data>
		</dict>
		<key>Runner.debug.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			CrKLF4vXLSmPmnwEWeEihaUOcCI/ID4ncHcwOX28U8E=
			</data>
		</dict>
		<key>__preview.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			hOYaNVicMHnEEp++9weW1iG6hXTKTQDNutArkVWEISE=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
