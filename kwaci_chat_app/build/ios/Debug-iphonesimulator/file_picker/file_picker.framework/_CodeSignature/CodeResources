<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FileInfo.h</key>
		<data>
		Sq68fOlqt2EnxavbxXltBur/XPc=
		</data>
		<key>Headers/FilePickerPlugin.h</key>
		<data>
		OwGm1DstOwVBD611OigvAXMcSig=
		</data>
		<key>Headers/FileUtils.h</key>
		<data>
		sDZi/e7YObjZBLESOZdmVKJ1lOg=
		</data>
		<key>Headers/ImageUtils.h</key>
		<data>
		FWmb4dqf0/ibtOwSqp0Sg5XyKu0=
		</data>
		<key>Headers/file_picker-umbrella.h</key>
		<data>
		oOvDrXzCWoANTBcdo93rLdj44i8=
		</data>
		<key>Info.plist</key>
		<data>
		/8RL5EjcMlQshiSbv80ui07Zrl8=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		wL3LRgFgUoE4dQ7d+tAgsxG8PRY=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FileInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			VB3TsWr4fWzdlU23ufNj4/+AZDfGbxFpRBm7v6efCaI=
			</data>
		</dict>
		<key>Headers/FilePickerPlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			jCp8AueijV/jI8OgsNJqbKDlilB8Y31cgwh11WqBL0k=
			</data>
		</dict>
		<key>Headers/FileUtils.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Y+sFnCCgCKV5QX+AyruigNU3cdDO6A1OzRbvUrTEHMQ=
			</data>
		</dict>
		<key>Headers/ImageUtils.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yr5wVJ8Je912D78+Zueqyi4G0dB7+XpTdlV28uN3AVU=
			</data>
		</dict>
		<key>Headers/file_picker-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			6dxbIpeRf7MZ37tXaOKF/E3kzlawupDJu/4vFme+9wA=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			NN7URogrInyz3nmt8l+00AZ98o7MMdRwvzbeCelAoWM=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
