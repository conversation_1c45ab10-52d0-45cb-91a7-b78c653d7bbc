<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/path_provider_foundation-Swift.h</key>
		<data>
		nSUpQZozhKEJ7mnz00N8I8jIBGY=
		</data>
		<key>Headers/path_provider_foundation-umbrella.h</key>
		<data>
		HLPUH7a1+Uud1CEb8ZVbUJlVow0=
		</data>
		<key>Info.plist</key>
		<data>
		Bf1UZJU/fB1U3eFVZR/FS9EgOO8=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		4ou7sfZJMUritXkWYrNLe8ROvJ8=
		</data>
		<key>Modules/path_provider_foundation.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		Gyfg35+GUByfzvYM1FxtyEAyE6Q=
		</data>
		<key>Modules/path_provider_foundation.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		Yyxh943mFcE8p67dsmpfk3nqcrk=
		</data>
		<key>Modules/path_provider_foundation.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/path_provider_foundation.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		Yfeq2/CER3vztzXRo5n3gxmTvhY=
		</data>
		<key>Modules/path_provider_foundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		4yTVe3RNUqm57t4uMxcwHKiKh5s=
		</data>
		<key>Modules/path_provider_foundation.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/path_provider_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		irTA9myzFCXibLhAetbvSKJFKXY=
		</data>
		<key>Modules/path_provider_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		kteUWQq4F9i8rqEY7dKz8U5ycJ8=
		</data>
		<key>path_provider_foundation_privacy.bundle/Info.plist</key>
		<data>
		0k4F/m5fStSsN0EWvqvYpqxNy8g=
		</data>
		<key>path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/path_provider_foundation-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XkPgtEZE6v7/9fjOInHmDMyRYXD7yK2sahXKKYlstHM=
			</data>
		</dict>
		<key>Headers/path_provider_foundation-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			zQc9a/y7t7MqxTgSSmpfImDb6DSiKF26gJFqk0CuTBE=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			GljPXu0mFl3W6UpwpsufQgltdn4I8vCubbMMs3ACbiw=
			</data>
		</dict>
		<key>Modules/path_provider_foundation.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			p3WiUbqZwrhDx97gYwGAgPZu1kJUTQFYuN/Fszmvik8=
			</data>
		</dict>
		<key>Modules/path_provider_foundation.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			l9K3lq8xyHHuh80G5kNvTEDIRG4tABJo/zycShOW/tw=
			</data>
		</dict>
		<key>Modules/path_provider_foundation.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/path_provider_foundation.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			5SQbvKgYKJYI4j2mXqKMxF/BXkxHZ5SCtVEFHL9iPbc=
			</data>
		</dict>
		<key>Modules/path_provider_foundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			kVQw1qBpWecsBHCouiC8LelPx9RJ7r5DITAW6KiAz9c=
			</data>
		</dict>
		<key>Modules/path_provider_foundation.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/path_provider_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			OXqwnWDkBs5ct3ObkGMeyk8vI+aVQQ1rwOSqhvfe6eg=
			</data>
		</dict>
		<key>Modules/path_provider_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			yopB45Me9PYgEnPvnMdRu9iP0yG/fqbQjJpp98igkCE=
			</data>
		</dict>
		<key>path_provider_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			yh03P5G69n7NAIFZX5XFGdrN6Q4TxwK4mDyGnwfst/U=
			</data>
		</dict>
		<key>path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
