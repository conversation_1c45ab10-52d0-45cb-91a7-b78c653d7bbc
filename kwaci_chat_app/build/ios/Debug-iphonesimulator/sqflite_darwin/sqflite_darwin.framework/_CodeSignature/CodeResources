<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/SqfliteImportPublic.h</key>
		<data>
		hZiOds+Ex7aPD3CL2x+a9GJQosg=
		</data>
		<key>Headers/SqflitePluginPublic.h</key>
		<data>
		9XzIMcuPfmizIkNOn/X69rBY7yU=
		</data>
		<key>Headers/sqflite_darwin-umbrella.h</key>
		<data>
		BhhY+UbPtvYkZfs+lBROeQgfC4E=
		</data>
		<key>Info.plist</key>
		<data>
		PO72UreZXG/QWV+fN/HViQJUgD8=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		zU5h1VWgEIvj9oJT/rHlRd4Ug7E=
		</data>
		<key>sqflite_darwin_privacy.bundle/Info.plist</key>
		<data>
		zxcuJuY27PHPW0cRUIvmtipcFSU=
		</data>
		<key>sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YIiJ5tHvqBeSpBm2mcfVZdaGz3E=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/SqfliteImportPublic.h</key>
		<dict>
			<key>hash2</key>
			<data>
			1amTn6ajlIhC0Kvfa0IPyl4ifgFJ0YcPoZCfxqn0HLA=
			</data>
		</dict>
		<key>Headers/SqflitePluginPublic.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Z3iHtqdw1Gg2KizCYJMP+HZYreM/8+kcE6Y7epCvhpc=
			</data>
		</dict>
		<key>Headers/sqflite_darwin-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			NtTlRc3zt1eSdmMxJAwfGY53wQ+WpoUVTelB7pEC4J8=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			yjD4gwrTLnAHDbjYUx/sI+631aXElwW6ghJJ9FcU90Y=
			</data>
		</dict>
		<key>sqflite_darwin_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			9v9s4LTNRsS3vbd0J8p9YonPrnqZbHmeIFD1v85mzjw=
			</data>
		</dict>
		<key>sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			RyJqKWCN8gatChEOav61p3/1dawd+cdr/bLW37P6/tE=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
