<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/SwiftyGif-Swift.h</key>
		<data>
		NgNjz/u6tC/S0OvrpqV3WWRbpYY=
		</data>
		<key>Headers/SwiftyGif-umbrella.h</key>
		<data>
		bS6Q1h+ZrHelbYNBvZbwcjvLthM=
		</data>
		<key>Headers/SwiftyGif.h</key>
		<data>
		aS1yn4ira5MN/J3KwkZi1vpT3YU=
		</data>
		<key>Info.plist</key>
		<data>
		98mij3q5W0C/IWWDOmow+a0+ZNU=
		</data>
		<key>Modules/SwiftyGif.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		BGq7X+qwZa61d31N3ewEbVGP+qM=
		</data>
		<key>Modules/SwiftyGif.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		eogi3m0HX1QSiA4032UDYqWOcUw=
		</data>
		<key>Modules/SwiftyGif.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/SwiftyGif.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		GbWtswUceiT/Lr7tTCR/bTmoRZg=
		</data>
		<key>Modules/SwiftyGif.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		L5NOxGK2oz/Ltl4YoeAFDJhSArA=
		</data>
		<key>Modules/SwiftyGif.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/SwiftyGif.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		GgDIrBvUdetHBIK3VacyQTOZisw=
		</data>
		<key>Modules/SwiftyGif.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		HRNjvxGaduia6MV9giPX4Mpk/9g=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		8+1d7wACscKuQ9hMQDHR58QgPQw=
		</data>
		<key>SwiftyGif.bundle/Info.plist</key>
		<data>
		QihogB2Uh6sxZH96UmF/U0pyDHQ=
		</data>
		<key>SwiftyGif.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		i3YpomTpJHpRGpdt+oGaesmknBg=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/SwiftyGif-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			NgNjz/u6tC/S0OvrpqV3WWRbpYY=
			</data>
			<key>hash2</key>
			<data>
			AM3QgS2dR8dwFWdOBdvoJkwz5+dWhQLNhjllY+EmzNo=
			</data>
		</dict>
		<key>Headers/SwiftyGif-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			bS6Q1h+ZrHelbYNBvZbwcjvLthM=
			</data>
			<key>hash2</key>
			<data>
			Opubb27jn/eCefJAm5HVaXzjxFf12LnTssuZ/JUbDP8=
			</data>
		</dict>
		<key>Headers/SwiftyGif.h</key>
		<dict>
			<key>hash</key>
			<data>
			aS1yn4ira5MN/J3KwkZi1vpT3YU=
			</data>
			<key>hash2</key>
			<data>
			2Ty6lQeyn/Oq7Yu9oSJQeODJVU0htDeiml1VaMBYDFg=
			</data>
		</dict>
		<key>Modules/SwiftyGif.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			BGq7X+qwZa61d31N3ewEbVGP+qM=
			</data>
			<key>hash2</key>
			<data>
			0MuoysRfPLTtqh379F4bOI6rvvSxzvpayrmu9/HOJro=
			</data>
		</dict>
		<key>Modules/SwiftyGif.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			eogi3m0HX1QSiA4032UDYqWOcUw=
			</data>
			<key>hash2</key>
			<data>
			bo7IaaAm/mRoXU4TnMZsfRl9RJ32Xu8m1kKykdC5s1M=
			</data>
		</dict>
		<key>Modules/SwiftyGif.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/SwiftyGif.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			GbWtswUceiT/Lr7tTCR/bTmoRZg=
			</data>
			<key>hash2</key>
			<data>
			1upsCuzF2r0C1oxFOVW/IPCpl88G/Alnyvb10T80/+o=
			</data>
		</dict>
		<key>Modules/SwiftyGif.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			L5NOxGK2oz/Ltl4YoeAFDJhSArA=
			</data>
			<key>hash2</key>
			<data>
			2l4ph8/CYE5+U0hyI4y+QmYqCQXzsSS1MW8UsV60xBY=
			</data>
		</dict>
		<key>Modules/SwiftyGif.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/SwiftyGif.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			GgDIrBvUdetHBIK3VacyQTOZisw=
			</data>
			<key>hash2</key>
			<data>
			FPhCVjyvF0uD6nITjsPNQDiH9N7MsvYRhzDpopyONnw=
			</data>
		</dict>
		<key>Modules/SwiftyGif.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			HRNjvxGaduia6MV9giPX4Mpk/9g=
			</data>
			<key>hash2</key>
			<data>
			XOowA2L+UZq9pjueYbaHLpl1RT9WgQygX5RD/qlK7hw=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			8+1d7wACscKuQ9hMQDHR58QgPQw=
			</data>
			<key>hash2</key>
			<data>
			K/92O2tqPNF7RPSRamjahJvhEtT7kX/9ve21rVOqq/w=
			</data>
		</dict>
		<key>SwiftyGif.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			QihogB2Uh6sxZH96UmF/U0pyDHQ=
			</data>
			<key>hash2</key>
			<data>
			Hek0uckc4FbxH5F6ZHrXiQbSzMZr3R3oM2nOAvqzvro=
			</data>
		</dict>
		<key>SwiftyGif.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			i3YpomTpJHpRGpdt+oGaesmknBg=
			</data>
			<key>hash2</key>
			<data>
			jWXQtDoSYs/inoM5bACz25T7Eg1E0RiNknNB8UpsIjw=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
