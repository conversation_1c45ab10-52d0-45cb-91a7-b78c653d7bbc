<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/NSButton+WebCache.h</key>
		<data>
		kerzpTzut4Y9DK30kKBUAjFRcpU=
		</data>
		<key>Headers/NSData+ImageContentType.h</key>
		<data>
		4Oy+WYBUGO9Wg9M02ey0X0PZNeE=
		</data>
		<key>Headers/NSImage+Compatibility.h</key>
		<data>
		IgoqKBotwi7ajNcN7zWtIVYlWkU=
		</data>
		<key>Headers/SDAnimatedImage.h</key>
		<data>
		wEwGKw7o6Y+8IgW5NGlNq1ui0pY=
		</data>
		<key>Headers/SDAnimatedImagePlayer.h</key>
		<data>
		umuBgniyctLvENMsQ/M26tordQ8=
		</data>
		<key>Headers/SDAnimatedImageRep.h</key>
		<data>
		Yt8eKC+fpPiSOgGyGc6xQqnu3KM=
		</data>
		<key>Headers/SDAnimatedImageView+WebCache.h</key>
		<data>
		489fmmZp9/CPyPoO2w6Oo2wI2ZA=
		</data>
		<key>Headers/SDAnimatedImageView.h</key>
		<data>
		b9Qe4o6IRmTexrB4Bk9ZxiGIj1g=
		</data>
		<key>Headers/SDCallbackQueue.h</key>
		<data>
		3Jd05cBGFzySvWu5n1yUNUCM71Y=
		</data>
		<key>Headers/SDDiskCache.h</key>
		<data>
		+Stln0lIE7wnn6SMSVFIVZXHJN0=
		</data>
		<key>Headers/SDGraphicsImageRenderer.h</key>
		<data>
		3BCpUmaL8N+yMIwHGvXg5k4W4GA=
		</data>
		<key>Headers/SDImageAPNGCoder.h</key>
		<data>
		QoaVXPzM3Wqh4fJ5zyVSURhUvuo=
		</data>
		<key>Headers/SDImageAWebPCoder.h</key>
		<data>
		z2cOz0U8ke7rzwQS0qqlUA9WNIo=
		</data>
		<key>Headers/SDImageCache.h</key>
		<data>
		GfuoxnwQZTveUzO0wpQmQWnI4ok=
		</data>
		<key>Headers/SDImageCacheConfig.h</key>
		<data>
		a5+mFkpBdDsOvMWVmjkdSzi242s=
		</data>
		<key>Headers/SDImageCacheDefine.h</key>
		<data>
		JGUeQAVQAzsI5AM0JZDIgYUQJg0=
		</data>
		<key>Headers/SDImageCachesManager.h</key>
		<data>
		EEi3XPzSfUrV7t4eX5+nGYb78qA=
		</data>
		<key>Headers/SDImageCoder.h</key>
		<data>
		kv1EltSYfQOsKx1qHgnAcThKoOo=
		</data>
		<key>Headers/SDImageCoderHelper.h</key>
		<data>
		gfBqViNN62nIMB0VsHqMBq86z/I=
		</data>
		<key>Headers/SDImageCodersManager.h</key>
		<data>
		kWuSCn1JQwFRndhsWspbX8YcS04=
		</data>
		<key>Headers/SDImageFrame.h</key>
		<data>
		OMHYfH8iLCcQLgZ4YyiDJ9W9S7c=
		</data>
		<key>Headers/SDImageGIFCoder.h</key>
		<data>
		gic8eBd8dd3Ycum3hGKDe2YhdA0=
		</data>
		<key>Headers/SDImageGraphics.h</key>
		<data>
		W2mWg+eO1NNZoHIb3Z22AyTfuuc=
		</data>
		<key>Headers/SDImageHEICCoder.h</key>
		<data>
		kQrjSojz9EH8bowedd6T1E7tlMI=
		</data>
		<key>Headers/SDImageIOAnimatedCoder.h</key>
		<data>
		1Hn503ITdXoB8soIwOtZvE5FRJw=
		</data>
		<key>Headers/SDImageIOCoder.h</key>
		<data>
		1Vlc9rQwx9IqgFQpVEy50XaLxCM=
		</data>
		<key>Headers/SDImageLoader.h</key>
		<data>
		5BXL40fPBJ5oGsP1iM0QnGdbcPs=
		</data>
		<key>Headers/SDImageLoadersManager.h</key>
		<data>
		m3fBhKWZNfRLURmQZcaWZATlRBQ=
		</data>
		<key>Headers/SDImageTransformer.h</key>
		<data>
		5yjJNtG6khRxRsYDw2lv9zBAJg8=
		</data>
		<key>Headers/SDMemoryCache.h</key>
		<data>
		Kk9HCkkkruGk5kLZSlD0/0rK6po=
		</data>
		<key>Headers/SDWebImage-umbrella.h</key>
		<data>
		oBxQsZbRQrJt/Ev0/IKSBVpMrfk=
		</data>
		<key>Headers/SDWebImage.h</key>
		<data>
		HCkBNG4f+fSdK4HG8fGozZdNiL0=
		</data>
		<key>Headers/SDWebImageCacheKeyFilter.h</key>
		<data>
		aYH0yhjC5zF2QCLSj2O+NMz0WQg=
		</data>
		<key>Headers/SDWebImageCacheSerializer.h</key>
		<data>
		3Z+TBpDAz0HbWFqUjv/rMeJeB8Q=
		</data>
		<key>Headers/SDWebImageCompat.h</key>
		<data>
		N5ShOvLw2XgdvUTw0Rdh/qaZMQQ=
		</data>
		<key>Headers/SDWebImageDefine.h</key>
		<data>
		40NWNA968zhYg4gODzG/o3o6PNI=
		</data>
		<key>Headers/SDWebImageDownloader.h</key>
		<data>
		6ubrD4w9QVuJfHVkUzUJTCcgn2w=
		</data>
		<key>Headers/SDWebImageDownloaderConfig.h</key>
		<data>
		dn8/qqvBQLDAhdDT8drLkiHywII=
		</data>
		<key>Headers/SDWebImageDownloaderDecryptor.h</key>
		<data>
		+wmSvaiWPMkVMzkNlMQDpWa6kUY=
		</data>
		<key>Headers/SDWebImageDownloaderOperation.h</key>
		<data>
		UJpmtCm9LOMFsGbPOVhSbh8/vLs=
		</data>
		<key>Headers/SDWebImageDownloaderRequestModifier.h</key>
		<data>
		Qy4yjYxFiQYtKZSnS84bT4Z7G1c=
		</data>
		<key>Headers/SDWebImageDownloaderResponseModifier.h</key>
		<data>
		c6I+X9Wwmw8vcx6ZHlDckWOzI+4=
		</data>
		<key>Headers/SDWebImageError.h</key>
		<data>
		FC3IAo7pR4h93t6QeXlmtOHdZvI=
		</data>
		<key>Headers/SDWebImageIndicator.h</key>
		<data>
		d4goT+IzohYDq38BvPeR7BTc5iw=
		</data>
		<key>Headers/SDWebImageManager.h</key>
		<data>
		xiHH/HCr0tx9VoY3F1eceSzc0rA=
		</data>
		<key>Headers/SDWebImageOperation.h</key>
		<data>
		Hitkjp5wpW1nlkxBEFxklBLznXY=
		</data>
		<key>Headers/SDWebImageOptionsProcessor.h</key>
		<data>
		+tN4ufzzcshfZfddoW2fki5pPcY=
		</data>
		<key>Headers/SDWebImagePrefetcher.h</key>
		<data>
		xMh1Gu+HBzvb1MOuMY5Vt7zYm0M=
		</data>
		<key>Headers/SDWebImageTransition.h</key>
		<data>
		T/5y2vKBxqAbm7KWUccowjKnp6Y=
		</data>
		<key>Headers/UIButton+WebCache.h</key>
		<data>
		CQ/r9KIZ4rC9OLCgZ8IlI6yO1C0=
		</data>
		<key>Headers/UIImage+ExtendedCacheData.h</key>
		<data>
		fCfC7/RKNYGmDHfXG9+Qg8uB5i4=
		</data>
		<key>Headers/UIImage+ForceDecode.h</key>
		<data>
		U2UQq4eLIdNIW/q4Txp2/bD4Now=
		</data>
		<key>Headers/UIImage+GIF.h</key>
		<data>
		YQ1NHxHIECTuq127MqEt8ssMl2E=
		</data>
		<key>Headers/UIImage+MemoryCacheCost.h</key>
		<data>
		LzgPQ2O81SwnuvHsClmNlVmadtw=
		</data>
		<key>Headers/UIImage+Metadata.h</key>
		<data>
		PrI9v6timUL2+1eymDpTBARKVZY=
		</data>
		<key>Headers/UIImage+MultiFormat.h</key>
		<data>
		tJOSxpAbfgpEkLSjOKDTQBvpAGI=
		</data>
		<key>Headers/UIImage+Transform.h</key>
		<data>
		Rg5hx4t/qdnlNZLUCFaziLkwocU=
		</data>
		<key>Headers/UIImageView+HighlightedWebCache.h</key>
		<data>
		V4leacX4kIZjjFmKNWTOcxEWbRI=
		</data>
		<key>Headers/UIImageView+WebCache.h</key>
		<data>
		kJScMN4XyKqi6oIV8vdvgX9QKs0=
		</data>
		<key>Headers/UIView+WebCache.h</key>
		<data>
		6s86639L67mb1Y1aTunR3oS9C+U=
		</data>
		<key>Headers/UIView+WebCacheOperation.h</key>
		<data>
		gWoTb4BsM6UfrNyXSanUM+AQLbU=
		</data>
		<key>Headers/UIView+WebCacheState.h</key>
		<data>
		771hZgr2q0NJeFs0oox1EMSX4mM=
		</data>
		<key>Info.plist</key>
		<data>
		BJcJ1dKfVau6iwBEEPUshVJFzgw=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		6irOkF2Qx//aDEBN8hX0lEFShtg=
		</data>
		<key>PrivateHeaders/NSBezierPath+SDRoundedCorners.h</key>
		<data>
		hVf10BOSXAmUGUC+yd0ztOAH4BY=
		</data>
		<key>PrivateHeaders/SDAssociatedObject.h</key>
		<data>
		vGTBkYhQJTlD6ZChh2vQXaWhC0U=
		</data>
		<key>PrivateHeaders/SDAsyncBlockOperation.h</key>
		<data>
		snzjaBGyE/z0cdUS9+aJRKrxDnI=
		</data>
		<key>PrivateHeaders/SDDeviceHelper.h</key>
		<data>
		n3vn/m5X9Sy1i9PlwGlLpJgwiQU=
		</data>
		<key>PrivateHeaders/SDDisplayLink.h</key>
		<data>
		ylFFSzJbR+eq7L+qXMeezb4OXSs=
		</data>
		<key>PrivateHeaders/SDFileAttributeHelper.h</key>
		<data>
		sb2CUGqQfxIv2pbTDH1eH9Tx/lc=
		</data>
		<key>PrivateHeaders/SDImageAssetManager.h</key>
		<data>
		8EwarZm30x7czN6Nn9NHYxVbepY=
		</data>
		<key>PrivateHeaders/SDImageCachesManagerOperation.h</key>
		<data>
		FO5aLhjBQ2flj+39N/ihTId+Yx4=
		</data>
		<key>PrivateHeaders/SDImageFramePool.h</key>
		<data>
		p4Uj8gNLlVyG4aMmYvdqi2/YJHI=
		</data>
		<key>PrivateHeaders/SDImageIOAnimatedCoderInternal.h</key>
		<data>
		DF/qdUa8vN7/EOif7GlLIoLfpC0=
		</data>
		<key>PrivateHeaders/SDInternalMacros.h</key>
		<data>
		Lns/aWnsokOaPUSXXvUqGIetRr4=
		</data>
		<key>PrivateHeaders/SDWeakProxy.h</key>
		<data>
		sDlUvBrJYVMQKR39qPnbjcghuhk=
		</data>
		<key>PrivateHeaders/SDWebImageTransitionInternal.h</key>
		<data>
		IofQDwABOOMR5dycmLjaLz5cOnU=
		</data>
		<key>PrivateHeaders/SDmetamacros.h</key>
		<data>
		/UCzmRZdtlxqZdIiubMLZeAL5oo=
		</data>
		<key>PrivateHeaders/UIColor+SDHexString.h</key>
		<data>
		bvb2vO3YtXbNaIvieoIZQFZg/Vs=
		</data>
		<key>SDWebImage.bundle/Info.plist</key>
		<data>
		fDgvkq3CZBiFRTmXisIrYs47Vz8=
		</data>
		<key>SDWebImage.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PFHYbs0V3eUFDWQyYQcwEetuqEk=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/NSButton+WebCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			kerzpTzut4Y9DK30kKBUAjFRcpU=
			</data>
			<key>hash2</key>
			<data>
			yU2vQj+20+pAJWBFNDuTfYWPe8OCkUpiuWgKxhh7rjI=
			</data>
		</dict>
		<key>Headers/NSData+ImageContentType.h</key>
		<dict>
			<key>hash</key>
			<data>
			4Oy+WYBUGO9Wg9M02ey0X0PZNeE=
			</data>
			<key>hash2</key>
			<data>
			FlEndsFPP+RVrg+p7+A61uIZwgNx2LotYz+nxrSUxA8=
			</data>
		</dict>
		<key>Headers/NSImage+Compatibility.h</key>
		<dict>
			<key>hash</key>
			<data>
			IgoqKBotwi7ajNcN7zWtIVYlWkU=
			</data>
			<key>hash2</key>
			<data>
			GRpU4OYeo/l4z55bHC3/Tm5xcKdOHksEymmz+v9i68k=
			</data>
		</dict>
		<key>Headers/SDAnimatedImage.h</key>
		<dict>
			<key>hash</key>
			<data>
			wEwGKw7o6Y+8IgW5NGlNq1ui0pY=
			</data>
			<key>hash2</key>
			<data>
			mEWk4+7FtipDtSAX9CdMwmNJZtSbdKG6GUvzybUrxSY=
			</data>
		</dict>
		<key>Headers/SDAnimatedImagePlayer.h</key>
		<dict>
			<key>hash</key>
			<data>
			umuBgniyctLvENMsQ/M26tordQ8=
			</data>
			<key>hash2</key>
			<data>
			7SD/R/rOdLs8EBPo0GzFBDVndbMTDD4KBvlIa8hx3j4=
			</data>
		</dict>
		<key>Headers/SDAnimatedImageRep.h</key>
		<dict>
			<key>hash</key>
			<data>
			Yt8eKC+fpPiSOgGyGc6xQqnu3KM=
			</data>
			<key>hash2</key>
			<data>
			rXU1YmGNqrf7EJd5bPGvA+zM9K0tcThTsVuSPob0hdU=
			</data>
		</dict>
		<key>Headers/SDAnimatedImageView+WebCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			489fmmZp9/CPyPoO2w6Oo2wI2ZA=
			</data>
			<key>hash2</key>
			<data>
			c4F0igYmbpW8pKWYoSurnGBCipQHW7+Zk7PhRfRmzEI=
			</data>
		</dict>
		<key>Headers/SDAnimatedImageView.h</key>
		<dict>
			<key>hash</key>
			<data>
			b9Qe4o6IRmTexrB4Bk9ZxiGIj1g=
			</data>
			<key>hash2</key>
			<data>
			jdxEAGT6Iu3pdEow7Pm6HNJbky8XIVp66b3VTn0bkL4=
			</data>
		</dict>
		<key>Headers/SDCallbackQueue.h</key>
		<dict>
			<key>hash</key>
			<data>
			3Jd05cBGFzySvWu5n1yUNUCM71Y=
			</data>
			<key>hash2</key>
			<data>
			i861wg7ASwvkyMnp/YIqsR8kvifQHJ/WK/Uv/iX+zZU=
			</data>
		</dict>
		<key>Headers/SDDiskCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			+Stln0lIE7wnn6SMSVFIVZXHJN0=
			</data>
			<key>hash2</key>
			<data>
			mVkH0pCyG12ArH9oUmS0+yx4+UTvHhHCEn3NRoNxs0w=
			</data>
		</dict>
		<key>Headers/SDGraphicsImageRenderer.h</key>
		<dict>
			<key>hash</key>
			<data>
			3BCpUmaL8N+yMIwHGvXg5k4W4GA=
			</data>
			<key>hash2</key>
			<data>
			FGpZ9ZuatHzrhrgCRYdysDvZwxamKpQlFdwsBtWKAeo=
			</data>
		</dict>
		<key>Headers/SDImageAPNGCoder.h</key>
		<dict>
			<key>hash</key>
			<data>
			QoaVXPzM3Wqh4fJ5zyVSURhUvuo=
			</data>
			<key>hash2</key>
			<data>
			BmW8I1K0wbkaUU5qUxht8LFF0rMHB80qravT/B2y7wQ=
			</data>
		</dict>
		<key>Headers/SDImageAWebPCoder.h</key>
		<dict>
			<key>hash</key>
			<data>
			z2cOz0U8ke7rzwQS0qqlUA9WNIo=
			</data>
			<key>hash2</key>
			<data>
			qNekBkSJ1CJcnSEWcr9hU2plkh9J+8YcCds/1Vw0IHQ=
			</data>
		</dict>
		<key>Headers/SDImageCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			GfuoxnwQZTveUzO0wpQmQWnI4ok=
			</data>
			<key>hash2</key>
			<data>
			T7WTmmq84N41e7wAdyf2S2ClNKghMkfukyLhA57mmmo=
			</data>
		</dict>
		<key>Headers/SDImageCacheConfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			a5+mFkpBdDsOvMWVmjkdSzi242s=
			</data>
			<key>hash2</key>
			<data>
			8NN1VAT2PWmgt0LNYJOdoFqBC6EYsP4brEjcxjP4LzQ=
			</data>
		</dict>
		<key>Headers/SDImageCacheDefine.h</key>
		<dict>
			<key>hash</key>
			<data>
			JGUeQAVQAzsI5AM0JZDIgYUQJg0=
			</data>
			<key>hash2</key>
			<data>
			Wi1D/1DYt5p7FAdtmQg44ZTZyyGPZMYkbKKpooE4bZE=
			</data>
		</dict>
		<key>Headers/SDImageCachesManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			EEi3XPzSfUrV7t4eX5+nGYb78qA=
			</data>
			<key>hash2</key>
			<data>
			igzO9GqQ6yyT4LjTjlmsP+X6wy87ckegAwVi+0jCEQ4=
			</data>
		</dict>
		<key>Headers/SDImageCoder.h</key>
		<dict>
			<key>hash</key>
			<data>
			kv1EltSYfQOsKx1qHgnAcThKoOo=
			</data>
			<key>hash2</key>
			<data>
			nXlNT0UTuPwXC8EE1TKB6a7LxQhTzbIT3uQk/Vwu5aw=
			</data>
		</dict>
		<key>Headers/SDImageCoderHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			gfBqViNN62nIMB0VsHqMBq86z/I=
			</data>
			<key>hash2</key>
			<data>
			+uMhCBCZc1kVNAOLrE3UVIB/M9ebzx1b3Q2hCzz4gLE=
			</data>
		</dict>
		<key>Headers/SDImageCodersManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			kWuSCn1JQwFRndhsWspbX8YcS04=
			</data>
			<key>hash2</key>
			<data>
			MpSFV9SfX6arHPiClt+/gNjg1IFhSrGQAXRztUCuZHM=
			</data>
		</dict>
		<key>Headers/SDImageFrame.h</key>
		<dict>
			<key>hash</key>
			<data>
			OMHYfH8iLCcQLgZ4YyiDJ9W9S7c=
			</data>
			<key>hash2</key>
			<data>
			6q5axlkCIoLdNPTgEtPNPyw1DF+5l8zfFPUqljPimsk=
			</data>
		</dict>
		<key>Headers/SDImageGIFCoder.h</key>
		<dict>
			<key>hash</key>
			<data>
			gic8eBd8dd3Ycum3hGKDe2YhdA0=
			</data>
			<key>hash2</key>
			<data>
			Rl5ROAfuQFIllM3APjTC4ScpBB3LzNX+3VSSu100ss0=
			</data>
		</dict>
		<key>Headers/SDImageGraphics.h</key>
		<dict>
			<key>hash</key>
			<data>
			W2mWg+eO1NNZoHIb3Z22AyTfuuc=
			</data>
			<key>hash2</key>
			<data>
			pM0fpHGZ+kPKKcerF6edVEiWftOGR5l5p1lUKUnxutk=
			</data>
		</dict>
		<key>Headers/SDImageHEICCoder.h</key>
		<dict>
			<key>hash</key>
			<data>
			kQrjSojz9EH8bowedd6T1E7tlMI=
			</data>
			<key>hash2</key>
			<data>
			WeEw9x868k0NoVrEPsNaFVZNnRWK4wYEv7Oqf0x//j0=
			</data>
		</dict>
		<key>Headers/SDImageIOAnimatedCoder.h</key>
		<dict>
			<key>hash</key>
			<data>
			1Hn503ITdXoB8soIwOtZvE5FRJw=
			</data>
			<key>hash2</key>
			<data>
			k1F4NVjmQNaOC73v8Gt1Xm9P7t+wcW8V+o57HVXG7C0=
			</data>
		</dict>
		<key>Headers/SDImageIOCoder.h</key>
		<dict>
			<key>hash</key>
			<data>
			1Vlc9rQwx9IqgFQpVEy50XaLxCM=
			</data>
			<key>hash2</key>
			<data>
			Ygs8T/H3FS87kxd2O3G/4WcOxXi/jzuXo7D9GakQp/s=
			</data>
		</dict>
		<key>Headers/SDImageLoader.h</key>
		<dict>
			<key>hash</key>
			<data>
			5BXL40fPBJ5oGsP1iM0QnGdbcPs=
			</data>
			<key>hash2</key>
			<data>
			k5s0BDpNxMcrxoMLndIa1xMYjYlsMPJXhI0DyN3DAvM=
			</data>
		</dict>
		<key>Headers/SDImageLoadersManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			m3fBhKWZNfRLURmQZcaWZATlRBQ=
			</data>
			<key>hash2</key>
			<data>
			pi+kOUzH/FizlRV0/Uv15k2jJuRoHkURndQyyP66U8c=
			</data>
		</dict>
		<key>Headers/SDImageTransformer.h</key>
		<dict>
			<key>hash</key>
			<data>
			5yjJNtG6khRxRsYDw2lv9zBAJg8=
			</data>
			<key>hash2</key>
			<data>
			ZmY/KYJi87ZCW/wLtCI3t2MQ/HPPHDQ0s0KvhIfPtWo=
			</data>
		</dict>
		<key>Headers/SDMemoryCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			Kk9HCkkkruGk5kLZSlD0/0rK6po=
			</data>
			<key>hash2</key>
			<data>
			kETUHE9yf4Vaxm133lqriAzmP3oLT/v4PWBLZm45Wac=
			</data>
		</dict>
		<key>Headers/SDWebImage-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			oBxQsZbRQrJt/Ev0/IKSBVpMrfk=
			</data>
			<key>hash2</key>
			<data>
			lG3Rv5uikmQqaVNcjF3kM0QDvnPLR+iR0bXWpnZXhas=
			</data>
		</dict>
		<key>Headers/SDWebImage.h</key>
		<dict>
			<key>hash</key>
			<data>
			HCkBNG4f+fSdK4HG8fGozZdNiL0=
			</data>
			<key>hash2</key>
			<data>
			RzW3faycWKnFoi8a02SG2OFaUS3A1bYy4WPXRdprCKA=
			</data>
		</dict>
		<key>Headers/SDWebImageCacheKeyFilter.h</key>
		<dict>
			<key>hash</key>
			<data>
			aYH0yhjC5zF2QCLSj2O+NMz0WQg=
			</data>
			<key>hash2</key>
			<data>
			XbPrloD/gfU7g5lRm91x2vMzVvpglaaL7a2WAnW+cNg=
			</data>
		</dict>
		<key>Headers/SDWebImageCacheSerializer.h</key>
		<dict>
			<key>hash</key>
			<data>
			3Z+TBpDAz0HbWFqUjv/rMeJeB8Q=
			</data>
			<key>hash2</key>
			<data>
			VMTn0QvKDVDRkKdFHwbcrjvicIwKj0MKorGrJcQlFaE=
			</data>
		</dict>
		<key>Headers/SDWebImageCompat.h</key>
		<dict>
			<key>hash</key>
			<data>
			N5ShOvLw2XgdvUTw0Rdh/qaZMQQ=
			</data>
			<key>hash2</key>
			<data>
			MT6U9TzBh29vH7M6UuuM6lxhP06ugEGss9T9a9ePW9w=
			</data>
		</dict>
		<key>Headers/SDWebImageDefine.h</key>
		<dict>
			<key>hash</key>
			<data>
			40NWNA968zhYg4gODzG/o3o6PNI=
			</data>
			<key>hash2</key>
			<data>
			x7qmMoKH81TgPJnpqi3wZw5YvcCOMJChJSv/9aTKb1g=
			</data>
		</dict>
		<key>Headers/SDWebImageDownloader.h</key>
		<dict>
			<key>hash</key>
			<data>
			6ubrD4w9QVuJfHVkUzUJTCcgn2w=
			</data>
			<key>hash2</key>
			<data>
			YtMMZzUjWHilaNYwxcjHrNBVsHTM+TpCZRogGYXlQ5k=
			</data>
		</dict>
		<key>Headers/SDWebImageDownloaderConfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			dn8/qqvBQLDAhdDT8drLkiHywII=
			</data>
			<key>hash2</key>
			<data>
			cx9xDgDgcwrzA7WZzodGHISNOlinC52JA8UlxyiZcKI=
			</data>
		</dict>
		<key>Headers/SDWebImageDownloaderDecryptor.h</key>
		<dict>
			<key>hash</key>
			<data>
			+wmSvaiWPMkVMzkNlMQDpWa6kUY=
			</data>
			<key>hash2</key>
			<data>
			FKBGaywdBkdBkbqEAH8tEty7WspySouebsR8EWFh1tw=
			</data>
		</dict>
		<key>Headers/SDWebImageDownloaderOperation.h</key>
		<dict>
			<key>hash</key>
			<data>
			UJpmtCm9LOMFsGbPOVhSbh8/vLs=
			</data>
			<key>hash2</key>
			<data>
			wCcSP4ADZQp7SG2nO3pESqKyjfn25kObeEFJKihWD6c=
			</data>
		</dict>
		<key>Headers/SDWebImageDownloaderRequestModifier.h</key>
		<dict>
			<key>hash</key>
			<data>
			Qy4yjYxFiQYtKZSnS84bT4Z7G1c=
			</data>
			<key>hash2</key>
			<data>
			ely2wwcrB0UgYuT8cK7UkrjFFeB6qEKdg7Mcas1xvkg=
			</data>
		</dict>
		<key>Headers/SDWebImageDownloaderResponseModifier.h</key>
		<dict>
			<key>hash</key>
			<data>
			c6I+X9Wwmw8vcx6ZHlDckWOzI+4=
			</data>
			<key>hash2</key>
			<data>
			DmKV+NTcDNFkjZOGQ5h44vwDWCRZeul2vzc+SHcQUQs=
			</data>
		</dict>
		<key>Headers/SDWebImageError.h</key>
		<dict>
			<key>hash</key>
			<data>
			FC3IAo7pR4h93t6QeXlmtOHdZvI=
			</data>
			<key>hash2</key>
			<data>
			v000vZmWuZTD6mW0rNs0pqbTyKzZryRFBzb9H0sEzS4=
			</data>
		</dict>
		<key>Headers/SDWebImageIndicator.h</key>
		<dict>
			<key>hash</key>
			<data>
			d4goT+IzohYDq38BvPeR7BTc5iw=
			</data>
			<key>hash2</key>
			<data>
			MDKI/ZSjy+r9C2ncvk/1ZwGZ+c5AK5eakmNXS/wWA8Y=
			</data>
		</dict>
		<key>Headers/SDWebImageManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			xiHH/HCr0tx9VoY3F1eceSzc0rA=
			</data>
			<key>hash2</key>
			<data>
			R6U+hFzGejL1cDxtTSrd1ap+R9YOQGp6sDSSA5Dk0B8=
			</data>
		</dict>
		<key>Headers/SDWebImageOperation.h</key>
		<dict>
			<key>hash</key>
			<data>
			Hitkjp5wpW1nlkxBEFxklBLznXY=
			</data>
			<key>hash2</key>
			<data>
			stSfyXNeoUfeIWpAmkzq1RYWGKFDDtXw8xz9XrTDlXg=
			</data>
		</dict>
		<key>Headers/SDWebImageOptionsProcessor.h</key>
		<dict>
			<key>hash</key>
			<data>
			+tN4ufzzcshfZfddoW2fki5pPcY=
			</data>
			<key>hash2</key>
			<data>
			MLr4/XYAKsIVDSMb+mfG7w3K+liH8qTe4lBcHnSHU4U=
			</data>
		</dict>
		<key>Headers/SDWebImagePrefetcher.h</key>
		<dict>
			<key>hash</key>
			<data>
			xMh1Gu+HBzvb1MOuMY5Vt7zYm0M=
			</data>
			<key>hash2</key>
			<data>
			brr5Y7kpCqKbUrsltKgIobZ4Xankz12/Op9BRFEWtzo=
			</data>
		</dict>
		<key>Headers/SDWebImageTransition.h</key>
		<dict>
			<key>hash</key>
			<data>
			T/5y2vKBxqAbm7KWUccowjKnp6Y=
			</data>
			<key>hash2</key>
			<data>
			HJe8qOqRy61joWehD1GFu4GPbrJUkHlzm+A9buZqHG0=
			</data>
		</dict>
		<key>Headers/UIButton+WebCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			CQ/r9KIZ4rC9OLCgZ8IlI6yO1C0=
			</data>
			<key>hash2</key>
			<data>
			C1ctCpMMEySE7HPIZFSv6EPFGboSKWUTJn6yiQqTxBc=
			</data>
		</dict>
		<key>Headers/UIImage+ExtendedCacheData.h</key>
		<dict>
			<key>hash</key>
			<data>
			fCfC7/RKNYGmDHfXG9+Qg8uB5i4=
			</data>
			<key>hash2</key>
			<data>
			/40DnZqAsRHzfcMXHjG2eMa8E22wUQwcZ95V9HZh688=
			</data>
		</dict>
		<key>Headers/UIImage+ForceDecode.h</key>
		<dict>
			<key>hash</key>
			<data>
			U2UQq4eLIdNIW/q4Txp2/bD4Now=
			</data>
			<key>hash2</key>
			<data>
			Z8wk8GiXpqzZl1iobdbm5nIgRZAbn/ZWxUHM7wMYRBQ=
			</data>
		</dict>
		<key>Headers/UIImage+GIF.h</key>
		<dict>
			<key>hash</key>
			<data>
			YQ1NHxHIECTuq127MqEt8ssMl2E=
			</data>
			<key>hash2</key>
			<data>
			NNm7KhFvpIhffaqvzJaIc7ws39cuvtBXZCzzGCyoVsw=
			</data>
		</dict>
		<key>Headers/UIImage+MemoryCacheCost.h</key>
		<dict>
			<key>hash</key>
			<data>
			LzgPQ2O81SwnuvHsClmNlVmadtw=
			</data>
			<key>hash2</key>
			<data>
			ikCrUNKr+vC/RPm2HC4Dl7IOgrCExPpBVz7A8mQ8erI=
			</data>
		</dict>
		<key>Headers/UIImage+Metadata.h</key>
		<dict>
			<key>hash</key>
			<data>
			PrI9v6timUL2+1eymDpTBARKVZY=
			</data>
			<key>hash2</key>
			<data>
			qzqckvfFPurcpjMg4jn0tOn7ApaU6THO624ZHaJjTYo=
			</data>
		</dict>
		<key>Headers/UIImage+MultiFormat.h</key>
		<dict>
			<key>hash</key>
			<data>
			tJOSxpAbfgpEkLSjOKDTQBvpAGI=
			</data>
			<key>hash2</key>
			<data>
			MmLcD/FrFSkkfHdDcztp8LrJWG5U3pfcp7ny41PxeLk=
			</data>
		</dict>
		<key>Headers/UIImage+Transform.h</key>
		<dict>
			<key>hash</key>
			<data>
			Rg5hx4t/qdnlNZLUCFaziLkwocU=
			</data>
			<key>hash2</key>
			<data>
			mV6xHHLwSjVlZPUQpgBxl2jtzJrEcDAq55mSA5ik3tc=
			</data>
		</dict>
		<key>Headers/UIImageView+HighlightedWebCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			V4leacX4kIZjjFmKNWTOcxEWbRI=
			</data>
			<key>hash2</key>
			<data>
			EPJRLMV/bmuW4OGFh41msEopuK9PgANZXKVZNFosf40=
			</data>
		</dict>
		<key>Headers/UIImageView+WebCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			kJScMN4XyKqi6oIV8vdvgX9QKs0=
			</data>
			<key>hash2</key>
			<data>
			a1RXtDV4geqEDqM64e29H8eliaeTMJHQouZTzmt3f0Q=
			</data>
		</dict>
		<key>Headers/UIView+WebCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			6s86639L67mb1Y1aTunR3oS9C+U=
			</data>
			<key>hash2</key>
			<data>
			yXMbWvtPWEfJjMeh+3ChnyVhz1HrUNZLwGolR15Dbws=
			</data>
		</dict>
		<key>Headers/UIView+WebCacheOperation.h</key>
		<dict>
			<key>hash</key>
			<data>
			gWoTb4BsM6UfrNyXSanUM+AQLbU=
			</data>
			<key>hash2</key>
			<data>
			meaO3k2BE7QmtJ0v6cEEmGv76UODAt0bbR3Irp23TxI=
			</data>
		</dict>
		<key>Headers/UIView+WebCacheState.h</key>
		<dict>
			<key>hash</key>
			<data>
			771hZgr2q0NJeFs0oox1EMSX4mM=
			</data>
			<key>hash2</key>
			<data>
			hDI+hQNqT+BmJWioRTwhS7xCtZFxHSy4PwALr4slTeE=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			6irOkF2Qx//aDEBN8hX0lEFShtg=
			</data>
			<key>hash2</key>
			<data>
			Se+Cxql/XEjqOthTRwI0w2EAt/bLR23SprA3cMveyA0=
			</data>
		</dict>
		<key>PrivateHeaders/NSBezierPath+SDRoundedCorners.h</key>
		<dict>
			<key>hash</key>
			<data>
			hVf10BOSXAmUGUC+yd0ztOAH4BY=
			</data>
			<key>hash2</key>
			<data>
			125iXezXazuk0bBlzjX97k56M5HND0POvD1P26UXQvQ=
			</data>
		</dict>
		<key>PrivateHeaders/SDAssociatedObject.h</key>
		<dict>
			<key>hash</key>
			<data>
			vGTBkYhQJTlD6ZChh2vQXaWhC0U=
			</data>
			<key>hash2</key>
			<data>
			SxKYYCAzWltASk9/E4T+WJihZuqMEf6TbrBis4zVZEE=
			</data>
		</dict>
		<key>PrivateHeaders/SDAsyncBlockOperation.h</key>
		<dict>
			<key>hash</key>
			<data>
			snzjaBGyE/z0cdUS9+aJRKrxDnI=
			</data>
			<key>hash2</key>
			<data>
			HPaaPMjj0fdrwSzEf++FQXpOIK7j880TYegbK3JnETo=
			</data>
		</dict>
		<key>PrivateHeaders/SDDeviceHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			n3vn/m5X9Sy1i9PlwGlLpJgwiQU=
			</data>
			<key>hash2</key>
			<data>
			wF+c5dvmk4GtY8y309yUVkLszKFY+Bjo5ACPl3C+nA4=
			</data>
		</dict>
		<key>PrivateHeaders/SDDisplayLink.h</key>
		<dict>
			<key>hash</key>
			<data>
			ylFFSzJbR+eq7L+qXMeezb4OXSs=
			</data>
			<key>hash2</key>
			<data>
			ck/YSP9JmZtonYX1YfoXENrBGq3YSnTL7L/2ZZn98d0=
			</data>
		</dict>
		<key>PrivateHeaders/SDFileAttributeHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			sb2CUGqQfxIv2pbTDH1eH9Tx/lc=
			</data>
			<key>hash2</key>
			<data>
			zB3IKhQyk1zD3DyxhRPeN6d64/vK8mZe0PddbeHnzsE=
			</data>
		</dict>
		<key>PrivateHeaders/SDImageAssetManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			8EwarZm30x7czN6Nn9NHYxVbepY=
			</data>
			<key>hash2</key>
			<data>
			9y6khxuRX1WIQMj6J8AJ/2tByFNlAqTDD/hhD5qBoUE=
			</data>
		</dict>
		<key>PrivateHeaders/SDImageCachesManagerOperation.h</key>
		<dict>
			<key>hash</key>
			<data>
			FO5aLhjBQ2flj+39N/ihTId+Yx4=
			</data>
			<key>hash2</key>
			<data>
			AEyIv1rLeiXGp4mxbe8dolUaev33aJwaA6/pvRLy244=
			</data>
		</dict>
		<key>PrivateHeaders/SDImageFramePool.h</key>
		<dict>
			<key>hash</key>
			<data>
			p4Uj8gNLlVyG4aMmYvdqi2/YJHI=
			</data>
			<key>hash2</key>
			<data>
			d0xW0Xh/bXttI34nV1E8wZ4dTeSr8WmCLzItU5jo3Ng=
			</data>
		</dict>
		<key>PrivateHeaders/SDImageIOAnimatedCoderInternal.h</key>
		<dict>
			<key>hash</key>
			<data>
			DF/qdUa8vN7/EOif7GlLIoLfpC0=
			</data>
			<key>hash2</key>
			<data>
			DTo+TgqvBrpiIbqISSRD5IwrnIF2KBspPkvoaToiqAE=
			</data>
		</dict>
		<key>PrivateHeaders/SDInternalMacros.h</key>
		<dict>
			<key>hash</key>
			<data>
			Lns/aWnsokOaPUSXXvUqGIetRr4=
			</data>
			<key>hash2</key>
			<data>
			CcLqNsDNmKb4N09owp1EFqkqzeMHeb8Q3v92YNX4hDA=
			</data>
		</dict>
		<key>PrivateHeaders/SDWeakProxy.h</key>
		<dict>
			<key>hash</key>
			<data>
			sDlUvBrJYVMQKR39qPnbjcghuhk=
			</data>
			<key>hash2</key>
			<data>
			sOFO2j8398Rj63PfofP+WM3UpONhTV+g8dtNiB7OlGk=
			</data>
		</dict>
		<key>PrivateHeaders/SDWebImageTransitionInternal.h</key>
		<dict>
			<key>hash</key>
			<data>
			IofQDwABOOMR5dycmLjaLz5cOnU=
			</data>
			<key>hash2</key>
			<data>
			typy3BLrrUyGQLZ0rgfRtqOXQsJbVQp1VyqAZuAN+9M=
			</data>
		</dict>
		<key>PrivateHeaders/SDmetamacros.h</key>
		<dict>
			<key>hash</key>
			<data>
			/UCzmRZdtlxqZdIiubMLZeAL5oo=
			</data>
			<key>hash2</key>
			<data>
			woZuhByGlpU1DSRGoXvfFNTGkSSvobL7ky23jPzEDxY=
			</data>
		</dict>
		<key>PrivateHeaders/UIColor+SDHexString.h</key>
		<dict>
			<key>hash</key>
			<data>
			bvb2vO3YtXbNaIvieoIZQFZg/Vs=
			</data>
			<key>hash2</key>
			<data>
			+mdUXWt+Qmy2a2ZiqxrK4pGY+Abw0T8/u8uTluVAKpA=
			</data>
		</dict>
		<key>SDWebImage.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			fDgvkq3CZBiFRTmXisIrYs47Vz8=
			</data>
			<key>hash2</key>
			<data>
			ol5OS4IrOdsEGxw/b8CQi5hN8meqeyJFDBfDDerhuLs=
			</data>
		</dict>
		<key>SDWebImage.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			PFHYbs0V3eUFDWQyYQcwEetuqEk=
			</data>
			<key>hash2</key>
			<data>
			A7LHCDOjMaKx79Ef8WjtAqjq39Xn0fvzDuzHUJpK6kc=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
