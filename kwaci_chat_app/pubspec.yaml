name: kwaci_chat_app
description: "AI-powered chat application with document RAG integration."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3

  # HTTP & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.2

  # File Handling
  file_picker: ^6.1.1
  mime: ^1.0.4

  # Navigation & UI
  go_router: ^12.1.3
  cached_network_image: ^3.3.1
  flutter_markdown: ^0.6.18

  # Utils
  uuid: ^4.2.1
  intl: ^0.19.0
  equatable: ^2.0.5
  dartz: ^0.10.1

  # Icons
  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.7
  retrofit_generator: ^8.0.4
  riverpod_generator: ^2.3.9
  json_serializable: ^6.7.1
  hive_generator: ^2.0.1

  # Linting
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  uses-material-design: true

  # Assets
  # assets:
  #   - assets/images/
  #   - assets/icons/
