["json_serializable on lib/data/models/document_model.dart", ["", "Could not generate `fromJson` code for `metadata`.\nTo support the type `DocumentMetadata` you can:\n* Use `JsonConverter`\n  https://pub.dev/documentation/json_annotation/latest/json_annotation/JsonConverter-class.html\n* Use `JsonKey` fields `from<PERSON>son` and `to<PERSON>son`\n  https://pub.dev/documentation/json_annotation/latest/json_annotation/JsonKey/fromJson.html\n  https://pub.dev/documentation/json_annotation/latest/json_annotation/JsonKey/toJson.html\npackage:kwaci_chat_app/domain/entities/document.dart:7:26\n  ╷\n7 │   final DocumentMetadata metadata;\n  │                          ^^^^^^^^\n  ╵", "#0      DecodeHelper._deserializeForField (package:json_serializable/src/decode_helper.dart:241:7)\n#1      DecodeHelper.createFactory.deserializeFun (package:json_serializable/src/decode_helper.dart:56:9)\n#2      _writeConstructorInvocation.<anonymous closure> (package:json_serializable/src/decode_helper.dart:335:32)\n#3      MappedListIterable.elementAt (dart:_internal/iterable.dart:442:31)\n#4      ListIterator.moveNext (dart:_internal/iterable.dart:371:26)\n#5      StringBuffer.writeAll (dart:core-patch/string_buffer_patch.dart:106:25)\n#6      _writeConstructorInvocation (package:json_serializable/src/decode_helper.dart:333:9)\n#7      DecodeHelper.createFactory (package:json_serializable/src/decode_helper.dart:59:18)\n#8      GeneratorHelper.generate (package:json_serializable/src/generator_helper.dart:86:28)\n#9      _SyncStarIterator.moveNext (dart:async-patch/async_patch.dart:595:13)\n#10     WhereIterator.moveNext (dart:_internal/iterable.dart:467:22)\n#11     MappedIterator.moveNext (dart:_internal/iterable.dart:419:19)\n#12     WhereIterator.moveNext (dart:_internal/iterable.dart:467:22)\n#13     _UnifiedGenerator.generate (package:json_serializable/src/json_part_builder.dart:64:27)\n<asynchronous suspension>\n#14     _generate (package:source_gen/src/builder.dart:342:23)\n<asynchronous suspension>\n#15     Stream.toList.<anonymous closure> (dart:async/stream.dart:1418:7)\n<asynchronous suspension>\n#16     _Builder._generateForLibrary (package:source_gen/src/builder.dart:107:9)\n<asynchronous suspension>\n#17     _Builder.build (package:source_gen/src/builder.dart:99:5)\n<asynchronous suspension>\n#18     runBuilder.buildForInput (package:build/src/generate/run_builder.dart:83:7)\n<asynchronous suspension>\n#19     Future.wait.<anonymous closure> (dart:async/future.dart:528:21)\n<asynchronous suspension>\n#20     scopeLogAsync.<anonymous closure> (package:build/src/builder/logging.dart:32:40)\n<asynchronous suspension>\n"]]