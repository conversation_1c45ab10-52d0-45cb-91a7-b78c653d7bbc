// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXAggregateTarget section */
		1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 94078A06C17E946ADC1F2C06726219E5 /* Build configuration list for PBXAggregateTarget "Flutter" */;
			buildPhases = (
			);
			dependencies = (
			);
			name = Flutter;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		00CC8622F9CECEE6738C6EBA224F3AFD /* SDWebImageCacheKeyFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9CFF3FC67131B888135B0062F9DACD6E /* SDWebImageCacheKeyFilter.m */; };
		02B9991D0D6FC6F2558078A11F063889 /* UIImage+MemoryCacheCost.h in Headers */ = {isa = PBXBuildFile; fileRef = 44C6A649E2C9593B4DC800E4A2248313 /* UIImage+MemoryCacheCost.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0358550B94AADC665DF43A09A7D8D4D5 /* SDImageHEICCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 93E1F359B238F540B0795D9287E2F3EF /* SDImageHEICCoder.m */; };
		0359537355B4B312FFC8D625AAA750B8 /* UIColor+SDHexString.h in Headers */ = {isa = PBXBuildFile; fileRef = 037CFB958236492D2431DAD90447D27F /* UIColor+SDHexString.h */; settings = {ATTRIBUTES = (Private, ); }; };
		03AC14B534B1222D6704672EF0D7C621 /* UIImage+ForceDecode.h in Headers */ = {isa = PBXBuildFile; fileRef = FC4C0D09130EA693500EF4707BA41BA4 /* UIImage+ForceDecode.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0447458B575FC97F4202172E28CFD17C /* SqfliteDarwinDatabaseQueue.h in Headers */ = {isa = PBXBuildFile; fileRef = CB8932706B28A8B6A8A1A1FEF3423189 /* SqfliteDarwinDatabaseQueue.h */; settings = {ATTRIBUTES = (Project, ); }; };
		054BE0A371B5FE1BF7B69594AB09B203 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 163B3B630806B6DC3C48FF69996A353D /* PrivacyInfo.xcprivacy */; };
		06602B786B5C16AD1FBA445A4064BFDE /* SDWebImageDownloaderOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = F0FE15373CF1F72CCAC4E44E93611A6A /* SDWebImageDownloaderOperation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		068EE2BFCD9869F3482F0CA621F1E3FF /* SDFileAttributeHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 0FF6F2DB836BE8463976732D1C1DE2FA /* SDFileAttributeHelper.h */; settings = {ATTRIBUTES = (Private, ); }; };
		06CBBD253C541754C8DA43FD5D930BBF /* SDWebImageIndicator.h in Headers */ = {isa = PBXBuildFile; fileRef = 193E1CD46454AB8774F082A207245060 /* SDWebImageIndicator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		074A9652FFB4790E36600DE59E36C980 /* SDWebImage-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 28EB180F2D1FEEC56C83D5F1E529AF16 /* SDWebImage-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		086880AB1FFF15ADE6BACED51B0A10D7 /* SDAssociatedObject.h in Headers */ = {isa = PBXBuildFile; fileRef = 526D021BF988C55A8A8CA5AD0E47E146 /* SDAssociatedObject.h */; settings = {ATTRIBUTES = (Private, ); }; };
		097DC93583DF2306BD9F0F1DF59F388D /* en.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 0317A879E8DB725D7E3A04332B0F04FE /* en.lproj */; };
		099AC07AB5F88C8101B52BBC1B876C46 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = AF2308829CFEABB97817F0E52ED98D6A /* PrivacyInfo.xcprivacy */; };
		0B890FA352D8E20B899DCD8926ACB25D /* SDWebImageDownloaderRequestModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = DD8D5D70EF7F01FB42EC000BDD1D0A02 /* SDWebImageDownloaderRequestModifier.m */; };
		0BB41CF66B36C589B187A619D5EE8716 /* DKPhotoBaseImagePreviewVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4591242795E8FDCC6C65962A5EB118F0 /* DKPhotoBaseImagePreviewVC.swift */; };
		0C770BDEB8B2C4EA09AB8700FBC7EEF4 /* DKPhotoImageUtility.swift in Sources */ = {isa = PBXBuildFile; fileRef = CE48C851D036CBD2600AD30FE109D805 /* DKPhotoImageUtility.swift */; };
		0CC891AEF7AEC2366F15F8B52C12B55D /* SDWebImageDownloaderConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = B6FB2AE1358B06D39A0622A9623A6E90 /* SDWebImageDownloaderConfig.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0DEA96F80583EDB0B1A439B79C14F29F /* UIImage+Metadata.m in Sources */ = {isa = PBXBuildFile; fileRef = 36CABB822BC8CFBE8AF794099CFEB6B7 /* UIImage+Metadata.m */; };
		0E37E8E9CD8245FB1993052B666A0EAE /* en.lproj in Resources */ = {isa = PBXBuildFile; fileRef = CE1CDB9D2119321BD845E825C048B9FA /* en.lproj */; };
		0ECBB230C0F9E389D500B568892A8342 /* FilePickerPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = 1BB8FBB76EC625D4961877C0E0F9B572 /* FilePickerPlugin.m */; };
		0FB76DC5F9BBE8514F6A0B7E2DD7EADB /* DKPhotoIncrementalIndicator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2D5B453D1CB610A9D15EC826630ACD14 /* DKPhotoIncrementalIndicator.swift */; };
		1068E74C6249E12C8ABF9099D9C91FCF /* SDWebImageDownloaderOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 0E7D88775D9607B5B2669B687E24DF97 /* SDWebImageDownloaderOperation.m */; };
		10A218FABBA3B58065077701DE0B6770 /* DKImagePickerController-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 99115CD6710556527DF72A648E0E6DEF /* DKImagePickerController-dummy.m */; };
		119E21840A276EDD5B1E5DE62D1BEB5C /* DKImagePickerControllerResource.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0810676177E4E5AE719C8068D59C85EA /* DKImagePickerControllerResource.swift */; };
		11EE828AE0664EA1EA51FB0A63FF8DA1 /* DKImageExtensionController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9002FF03C62F1DB866747FD7D2380865 /* DKImageExtensionController.swift */; };
		120933850149F94F7325BD3971F26EDD /* SDWebImageTransitionInternal.h in Headers */ = {isa = PBXBuildFile; fileRef = 664F757AF84876E95DD100931F5E51D9 /* SDWebImageTransitionInternal.h */; settings = {ATTRIBUTES = (Private, ); }; };
		122F8872E1D00DB6FA0877C26E9BA16F /* FilePickerPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 83A952E2F026F6B77F742162DC2F406D /* FilePickerPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1330A87C24E8C5FA52A26AA3D88CDE05 /* SDMemoryCache.m in Sources */ = {isa = PBXBuildFile; fileRef = E90D0CC7D650A88957252D78CB5E955B /* SDMemoryCache.m */; };
		147167D55FDCFE4AAB312AB16D90A89B /* SDImageIOAnimatedCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 4E1127B1D755504DC3CFED311B7A7C58 /* SDImageIOAnimatedCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		14F8DD4CD78B04D048D64E6B0C8F4D0B /* path_provider_foundation-path_provider_foundation_privacy in Resources */ = {isa = PBXBuildFile; fileRef = 3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */; };
		152260C7CC12E6405E97D8B1B8DA30D2 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 544CE4FAB9748BB244C77D7C8BDDEBFC /* Foundation.framework */; };
		190694EC8099CAC8B71049AF2C126F21 /* UIImage+Transform.m in Sources */ = {isa = PBXBuildFile; fileRef = A2676DCDD2D5F740722D70C227DC2C00 /* UIImage+Transform.m */; };
		19A36F47A24047A22A3DBB9AA5C5E47E /* SDWebImageDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = 0FF02D582583464E21DF635A2FD0456E /* SDWebImageDownloader.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1A3C83553E599473DCEE49142595AD6A /* UIImageView+HighlightedWebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = F499E0C710700093032636C2129A6C42 /* UIImageView+HighlightedWebCache.m */; };
		1A9A8F94BA9B2D08557D79AE1A7A24F4 /* NSImage+Compatibility.h in Headers */ = {isa = PBXBuildFile; fileRef = 40619F1585AED234B940722C7704ACC0 /* NSImage+Compatibility.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1AE4057C2E9D5CE8ECC1E3CF5F057BDF /* UIView+WebCacheState.h in Headers */ = {isa = PBXBuildFile; fileRef = AF045921C014ECFE871641656F7A8263 /* UIView+WebCacheState.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1C27C3C1BBB4F6BB8552C7F5B2819E20 /* SDWebImageCacheSerializer.h in Headers */ = {isa = PBXBuildFile; fileRef = 78C26B66151FF0666D53FF95DFB24CC6 /* SDWebImageCacheSerializer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1ED77531D5C1FA6489DD98ECF1213B8B /* vi.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 178C00F3E8FADF6109FA4C3E3D2D1C13 /* vi.lproj */; };
		1EE390ED5D95BA1BEB2C3EE0B84C3ADC /* nl.lproj in Resources */ = {isa = PBXBuildFile; fileRef = E5B8BAD4DCE5B5C285F6A7E080EFC0A9 /* nl.lproj */; };
		1F390C289814EF57E68B8E02E00EFE6D /* ImageIO.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7B5E6D4EED9D7031B7EF9CBADD83B364 /* ImageIO.framework */; };
		20302FF56E778E318E04E911C6F0168D /* SDWebImageIndicator.m in Sources */ = {isa = PBXBuildFile; fileRef = A910FE54FECA1F4AB9FF751A7369D2CA /* SDWebImageIndicator.m */; };
		219E7EAE58315F178DE5FA636F411113 /* DKPhotoPlayerPreviewVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 54FE0A6E8684179D959A0D19CD625441 /* DKPhotoPlayerPreviewVC.swift */; };
		21EAE8080DDCDE6C0567E9034DACFB83 /* SDDeviceHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = F763ABEC120B4A237083642AA4BC5263 /* SDDeviceHelper.m */; };
		23053B38F426C67DAA0AEF05D662F5B3 /* SDImageCoderHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 6F994383150A741DF9B1FFB4FE6D1F76 /* SDImageCoderHelper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		23F3D313BD8BCDE25356247DD21EB424 /* SqfliteDarwinResultSet.h in Headers */ = {isa = PBXBuildFile; fileRef = 56D90C42BA11BE7FBFF39A02376CD60D /* SqfliteDarwinResultSet.h */; settings = {ATTRIBUTES = (Project, ); }; };
		24072965FA936091E8C49752F5202E5F /* UIView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 714586DDD6BB2DC815586BC92DE1DF59 /* UIView+WebCache.m */; };
		25290B066A075D5A18F1E9A5D5FEFAB8 /* SDAssociatedObject.m in Sources */ = {isa = PBXBuildFile; fileRef = 809BE2F73F70AC9C53AB791EF55F89EE /* SDAssociatedObject.m */; };
		25784F0865063087BD75D3F88C8F56C4 /* SDImageCacheDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = B289029A2A9E63EE71F2F75DC7FDDF25 /* SDImageCacheDefine.m */; };
		2631CEC725AA8C0549E5D2980171E0B0 /* path_provider_foundation-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 64C532391A7B5F56B8622A219800288E /* path_provider_foundation-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		278303399A5839329320379983235C2D /* SDImageGraphics.h in Headers */ = {isa = PBXBuildFile; fileRef = 4AC63E9D4FF571A8F0813F399A2729E5 /* SDImageGraphics.h */; settings = {ATTRIBUTES = (Public, ); }; };
		28873B7252A10AB97FA1ECB3B3FA027B /* es.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 53E673146B08BD6614925B39051F5479 /* es.lproj */; };
		28CDE7105B5CBA846AAA1A1C6513010E /* it.lproj in Resources */ = {isa = PBXBuildFile; fileRef = AF9436FC3C179BE6332F89289A0D594D /* it.lproj */; };
		290E3ADDEB5B8DEA3EE65C3D5B29F6ED /* SDImageIOCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 87DB018C0EBE728B28D6A5AE5285AAC0 /* SDImageIOCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		296F8A617ED41772C6271F5F4722EB4F /* SqfliteOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = AAD87A5D5939F79B971D95C1E00D1631 /* SqfliteOperation.m */; };
		2BF7C1AA748932E55AB80665DB1D1740 /* FileInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 746BBF33504A0699A90AAF0DF6BF1C3A /* FileInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2C35AC8AE8EE7D71F86D9607A9E1B52F /* SDInternalMacros.m in Sources */ = {isa = PBXBuildFile; fileRef = 82762ED739DDD5E6A82A1AD119D1DC5A /* SDInternalMacros.m */; };
		2CD13FADE3A6DE41D016518A46E19588 /* SDAnimatedImage.m in Sources */ = {isa = PBXBuildFile; fileRef = 21E3C16CD240C7F7929F6768CC223340 /* SDAnimatedImage.m */; };
		2D3E80ED15E72912790CB52C60C338E8 /* SDImageCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 333901A5FDF5D2E3D72F5A2E5F152188 /* SDImageCache.m */; };
		2D6937E8D7AADEFCA71B193ADBB99AA5 /* DKAssetGroupListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 823EAB70C33F9BBC2D30E14237611EE6 /* DKAssetGroupListVC.swift */; };
		2EAFA69DF31F8EA6FC5E9F204B7A9B3B /* SqfliteDarwinDatabase.h in Headers */ = {isa = PBXBuildFile; fileRef = 12155415260FB301F696655FE9BB0F0C /* SqfliteDarwinDatabase.h */; settings = {ATTRIBUTES = (Project, ); }; };
		30002454870A7768082422F3D005AD55 /* ObjcAssociatedWeakObject.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98EC63143D86891CEF60EAF54154907C /* ObjcAssociatedWeakObject.swift */; };
		30774628EB6346486D87EBD245C76288 /* tr.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 1C395147920BA0F2ED484BDC6FDA3D00 /* tr.lproj */; };
		30BD26961928AEA215A5297842CB6E4D /* Photos.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2B18DB190CB3877B1931D632678E4A50 /* Photos.framework */; };
		31308839CCC1B53E9F3BAE31BF261759 /* SqfliteCursor.h in Headers */ = {isa = PBXBuildFile; fileRef = 3B60E3606FF684C80B30BD09241E1A44 /* SqfliteCursor.h */; settings = {ATTRIBUTES = (Project, ); }; };
		3151AED946F9E3E04317FDFA441B37BE /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 5EC40588954DF1EBCB3F100414A48355 /* PrivacyInfo.xcprivacy */; };
		315E7B4EF554DA3DC6372BF65F077EB0 /* DKAsset+Export.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D4742F4C898315E9D8FC262C1D0C5D1 /* DKAsset+Export.swift */; };
		318D194CABEF87A5657BD250DE8EB0CB /* SqfliteCursor.m in Sources */ = {isa = PBXBuildFile; fileRef = C18CCDB70542EF82D63836F264A1D7A1 /* SqfliteCursor.m */; };
		324AA14FDCAA7101CD2637507DEE8387 /* SDImageAWebPCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 00AE2D6DD6DF51C4C2B09FB40A83486B /* SDImageAWebPCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3376EBA340CCF5916A93365EF8DB9478 /* SDCallbackQueue.h in Headers */ = {isa = PBXBuildFile; fileRef = 9802B7F6F402B20B8A338B418A577F8B /* SDCallbackQueue.h */; settings = {ATTRIBUTES = (Public, ); }; };
		338767F71B0307EF3F31F230E1E35658 /* sqflite_darwin-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 86371477A8C16F168BFD3A274F96B823 /* sqflite_darwin-dummy.m */; };
		33FE25FE9D906483259EE5C75E2BDA02 /* UIImage+GIF.h in Headers */ = {isa = PBXBuildFile; fileRef = BD048AA3CEB4874E738992566F3C2588 /* UIImage+GIF.h */; settings = {ATTRIBUTES = (Public, ); }; };
		348E8334AF62A1304F67CC1507757025 /* ko.lproj in Resources */ = {isa = PBXBuildFile; fileRef = FBB7D94DCAE93443547F6AADE9EF7E64 /* ko.lproj */; };
		3544ED679AF22F9558ED08A66F3C0C1C /* DKPhotoGalleryScrollView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F006DD0FEB32CB3A5079027A22EB082B /* DKPhotoGalleryScrollView.swift */; };
		36524C6893C49D93D17AB9A9FC0F9F99 /* SqfliteOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 37230F72808ED6F5F6F430C96DBD5CF8 /* SqfliteOperation.h */; settings = {ATTRIBUTES = (Project, ); }; };
		36D613CDE8BF8DAD84C834B120FAAC6D /* DKImagePickerController-DKImagePickerController in Resources */ = {isa = PBXBuildFile; fileRef = 7247E064ECCE473BC39CE9C6B1E7C0B3 /* DKImagePickerController-DKImagePickerController */; };
		37252DE0C242ADD277A3C92981C76C20 /* SqflitePlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 1BB31DB611B78E2CC38EAA0E03E17C4A /* SqflitePlugin.h */; settings = {ATTRIBUTES = (Project, ); }; };
		3AA426D9310F16A66DA067D7FABAEC8C /* UIView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C502322A90F283DE0AA7996C79F1DD5 /* UIView+WebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3B44C7A8263FB4C7D635CCA0D6526901 /* DKPhotoImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E02C1C4A89BB4CC0BC278D8C4D0D8BD5 /* DKPhotoImageView.swift */; };
		******************************** /* SDWebImageCompat.m in Sources */ = {isa = PBXBuildFile; fileRef = 252E12216ED1E68BEE62EF6E200684E9 /* SDWebImageCompat.m */; };
		3DD24B476D326068A41603316B5828AD /* SDWebImageDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = C0FA2332A3665702D9B2ECB0498FF316 /* SDWebImageDefine.m */; };
		3F087BC352E4E9B5678D6E9EDC7EAF6B /* DKImagePickerController-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 8B15D97E1E4B86D44B9D9D97ABFF88D1 /* DKImagePickerController-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3F44797CDC1B48689B7AF1F70A0503D2 /* SDImageCacheDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 53CF8F5F962D52D648117B8528335011 /* SDImageCacheDefine.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3FBDFBBE5F2D55C8C163772E879A63FC /* DKPhotoGalleryTransitionPresent.swift in Sources */ = {isa = PBXBuildFile; fileRef = DA911E92959967B9215184E7E33D1537 /* DKPhotoGalleryTransitionPresent.swift */; };
		43256922E1EC61FED22FB24F009B69B7 /* DKPhotoQRCodeResultVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = B71ED232D67949A5C2AE8CD69403697E /* DKPhotoQRCodeResultVC.swift */; };
		438B85BA7F8FDAE067BAC863A0274714 /* SDAnimatedImageRep.m in Sources */ = {isa = PBXBuildFile; fileRef = FCB225BAED5A78B4AB86F0D0987D57DE /* SDAnimatedImageRep.m */; };
		453782C5694D74646FBF7563DA3CF18B /* ur.lproj in Resources */ = {isa = PBXBuildFile; fileRef = E9AF85467FC35B2F758574B1B2D48AF0 /* ur.lproj */; };
		4AFE10FC1E2D073EA45F55E86C38CDA5 /* SDWebImageOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 8B040808633321DF5B09556AF2A4DE3E /* SDWebImageOperation.m */; };
		4B34BCA8A854401BE9AA60D04B620102 /* SDImageLoader.h in Headers */ = {isa = PBXBuildFile; fileRef = 02763698D1944FC236DFC2F999C0F24D /* SDImageLoader.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4C397E3C975AE4A17F61470FF3E81EED /* SDWebImagePrefetcher.h in Headers */ = {isa = PBXBuildFile; fileRef = 52B9FEE904A761B23281300065D431FE /* SDWebImagePrefetcher.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4C475CB455ED065B025E9EE589BD1D67 /* SwiftyGif-SwiftyGif in Resources */ = {isa = PBXBuildFile; fileRef = 0A37DE9A55F6CEA1AF18BDEF878BF1F5 /* SwiftyGif-SwiftyGif */; };
		4DE609D64E8450C7B0CA49C54E11A437 /* SDImageGIFCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = BB03859F9C5341E9D17E770C6121EA66 /* SDImageGIFCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4E11E426F54801885A8F52AD3629B7A2 /* SDImageCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = D81065B2050933B69046A61F756EF33D /* SDImageCoder.m */; };
		4F5BEC7214A0F38643B34E86192EFAD3 /* DKAssetGroupDetailImageCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9121B2897840B068C0E6BD952C182343 /* DKAssetGroupDetailImageCell.swift */; };
		504BDAE9EC84472751996A01FE6FE5EF /* SDWebImageDownloaderDecryptor.h in Headers */ = {isa = PBXBuildFile; fileRef = 15DF37520825A33A1CD95D9E4581E7FA /* SDWebImageDownloaderDecryptor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		510D8A0B13E38B0184E23FE20121390A /* SDImageFramePool.h in Headers */ = {isa = PBXBuildFile; fileRef = 75DCD7E22599C1BAAF8EE36561E572A0 /* SDImageFramePool.h */; settings = {ATTRIBUTES = (Private, ); }; };
		51993AF62016F6D66E193B5D92E000D7 /* SDAnimatedImageView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = DDCFB0568851722C689867C3D8B9B3FF /* SDAnimatedImageView+WebCache.m */; };
		5248914B09DBDDD3FBC067C28DD7916D /* Photos.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2B18DB190CB3877B1931D632678E4A50 /* Photos.framework */; };
		536C4F61524406AA282F819D1D45EBBD /* SDWebImageError.m in Sources */ = {isa = PBXBuildFile; fileRef = 4482A8841A1F1E5BDF45F88FF764F9E8 /* SDWebImageError.m */; };
		548ECE54A90307FE3F5345D6DC709499 /* NSData+ImageContentType.h in Headers */ = {isa = PBXBuildFile; fileRef = 8CDB4904C17A77BF2CDFC740DD72E34C /* NSData+ImageContentType.h */; settings = {ATTRIBUTES = (Public, ); }; };
		54BE9077826608AE9BAB99E4266B57CC /* UIView+WebCacheOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C13667A8AC102064180EEC15D2530F8 /* UIView+WebCacheOperation.m */; };
		55075053B5292AB8C5D23FAFAD8F8FF2 /* UIImage+Transform.h in Headers */ = {isa = PBXBuildFile; fileRef = 29702D0F4CE361960D3843D0FC1516EB /* UIImage+Transform.h */; settings = {ATTRIBUTES = (Public, ); }; };
		55156F828612CD7261B51BF7D73A3C43 /* SDWebImageOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = C39FBA296695B60A112108133BC9FF16 /* SDWebImageOperation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		579F55EEFFADF96E8F936DBA91D26494 /* DKAssetGroup.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7C26E48B6A32C3499D867872EB112538 /* DKAssetGroup.swift */; };
		58A18F12AE88C977E18DB18FBACE0E0B /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 544CE4FAB9748BB244C77D7C8BDDEBFC /* Foundation.framework */; };
		58DBA636318A2DE38E4B05E6F40FB6CC /* SDWebImageDownloaderDecryptor.m in Sources */ = {isa = PBXBuildFile; fileRef = AC8403BA1DA69DF47C2FE4D61B2C72D2 /* SDWebImageDownloaderDecryptor.m */; };
		5A6DEFF05C4601D30618C55DF681AC38 /* AVKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 697F1BC979DA5F63FCDA032EB7A85FD0 /* AVKit.framework */; };
		5A87A01E01BADD80B7F787211FE62CFF /* SDWebImageCacheSerializer.m in Sources */ = {isa = PBXBuildFile; fileRef = 9C4C564FCE56D9FEC88A0D69FF61656E /* SDWebImageCacheSerializer.m */; };
		5AA7ACB018166A9BD333DB51828EDC98 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 198A8538874FC857E01267BE1F017B5A /* AVFoundation.framework */; };
		5ABD26486C5C5CD33F97B0E36B0C1551 /* SDImageCachesManagerOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 7E3461DFB3DF66071A606253691043A3 /* SDImageCachesManagerOperation.h */; settings = {ATTRIBUTES = (Private, ); }; };
		5B326344BE346F974BC65B100AD9FBA1 /* file_picker-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = E527F5CD3726095AD1461D752CF3CF11 /* file_picker-dummy.m */; };
		5B60A30157CEDDD4B1D70499975ACAE4 /* fr.lproj in Resources */ = {isa = PBXBuildFile; fileRef = D0D54FF993CCC9BC5769D73BBC879CAD /* fr.lproj */; };
		5CA70AF50AFCFEE736476D4C600287B1 /* zh-Hans.lproj in Resources */ = {isa = PBXBuildFile; fileRef = A7BD1B4BB7B7B7F323AED9971F4C8897 /* zh-Hans.lproj */; };
		5DC05FFD99CCBC2B34BCDB0BD4531F41 /* SDWebImageDownloaderResponseModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C869079073C6295F67F64D07EE6A665 /* SDWebImageDownloaderResponseModifier.m */; };
		5EC1F06BAB7BF265CAFEFC30E0094B8A /* DKImageBaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 584A66BB8106A644D5AFEBF27AFF4F56 /* DKImageBaseManager.swift */; };
		5F80DDC737DBEEA7CCAC2924715CCBD0 /* ar.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 8E41FDDCC3086A68D2AB2DE9DCBBFBA3 /* ar.lproj */; };
		608658907B13F7FB09D7CE4E819AB230 /* DKPhotoImageDownloader.swift in Sources */ = {isa = PBXBuildFile; fileRef = B226930FC757308F96D78BC70158FCF2 /* DKPhotoImageDownloader.swift */; };
		64DC61DF7C573EC98DCBD205EC82B5A0 /* SDImageIOCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 58DFE05D0C28250C4F795C338572A86E /* SDImageIOCoder.m */; };
		64FE0A9CD95376F0A12EDB8231D76DC7 /* SDWebImageOptionsProcessor.m in Sources */ = {isa = PBXBuildFile; fileRef = 54B746288D2A859351C5D7D38D87FA92 /* SDWebImageOptionsProcessor.m */; };
		661A1CEBB05B09F3423053819286C61F /* SDImageCoderHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = B04D9195CE7D91E33317BE7D5613E39E /* SDImageCoderHelper.m */; };
		67E75C46BF8CF42A0DBEC70B80B8DDC0 /* SDAnimatedImagePlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 8012156CA8D5D683C941BD52301448F2 /* SDAnimatedImagePlayer.m */; };
		6B39D201D52530DCFF681730B0D094A2 /* SDImageCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 00933C67E356438676C296252CBE29B7 /* SDImageCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6B3B1BDAF4CB41CC3284343552321227 /* Pods-Runner-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 499E3722E8DACDC2AAA6C15AD1796520 /* Pods-Runner-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6B61792C661E8B939635FEC5561B4D8C /* SDAsyncBlockOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 0A64BF1E08996EC1F0DF0FB68C96926E /* SDAsyncBlockOperation.h */; settings = {ATTRIBUTES = (Private, ); }; };
		6C450F143FA798F37D14D2D829A2B44C /* SDImageCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 8EF44FC4EE5A1070C421EEFE9B6B44D3 /* SDImageCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C5DA7CDF65678819796A1747D34C3C5 /* SqfliteDarwinDatabaseAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = C79B86A180E741505587DBDDD31D82D0 /* SqfliteDarwinDatabaseAdditions.m */; };
		6DAC480A0F8D8839897DEB2540669240 /* DKImagePickerController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3EDCF61AA4F4BD3B51F408EAF838517D /* DKImagePickerController.swift */; };
		6F06EEE2B41EDE7C90E9BDC800220BB8 /* SqfliteDatabase.h in Headers */ = {isa = PBXBuildFile; fileRef = F97632DB1EBEBF407112E2947DB9517C /* SqfliteDatabase.h */; settings = {ATTRIBUTES = (Project, ); }; };
		6F5D88E42C8A2AD03FC90F03938EDE11 /* DKImageGroupDataManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = E2C12FE444D39CE575CAC618A1B31085 /* DKImageGroupDataManager.swift */; };
		6F9E1250A95526D8E23F9F8BC29D513C /* DKPhotoProgressIndicatorProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3510BF004F0CD883594D808691CA9377 /* DKPhotoProgressIndicatorProtocol.swift */; };
		6FB12AA44E086D44C20D7EBF67D45642 /* DKPhotoGallery.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5B29685B553F3F02916B86BAD24D18AD /* DKPhotoGallery.swift */; };
		70AFA2FF809336C31996F765B6E27ECB /* DKAssetGroupCellItemProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 697F580784149950D87E5036BE0E9973 /* DKAssetGroupCellItemProtocol.swift */; };
		7110985989D72D210332C31DB8DA3566 /* SDImageCodersManager.h in Headers */ = {isa = PBXBuildFile; fileRef = F434B085C4FCAB1CBE1653D90C47855F /* SDImageCodersManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		71B98C5CDE17A252AF0DFE252A5591C4 /* SDWebImageDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C97386223F9362E9C2E4EB9445ECD34 /* SDWebImageDefine.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7398852A92A8022C4C99E927B47A891C /* SDWebImageError.h in Headers */ = {isa = PBXBuildFile; fileRef = 0C08A8DD6D68F39F4C1D100F1FA4AFD2 /* SDWebImageError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		744D4F5B29895D716CD82020B26F01CF /* UIView+WebCacheState.m in Sources */ = {isa = PBXBuildFile; fileRef = 896562C9511358EE67C8CBBEE34E6444 /* UIView+WebCacheState.m */; };
		7519B64A6A501181500A040F46E090BD /* DKPhotoPDFPreviewVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = CEF5DE5C78B0CA1EAE3C50C5BD2CF484 /* DKPhotoPDFPreviewVC.swift */; };
		75F07F1ACFC462BB6B8E8756F9F3A535 /* SqfliteDarwinDatabase.m in Sources */ = {isa = PBXBuildFile; fileRef = CD43F23FAB132293A968A5F2629F015C /* SqfliteDarwinDatabase.m */; };
		76287F90002CFFA382123C28CC5244E2 /* DKAsset+Fetch.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5D5F83FABF185E339BDD61B8A9CC26B3 /* DKAsset+Fetch.swift */; };
		7644DB2B5EB37264A050A53D35454174 /* da.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 112E86A0F15DF6F1F777F8360FCEE0B8 /* da.lproj */; };
		764BA112A87BF3E4305289B919657613 /* SDImageLoader.m in Sources */ = {isa = PBXBuildFile; fileRef = 068F7A2A72C969797DEAACD180194E27 /* SDImageLoader.m */; };
		76F12104EE23A5C0EEA1B40525368A10 /* DKPhotoBasePreviewVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1DD88569F894189EF84A98201C8D51F1 /* DKPhotoBasePreviewVC.swift */; };
		77D45BA1978F313656E390A9CC048060 /* UIImage+MultiFormat.h in Headers */ = {isa = PBXBuildFile; fileRef = FDE5B89F25BE0FB60E4F5EE06DCC7A8F /* UIImage+MultiFormat.h */; settings = {ATTRIBUTES = (Public, ); }; };
		78672CC7A069804A99E6C16A6123FFD1 /* DKPhotoImagePreviewVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE8DBD29B147EA62BF91ADC387ECE763 /* DKPhotoImagePreviewVC.swift */; };
		79EB8980D8E28EA25CA288E6A0BC77E7 /* SqflitePlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = DA4FBD87572142A21B8226CCAA644401 /* SqflitePlugin.m */; };
		7ACCFEA085D709C92408802375005F5D /* UIImage+MemoryCacheCost.m in Sources */ = {isa = PBXBuildFile; fileRef = 0DFF43AD2C53B6E68DC2F1E052DCBBB1 /* UIImage+MemoryCacheCost.m */; };
		7B143193409558126EE4F4DC0330338B /* SDImageAPNGCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = F93249511041AC418ECE46605CB8AF22 /* SDImageAPNGCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7CF1A21255474FAB679F692CE4017727 /* SDWebImageDownloaderConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 4728BDCB3F8F6B6F2A48BB8B5BA41AA6 /* SDWebImageDownloaderConfig.m */; };
		7D968E112E601623AAD444BE04A5F922 /* SDDeviceHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = D05F6AFDF66188951FC5DD9C8922661E /* SDDeviceHelper.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7F05966E7064A4D2E39B026A6FCFC112 /* DKImageDataManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9E764D8CAB9CEF2030E6C627DEADC064 /* DKImageDataManager.swift */; };
		7FACF4B1CB684CD901CCC039D54D9957 /* SDAnimatedImageRep.h in Headers */ = {isa = PBXBuildFile; fileRef = CDED959FEA7ECCAF5B87F7E23F2FDF74 /* SDAnimatedImageRep.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7FBECA30D19BD58A07D5F9A14B4001BC /* SDImageIOAnimatedCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 84F8812EAE588F093B026AD494074702 /* SDImageIOAnimatedCoder.m */; };
		80E5A48B0C6099699D040A7D22FF3DAC /* DKPlayerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5804E591074A372E46F63D0DF1B8F89E /* DKPlayerView.swift */; };
		81E5028FDB97C8B59FCD935F7FB569E8 /* SDWebImage-SDWebImage in Resources */ = {isa = PBXBuildFile; fileRef = CF1281E58AA1045D4B7F33FC56691C42 /* SDWebImage-SDWebImage */; };
		82BA5B5EAC25E58F2AE92200746E34B2 /* ja.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 688142D94D862449FC21560EC6EEA81B /* ja.lproj */; };
		8356FF5D3991FCED071276462C7C9FD4 /* UIImage+SwiftyGif.swift in Sources */ = {isa = PBXBuildFile; fileRef = EE998EF1C3F51183ED90CCC588BD9B25 /* UIImage+SwiftyGif.swift */; };
		8485C9AA039D08009FFC0967D73DC168 /* UIImage+ForceDecode.m in Sources */ = {isa = PBXBuildFile; fileRef = 0C7630AD87ADBD4A5E947D432D1F29AB /* UIImage+ForceDecode.m */; };
		85A74687A36FA9AC42D5879F7B6DC77A /* SDWeakProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = 1ACA1B3136E32A65C499F5A086825B21 /* SDWeakProxy.m */; };
		86B487591AB6C88FFF205591E0983E21 /* NSImageView+SwiftyGif.swift in Sources */ = {isa = PBXBuildFile; fileRef = 767E2ED40586F6E12141BC5B86B46448 /* NSImageView+SwiftyGif.swift */; };
		86BCBB024DBF6D4BEF294E1EE1A3BA73 /* DKPhotoProgressIndicator.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4692486E6503F599D73E72884DAD501 /* DKPhotoProgressIndicator.swift */; };
		87754ED42DFC97D0E2B7361A67F8D054 /* UIImage+MultiFormat.m in Sources */ = {isa = PBXBuildFile; fileRef = B8394AFF286008A68FCF59270538841E /* UIImage+MultiFormat.m */; };
		87C16A172BC1F27B82C0E2E24555C9F8 /* UIImageView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 74E9EE0A4F521C8E22CD4FD973883814 /* UIImageView+WebCache.m */; };
		8856919872BF7B18BF09701D2F7CFF70 /* NSImage+SwiftyGif.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9A644C7ABD2777AF2D4541D200284C1E /* NSImage+SwiftyGif.swift */; };
		886008242516899AE2C9A24616E7CF8B /* SDImageCodersManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 2400365BFCC706592D47F38B7877DBFB /* SDImageCodersManager.m */; };
		8923F7F3B70FE87328B5CF7A2B6D664F /* DKPhotoGalleryResource.swift in Sources */ = {isa = PBXBuildFile; fileRef = D3EA1BABD76B44E160C4203748AB3759 /* DKPhotoGalleryResource.swift */; };
		8A9E15908375B00041AB6EE4437F9F20 /* UIImage+ExtendedCacheData.h in Headers */ = {isa = PBXBuildFile; fileRef = D6B3E3D64CA0E0B3F6CE198AA3DD2B80 /* UIImage+ExtendedCacheData.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8AC73E81EDD4DF17337EBE276AED2552 /* DKPhotoGalleryTransitionController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B833000DF956D6F16410FCD83ECC83E /* DKPhotoGalleryTransitionController.swift */; };
		8AFE4E11CFA9D6BBE900061E961CC807 /* NSButton+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = F0C6E5556D24D604C521EE2EA6ECAEBC /* NSButton+WebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8B884FC13DCEBF47DD7C0E38DE271993 /* DKPhotoGallery-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = D6D6C31AF0B01A79E49649748D95CC8D /* DKPhotoGallery-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8B988774AF987BA232BC8528324A4385 /* SDWebImageManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 36379569FFBC1115EE083C467AF5F8DD /* SDWebImageManager.m */; };
		8BA7E31ED5287C455116A3A2F21B05F0 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 544CE4FAB9748BB244C77D7C8BDDEBFC /* Foundation.framework */; };
		8C46A70E66827A4F102C828595FCAE2B /* ImageUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 4B48C6A748A2F1E2BA91A219CD57969A /* ImageUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8C47508E707EAC61A796BC803D08EB26 /* PathProviderPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 04DF7B01599F65CB3B475370DA327281 /* PathProviderPlugin.swift */; };
		8C76F79BD3C045A6D5A140C542E22A7D /* Base.lproj in Resources */ = {isa = PBXBuildFile; fileRef = C6CD924D93C1E598BB9D0B593FC2E830 /* Base.lproj */; };
		8D9465B014109D15740EFEE5962D4DAB /* SDWebImageDownloaderRequestModifier.h in Headers */ = {isa = PBXBuildFile; fileRef = 3DC112141773B19D88086A423E43782B /* SDWebImageDownloaderRequestModifier.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8DEBD5860446E4DF39482F4202E700BB /* SqfliteDarwinDatabaseAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 2B858A22D990A1775CB93A235A5DE9E8 /* SqfliteDarwinDatabaseAdditions.h */; settings = {ATTRIBUTES = (Project, ); }; };
		8EBD628337E5C63FCF3DB19BDAC50309 /* UIColor+SDHexString.m in Sources */ = {isa = PBXBuildFile; fileRef = 90C34F503B1579DDFD3EE34CC2896D1E /* UIColor+SDHexString.m */; };
		8F7B88F6234BA1FEDCD4477BEB4FFD05 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = F6394929BDB64C0D50C759F3462F2568 /* PrivacyInfo.xcprivacy */; };
		91288D91F937EB83521BA4A656F9FA96 /* SDWebImageCacheKeyFilter.h in Headers */ = {isa = PBXBuildFile; fileRef = 55F67D1DD1ABE1B09C431AEBD34DEE83 /* SDWebImageCacheKeyFilter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9252D8EDCFB679F837A682021E943126 /* DKPhotoGallery-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 3AC30EA77C83D655A38667688DA692F4 /* DKPhotoGallery-dummy.m */; };
		925A7D8A4F1AC70B87DBB97CA689EAD8 /* SDWebImageOptionsProcessor.h in Headers */ = {isa = PBXBuildFile; fileRef = 7F528570A41D2A3DBF6C8365447FC67F /* SDWebImageOptionsProcessor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		92D06CE697A81B385ED6CD55784CB620 /* messages.g.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20DF93D3A9423B1127F6B2A4F18C2D67 /* messages.g.swift */; };
		9563B81D26A63ADAC9F892FF20BFD6A4 /* SDWebImageDownloaderResponseModifier.h in Headers */ = {isa = PBXBuildFile; fileRef = 49569045C965D45C4CDCCED2FD6BDF83 /* SDWebImageDownloaderResponseModifier.h */; settings = {ATTRIBUTES = (Public, ); }; };
		965825CD5671D76D064FC44AA7DC307F /* FileInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 98ADA473B15A879BF5041B3AD6D79415 /* FileInfo.m */; };
		972C8BA1346E91E9797241BF97D010E7 /* UIButton+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = A5A669CC4569721962BE4D662595F4EB /* UIButton+WebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		973D3BE20482FA5B58D514168F6EB279 /* DKAssetGroupGridLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4E3F6404E6C26C9B0ACF2FD19B965CED /* DKAssetGroupGridLayout.swift */; };
		97E9901255A66133778F2C09F0ACF7E5 /* ru.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 6B610070B8E29B8492490CCBDB5629F6 /* ru.lproj */; };
		9837731FBA98BDC9B1E887C2BAF9B046 /* NSData+ImageContentType.m in Sources */ = {isa = PBXBuildFile; fileRef = D408E8FA31B7E347AD58F6E058D9FA0B /* NSData+ImageContentType.m */; };
		9B9239D7A4B142CA737EF14847422EB9 /* SqfliteImport.h in Headers */ = {isa = PBXBuildFile; fileRef = D81657E55669EF5C52335B7E878D96BC /* SqfliteImport.h */; settings = {ATTRIBUTES = (Project, ); }; };
		9BCBB08937EEC52DD116CB066C55A8D5 /* SDImageHEICCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 7535BAB079BE7ECD688606B8DFB5E615 /* SDImageHEICCoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9E9A0BFB5B841445946F259ECE390631 /* SDAnimatedImage.h in Headers */ = {isa = PBXBuildFile; fileRef = 91A1FCF72D4EFDF47CB92FC242E300AC /* SDAnimatedImage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9F3D33F8C2DDF65AC2676DB32180A50F /* SwiftyGif-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 5BB8792AEB552A6DA7CBAB23E8F8D07C /* SwiftyGif-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9FAD87CE8A3DBF726B3828948D8BCDFA /* DKAssetGroupDetailVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = AAD98F33D07A4C4BB68B8FCBDD6BC9BE /* DKAssetGroupDetailVC.swift */; };
		A3F22E0C025372B8A45961CE1D05B974 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0FC2783B4AADE525C40AB282A3AB03BC /* UIKit.framework */; };
		A3FAEAF60836F44BC35F22B04A8E51EF /* SDImageLoadersManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 5E4FABC63538AC83AD5D1CC10A48D20C /* SDImageLoadersManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A4625ADE856E8516CABAAC85CA725239 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0FC2783B4AADE525C40AB282A3AB03BC /* UIKit.framework */; };
		A496DFBD4C34DBE5BF401ABD15E29628 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 544CE4FAB9748BB244C77D7C8BDDEBFC /* Foundation.framework */; };
		A4EEE153531F362A113DF55B67EAB12A /* SDWebImageManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 274D5C2DFA7D1E312A79FE9BDFFCC517 /* SDWebImageManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A4F87AB0E8B8439772147231B8747C47 /* UIImageView+HighlightedWebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 6BEDD4E76F3B805950249BF32D6FE195 /* UIImageView+HighlightedWebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A647070903B6CD5B12163DB89389586B /* SDWebImageTransition.h in Headers */ = {isa = PBXBuildFile; fileRef = 72FD41EE19E21D1DA794248E7C5B03F2 /* SDWebImageTransition.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A835CFE15179C71BBCE2799792214E71 /* DKPhotoContentAnimationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 91EDD8B56B94C4C584C6D7E49DCFCCAC /* DKPhotoContentAnimationView.swift */; };
		A914797AFBD5B7FB21C4ABEFEDDF874D /* de.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 347429E150A815EB04227977FB606088 /* de.lproj */; };
		A9E7282741F0E6E07170CC19B997C71D /* NSBezierPath+SDRoundedCorners.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F53528A4922001AE68DCF4961DF1F1A /* NSBezierPath+SDRoundedCorners.h */; settings = {ATTRIBUTES = (Private, ); }; };
		A9E98C7C9ED354084E74509D40CA274F /* UIView+WebCacheOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = CEFCB823E63805B6EED70E9D86E1EBF1 /* UIView+WebCacheOperation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AB4762B65BD6BC39F1690568A5AC65C2 /* SDImageGIFCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 9F535C3DFAA4AAD14F4AA24E7D394F14 /* SDImageGIFCoder.m */; };
		AC9407F923D48AC84D5BC6CD071FBF73 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 544CE4FAB9748BB244C77D7C8BDDEBFC /* Foundation.framework */; };
		ACE9C2359BBA68AE6BE49CCDC88148C9 /* SDGraphicsImageRenderer.m in Sources */ = {isa = PBXBuildFile; fileRef = EB4CAE5C287D7E8203BE39E247CC4EAD /* SDGraphicsImageRenderer.m */; };
		AD05FDAF1737B527D7D7C6D90777504A /* SDImageCachesManager.m in Sources */ = {isa = PBXBuildFile; fileRef = DD4A071432370AF1466BC51056CAE90F /* SDImageCachesManager.m */; };
		AD4D02ADC0131FFD1F5A6AEE93E78B9B /* SDImageTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = 2C7A9003FBE1F803ED91B1154EBB0921 /* SDImageTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD83558AB73FF7106AD42998FB17F0F6 /* SDDisplayLink.h in Headers */ = {isa = PBXBuildFile; fileRef = 19EE0167D7DEA8CE74573DA880CD5231 /* SDDisplayLink.h */; settings = {ATTRIBUTES = (Private, ); }; };
		B23FF189A0A31D7463FE3A4A14F7D5E2 /* SDImageCachesManagerOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 277C2FBFE576DA601E0FECA3B79785DD /* SDImageCachesManagerOperation.m */; };
		B2AB5F342E163C9A02F2A744F9E51D25 /* UIImageView+SwiftyGif.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF3615DFF25CB5670F2C955060E9B3BA /* UIImageView+SwiftyGif.swift */; };
		B3020B2F10E079F4EFB38C79F0D1C7B9 /* SDFileAttributeHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 9CFEA55F82E0CDC85EE91E1964CB31D3 /* SDFileAttributeHelper.m */; };
		B375EA128E6579366091BAA390BBDD34 /* Pods-RunnerTests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D3DB54C128E3D88C0C9C7DDEA475ED7 /* Pods-RunnerTests-dummy.m */; };
		B4ABE8192B2833C834180C0BEC4F70E1 /* DKAssetGroupDetailCameraCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08297DFC1BEE93D385211CD515ACE820 /* DKAssetGroupDetailCameraCell.swift */; };
		B4F8AA271D84DB5D965B45ABD760667D /* SDAsyncBlockOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = FF26EEDEA50A4C0EA7BDBC663422F83F /* SDAsyncBlockOperation.m */; };
		B6B1DCFBD2BE228A10FE747CBA058145 /* SDImageAssetManager.h in Headers */ = {isa = PBXBuildFile; fileRef = A422DFB0444CD0114193E01EC348D26D /* SDImageAssetManager.h */; settings = {ATTRIBUTES = (Private, ); }; };
		B7490E541F7092A16BED82284A554320 /* SDImageAPNGCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 88022B2A5229903CAF13B27964E479CE /* SDImageAPNGCoder.m */; };
		B94AA8D6D50641B4F326222C793D6E38 /* SDDisplayLink.m in Sources */ = {isa = PBXBuildFile; fileRef = 5B579E55322D6CA76C1E3224B689E63E /* SDDisplayLink.m */; };
		BA582B8FC100119E53525F87B43AD8DC /* sqflite_darwin-sqflite_darwin_privacy in Resources */ = {isa = PBXBuildFile; fileRef = 071549DBDD4BFC0BD7D2715539015DC2 /* sqflite_darwin-sqflite_darwin_privacy */; };
		BBDBF36B701BB90281B2445BCEAD4918 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = B9CD50144D5D98D52914342223E6C11A /* Images.xcassets */; };
		BC2C6328EDE1D0DF60CAAAD876439F5A /* DKAssetGroupDetailVideoCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 969C524BB7AC0F3CEE13E00722FDF441 /* DKAssetGroupDetailVideoCell.swift */; };
		BC927B13E69C7FE5A03B50D658FD3F7B /* SDInternalMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 28F5A8AEFFDEE9163E61E979254AAE9C /* SDInternalMacros.h */; settings = {ATTRIBUTES = (Private, ); }; };
		BD09BB348998C2EBB24F9DCDB3DCC44B /* FileUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 9511EA31BFCF2E016059E98B653E9B53 /* FileUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BD200458BB8240FFC506B0F8EA215A47 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 544CE4FAB9748BB244C77D7C8BDDEBFC /* Foundation.framework */; };
		BD7309FCDB34A4895520C6238A714E17 /* SDAnimatedImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9498391D4C15AF61D113EA6FEEC1CD5E /* SDAnimatedImageView.m */; };
		BE851FC371BCCACD1D018945A88D8D2C /* SDAnimatedImageView.h in Headers */ = {isa = PBXBuildFile; fileRef = ACB34A39EAA1E17A9640C36C6A50D723 /* SDAnimatedImageView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BF3A2ED3CE6B849E3A51DC0CD4BFB3D3 /* NSImage+Compatibility.m in Sources */ = {isa = PBXBuildFile; fileRef = 338CEF1EA1E8276F4D3EEF011B36A4AD /* NSImage+Compatibility.m */; };
		BF91D04769A71CC361475A164A76C339 /* SqfliteDarwinDatabaseQueue.m in Sources */ = {isa = PBXBuildFile; fileRef = 959C9CB804494791DEA4AC088B37A164 /* SqfliteDarwinDatabaseQueue.m */; };
		C0406DA2265E37B706A6147B6610F840 /* SDImageCacheConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 5F9D93F33DB66DB8D2A0E81CAFA3790C /* SDImageCacheConfig.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C075194826F8FEA0DDCED4FA97BFAF61 /* FileUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = EE5492D53DE03B1A0240C9CF7EFF6D97 /* FileUtils.m */; };
		C0B6E2F9D080CD87F58BEABFFCAF63C3 /* SDAnimatedImageView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 134581C0343EFCFC8C17FABBB299F83C /* SDAnimatedImageView+WebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C311CB575B532E46D6AC6023D1A52B4E /* SDImageFramePool.m in Sources */ = {isa = PBXBuildFile; fileRef = A9BE1772A58103E17BC879908BE6C7F6 /* SDImageFramePool.m */; };
		C3B82132FF6A981D9583B58FB3F987DC /* DKPopoverViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 879E08ECB5A6491E56AA823984677E59 /* DKPopoverViewController.swift */; };
		C44988533307F4EFCE1CD1B33235F2D3 /* hu.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 2233EE9EA50E777B556AA8119A3A20D2 /* hu.lproj */; };
		C5D3E4F2E67D93DF0C9725894ACF986C /* SDWebImageTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = C5D5D3432B3C6231931E5F8389E9F792 /* SDWebImageTransition.m */; };
		C6FBD973D2652FED4919C7BAEBEBCFE1 /* file_picker-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 538B48C36546DDA30EF65125984C05AD /* file_picker-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C76ACC97EB13E542E55159719AD66F08 /* SDmetamacros.h in Headers */ = {isa = PBXBuildFile; fileRef = A1D16953B9BB4516D0C668C8A5B90DC0 /* SDmetamacros.h */; settings = {ATTRIBUTES = (Private, ); }; };
		CA4F19C7BE5762453A6E06571EACFBDE /* DKAssetGroupDetailBaseCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = C9F846C845D7C08BCCE9325A674C7F00 /* DKAssetGroupDetailBaseCell.swift */; };
		CA7D652DDCB378D0F7C6DB1565AF4AD9 /* zh-Hant.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 1E6A2DFF164FD0FE61DED87A57FC18EF /* zh-Hant.lproj */; };
		CBD8771C8E7F52A8CD946F49B7469D34 /* DKPhotoGallery-DKPhotoGallery in Resources */ = {isa = PBXBuildFile; fileRef = B85D4E7E4EAEF1DAD4C541A2D2E6F27D /* DKPhotoGallery-DKPhotoGallery */; };
		CC7D9D556D9C31F069B1E66A173BD3D8 /* SDMemoryCache.h in Headers */ = {isa = PBXBuildFile; fileRef = CB484E8CFD74886D763544CC37AEC0AD /* SDMemoryCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		CCE4753173C2E39ACA5E0DD3925AE25E /* NSBezierPath+SDRoundedCorners.m in Sources */ = {isa = PBXBuildFile; fileRef = 286C81353C130B4CEF304AB33D2FE608 /* NSBezierPath+SDRoundedCorners.m */; };
		CEBD84922D2CCEF26C272418EC3EB3A6 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 544CE4FAB9748BB244C77D7C8BDDEBFC /* Foundation.framework */; };
		D058D5E13A137E3CCC36CA373030B2CB /* DKPhotoWebVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 166AC360FC0B94E28269D0F840E6E0C8 /* DKPhotoWebVC.swift */; };
		D0EC45DD91E4A325BB5F359C9469E0E0 /* DKPhotoGalleryContentVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = B5B9FF3631E057751D1DB8A4858DDBEB /* DKPhotoGalleryContentVC.swift */; };
		D20330684BC80847CF02A9307CF3A67F /* pt_BR.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 3198DAF8EDB6AFE80875AA1ED942031D /* pt_BR.lproj */; };
		D2A1863914ADD3808E28EFEE36F9F745 /* SDImageAWebPCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 081EE680B588A08AEB6EA681D59E52B6 /* SDImageAWebPCoder.m */; };
		D2BD71914A7AE0AF1A511969BF0440ED /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 0E3476D86DFA146A1594E648B05D1BE8 /* Images.xcassets */; };
		D3184C912EC512CB297AC3C457679B8D /* SDImageAssetManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 5AD4A601F33390204638051B079BF863 /* SDImageAssetManager.m */; };
		D32A7684739CBFAB19BC46BBF882F2D6 /* DKImagePickerControllerBaseUIDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20FFBD460D3750371C3430443337070F /* DKImagePickerControllerBaseUIDelegate.swift */; };
		D33568D0387FBBDF795D22DCB3BF7684 /* SDDiskCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 91A04E67F7FE9F78BC324A1F2757C368 /* SDDiskCache.m */; };
		D33C42C118850292C5FED7F43E733225 /* SDWebImage.h in Headers */ = {isa = PBXBuildFile; fileRef = 504BF3089EA68E184C483BC835B4249E /* SDWebImage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D3BBCFFB4D27E74DB0DEEF2420881EA9 /* SDImageTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = 916C59FA0D53BAB0D933C7AEA28B2D8B /* SDImageTransformer.m */; };
		D48210CEBF1E59F5F8734D54807E1AC4 /* sqflite_darwin-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = A2ACAE17191CF6514EBA0AFB689F6908 /* sqflite_darwin-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D515A7E1E6CFA8483A15072786A8D43D /* DKPhotoGalleryInteractiveTransition.swift in Sources */ = {isa = PBXBuildFile; fileRef = 25325060BDAEFF98510C260AB283BDD9 /* DKPhotoGalleryInteractiveTransition.swift */; };
		D5A1D37AE11F4F7A462A1211A995AB3A /* SDImageFrame.h in Headers */ = {isa = PBXBuildFile; fileRef = 54042645301ABCE32CA1225864679578 /* SDImageFrame.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D8CCE06F26DBC8605B6334EA053C8C15 /* DKPhotoPreviewFactory.swift in Sources */ = {isa = PBXBuildFile; fileRef = 32DEA70F553151FFCCB8BD72F44902E9 /* DKPhotoPreviewFactory.swift */; };
		D90A62E984B38C99F6FFD58FC0F874ED /* DKPermissionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3FB7369B40BEC79986FBCFEFF425ACB7 /* DKPermissionView.swift */; };
		DA550F4AA20F9275348FC460ECFB072A /* SDImageLoadersManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 979FDACF6F8EE8EC3CB4555AC7E17EB1 /* SDImageLoadersManager.m */; };
		DEBF7477BF25113EF3E4F33B85C9E070 /* SDImageIOAnimatedCoderInternal.h in Headers */ = {isa = PBXBuildFile; fileRef = 7AF535030BB921937BBB79112499EBB5 /* SDImageIOAnimatedCoderInternal.h */; settings = {ATTRIBUTES = (Private, ); }; };
		DEF4F43DE43C0154931606703FF52FEB /* nb-NO.lproj in Resources */ = {isa = PBXBuildFile; fileRef = AFE11AFCB255526008A836A10B56A9F6 /* nb-NO.lproj */; };
		DFBEDDCEDCF84BE53B269597F5BC2AAC /* UIImage+ExtendedCacheData.m in Sources */ = {isa = PBXBuildFile; fileRef = 9538304586F81B4A67AF4A2C3B907159 /* UIImage+ExtendedCacheData.m */; };
		E0565EBFF313BEC5F5B6F879A7C9AE1F /* DKPDFView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DDBBD3E733F80E38B2CFA0EC7EA06927 /* DKPDFView.swift */; };
		E134B4A94F733FD002943EA8668D57CF /* SqfliteDarwinImport.h in Headers */ = {isa = PBXBuildFile; fileRef = 5F20F82418D2781C08DEACAF881C7D20 /* SqfliteDarwinImport.h */; settings = {ATTRIBUTES = (Project, ); }; };
		E18FE89C7EF64202F1DB6524F8A0EF46 /* DKImageAssetExporter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 829E16B336914A73C246CD3F1563611E /* DKImageAssetExporter.swift */; };
		E1E534DDF2AE95A227D3C3E2A408E411 /* SqflitePluginPublic.h in Headers */ = {isa = PBXBuildFile; fileRef = C38450126D90A75E45DC7E90680DB0EF /* SqflitePluginPublic.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E1FAE945E39BF2D458779D3E3397329D /* UIImage+GIF.m in Sources */ = {isa = PBXBuildFile; fileRef = 2124873B53EFFAC993AB2DABE9C3AA0A /* UIImage+GIF.m */; };
		E22F4AB0D410A011F3A06E2261BC3A31 /* SDWebImageDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = B3E1C31DB1E16039089AFC02E19FAED3 /* SDWebImageDownloader.m */; };
		E2B1125C5CAB7C33EDD8E744CFB18118 /* SDGraphicsImageRenderer.h in Headers */ = {isa = PBXBuildFile; fileRef = 7C7B792150E6976AD8EAB479D64122BC /* SDGraphicsImageRenderer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E317425280058F109FA44E0510E87A60 /* SDWebImage-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = EC43565E336CD1F29FF80F384BADF2B6 /* SDWebImage-dummy.m */; };
		E38DF98EDD47E8857D58C7FB23A86913 /* SDImageGraphics.m in Sources */ = {isa = PBXBuildFile; fileRef = 59BA15EF657BB236D624BF810C0FAE84 /* SDImageGraphics.m */; };
		E3BB5A38C15DE1E77A178BFE2F37A9FE /* DKAsset.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4208203CE9D4E600FBD80CDB1B84246F /* DKAsset.swift */; };
		E46A03ED55E9AE3A3A19BE18ACE230BD /* DKPhotoGalleryItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1942AE028FE897380602862B4382B7A3 /* DKPhotoGalleryItem.swift */; };
		E4B5C68FD3DC266A04D4D496C2720D99 /* SDAnimatedImagePlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 90185AB3075BB983E6DB3C0B5F65FECC /* SDAnimatedImagePlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E500A469F4B7F4030AA7BDE00DE7F0CA /* DKImageExtensionGallery.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1CB40042050ADD6A3661CB69BA75295D /* DKImageExtensionGallery.swift */; };
		E57C82BF0701E3B2BB07E153152AC9B8 /* SDImageFrame.m in Sources */ = {isa = PBXBuildFile; fileRef = 2F5E02AD32A21530C1955F0DFF054011 /* SDImageFrame.m */; };
		E5CE9457D9F99E7FF25847729CC14678 /* SDImageCacheConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D2F826E2347A8F47CC8359A75F95D2C /* SDImageCacheConfig.m */; };
		E681E0A5396267D4199116423D1E1656 /* SDWebImageCompat.h in Headers */ = {isa = PBXBuildFile; fileRef = E01A2BB5D464E82E55335FA1E8D45F34 /* SDWebImageCompat.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E6E723CBE2432A2CF436CFCD59F4ADE0 /* UIButton+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 60656150275FC4AC88B9FEB08BA7CDAA /* UIButton+WebCache.m */; };
		E735DF76708213075A78F196849B3C82 /* NSButton+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 025480373CD1CBC8A8C690C859E2EFA6 /* NSButton+WebCache.m */; };
		E7798C39E72D9BEA2B31DD6A0E00CFEB /* path_provider_foundation-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 178985C7B533CAB9E665328A8C8E0BF7 /* path_provider_foundation-dummy.m */; };
		E7A410A2214B204F97DE766EF8C55E46 /* SqfliteDarwinResultSet.m in Sources */ = {isa = PBXBuildFile; fileRef = E48BE6928FA5CBD2A849564C56FA59CA /* SqfliteDarwinResultSet.m */; };
		E85F172BF3897EF74D9A186C8A461C25 /* SDCallbackQueue.m in Sources */ = {isa = PBXBuildFile; fileRef = 37D28101EFF6EEA2D2FF8280F27CB047 /* SDCallbackQueue.m */; };
		E8B800C8DB1A0A612E05CE6661CCA6A9 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 544CE4FAB9748BB244C77D7C8BDDEBFC /* Foundation.framework */; };
		EB2DC96CDCF638AB89007D2DB0F3119A /* Pods-RunnerTests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 9E9B12BCE5540D02A80250C3EC16D399 /* Pods-RunnerTests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EB3BBE3BB9F48ADBB15EF97D00758A85 /* SwiftyGif.h in Headers */ = {isa = PBXBuildFile; fileRef = 9299189F45F01C0AB996C5963AA97ACD /* SwiftyGif.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EB902F7BC26360A2E2AC0D63C776DC1E /* Base.lproj in Resources */ = {isa = PBXBuildFile; fileRef = E836B6881A29F6F83BA986B92E9CCAFA /* Base.lproj */; };
		EC03FEEF428A14F362E729D495A3C4F8 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 736EAF151B6A2EF5173EE77CC12F9DD2 /* PrivacyInfo.xcprivacy */; };
		EDDDC9C8CAAC3EE0D2848F99221B404D /* SDDiskCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 3628F6C8D9E374D4847A9A203CA99959 /* SDDiskCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE51D9C7F8F2DF2BB9EDA2E308F9D751 /* zh-Hans.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 0B96DC1C8B60D0F40EA56B17D5B7FC8E /* zh-Hans.lproj */; };
		F15CE987444233246CB3AFC495B8F340 /* Pods-Runner-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = E794494AB90477BA94C681E994ADF341 /* Pods-Runner-dummy.m */; };
		F161FE59EEEA186344C3DD6251AF57E2 /* SqfliteDarwinDB.h in Headers */ = {isa = PBXBuildFile; fileRef = 594A629AD1C412C799A3F8B292A83196 /* SqfliteDarwinDB.h */; settings = {ATTRIBUTES = (Project, ); }; };
		F19CFB681450153772E11ED7BB795F62 /* SDImageCachesManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 21CA9480BB54BFAF4820FD52800AC42A /* SDImageCachesManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F2911D5F8704D0E009939E0F8301C05D /* DKPhotoGalleryTransitionDismiss.swift in Sources */ = {isa = PBXBuildFile; fileRef = F381E77C793F0E36AFF6B1F0242CB05D /* DKPhotoGalleryTransitionDismiss.swift */; };
		F58F506D3681F42D07166B150C385539 /* SqfliteDatabase.m in Sources */ = {isa = PBXBuildFile; fileRef = 308FC1A1C9F81F5189B9B9A14EB50D32 /* SqfliteDatabase.m */; };
		F66F432C6DCDC38A4612C938980D1AB7 /* SDWeakProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = 3FBB19DA89A41EF55BCE054C3F8219E7 /* SDWeakProxy.h */; settings = {ATTRIBUTES = (Private, ); }; };
		F786403A51AF7C1C59EE05EA9FCC02F7 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 3E08445AE0549D9136DB51FA2E12B062 /* PrivacyInfo.xcprivacy */; };
		F7EAB2A7E514C004342D47A0E79E4CE2 /* SqfliteImportPublic.h in Headers */ = {isa = PBXBuildFile; fileRef = FA75EDE95C90A9DFA033778F15C03450 /* SqfliteImportPublic.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F91FDDBA788037B15CB431D4D65CDB43 /* UIImage+Metadata.h in Headers */ = {isa = PBXBuildFile; fileRef = 4B830B4BA8A92CA00F890A9BE58AFCA2 /* UIImage+Metadata.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F9A533EE7A611BAF853870B79F81D344 /* SDWebImagePrefetcher.m in Sources */ = {isa = PBXBuildFile; fileRef = B1F0658CBB539606C33BF2BE5566DAEB /* SDWebImagePrefetcher.m */; };
		FAA8407C7F9306A714DB8A7A36D9B42E /* SwiftyGif-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 113D9CE3642C265D44707E73D8FE4CE0 /* SwiftyGif-dummy.m */; };
		FB87E154938D16236DBAD6896BA0579F /* SwiftyGifManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 487C5DA3B07A19BBB6E3909A2EF83CDF /* SwiftyGifManager.swift */; };
		FE1E8F08A2FFEDAE06C61226A5B24BD7 /* ImageUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 26A7F2063FCD4E6A2871535A1DF00677 /* ImageUtils.m */; };
		FEA145D9CFEB4AFF9708D6F89551B5E1 /* UIImageView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = DD9701B9F93CDA659F27D81359790054 /* UIImageView+WebCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FF4DD793F0D555D227E93561C0E52318 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 544CE4FAB9748BB244C77D7C8BDDEBFC /* Foundation.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		02F46ED09627EEEA3831CF8E44C93CE1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 3847153A6E5EEFB86565BA840768F429;
			remoteInfo = SDWebImage;
		};
		0D6F83386095938A7856CC464F0C9BD6 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6244DB400F69AD751FA848C697E64C56;
			remoteInfo = "DKImagePickerController-DKImagePickerController";
		};
		199155410D8FA21F3D4D84A1EAFE1B1E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9F5CAB8B25787EBAAFCC237AEDC74E2D;
			remoteInfo = "sqflite_darwin-sqflite_darwin_privacy";
		};
		38448869EC5CF02029C640C31718F661 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 56F581DDCB0A032454E604885E17AE3C;
			remoteInfo = path_provider_foundation;
		};
		3DF5D4E35AFAC83AF4C54C91376B67FD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CACE6618E7996464E38687E13F67D945;
			remoteInfo = "path_provider_foundation-path_provider_foundation_privacy";
		};
		41FD029F7B9AEF28B08D8A147EC1C2C1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		59B23514118AACB9825FE4252C318E9A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		5A76A97DAA1D340B2A9DD175B50EB972 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A4BE7D9267EB551461B2837139E4A9D6;
			remoteInfo = DKImagePickerController;
		};
		6705DECE40C2FF2559590A45B710C657 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6178324C15B5DCE31C127429F0C8EE8F;
			remoteInfo = sqflite_darwin;
		};
		7DE8AA217AF75A3F228BA0888FFE04E5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		8F12AF90F106C7EAF82E0A39D800CE2E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 15AEBAFA43912F898FC2E1A73025C946;
			remoteInfo = SwiftyGif;
		};
		A20673A6CFCF63F865282BB91A835FB6 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 15AEBAFA43912F898FC2E1A73025C946;
			remoteInfo = SwiftyGif;
		};
		ACD4D7FECE83BB14C9ACD58ACEF1119A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7328189F8C34132A51A6532A51BBBC06;
			remoteInfo = DKPhotoGallery;
		};
		B2C043E84CC050918C6666EA60B96A65 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		B3E9233E13B5CC96691AC50ECD81AED2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 176E48377247D5011E906CB1A39E71BA;
			remoteInfo = "SwiftyGif-SwiftyGif";
		};
		C4C068B61CE62C2C9C8178F63A7F1199 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8B74B458B450D74B75744B87BD747314;
			remoteInfo = "Pods-Runner";
		};
		D149441C6300B3F4269A2AA6D7D579B1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A4BE7D9267EB551461B2837139E4A9D6;
			remoteInfo = DKImagePickerController;
		};
		D1ED40538B4ED6C3EEAB9C21FA9F37CB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 3847153A6E5EEFB86565BA840768F429;
			remoteInfo = SDWebImage;
		};
		D6EBD15BD3E355837C2E0519D5F117AD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 76ECBFA16F19926BEDC92324235D3BE7;
			remoteInfo = "DKPhotoGallery-DKPhotoGallery";
		};
		E72C1EF213EB149AA874E04CCC3B6FF5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7328189F8C34132A51A6532A51BBBC06;
			remoteInfo = DKPhotoGallery;
		};
		EEEC926AAF76B996B89A3ACFEEBB06BC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 94CFBA7D633ECA58DF85C327B035E6A3;
			remoteInfo = "SDWebImage-SDWebImage";
		};
		F46900BE8A2627E8C84C9438B20914AC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A47785EC1549E51E61B1C4CB35A8EF3A;
			remoteInfo = file_picker;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00933C67E356438676C296252CBE29B7 /* SDImageCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCache.h; path = SDWebImage/Core/SDImageCache.h; sourceTree = "<group>"; };
		00AE2D6DD6DF51C4C2B09FB40A83486B /* SDImageAWebPCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageAWebPCoder.h; path = SDWebImage/Core/SDImageAWebPCoder.h; sourceTree = "<group>"; };
		025480373CD1CBC8A8C690C859E2EFA6 /* NSButton+WebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSButton+WebCache.m"; path = "SDWebImage/Core/NSButton+WebCache.m"; sourceTree = "<group>"; };
		02763698D1944FC236DFC2F999C0F24D /* SDImageLoader.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageLoader.h; path = SDWebImage/Core/SDImageLoader.h; sourceTree = "<group>"; };
		02DAA13E6CB0364A1D6EBC7A26C8BC22 /* file_picker.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = file_picker.release.xcconfig; sourceTree = "<group>"; };
		0317A879E8DB725D7E3A04332B0F04FE /* en.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = en.lproj; path = Sources/DKImagePickerController/Resource/Resources/en.lproj; sourceTree = "<group>"; };
		037CFB958236492D2431DAD90447D27F /* UIColor+SDHexString.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIColor+SDHexString.h"; path = "SDWebImage/Private/UIColor+SDHexString.h"; sourceTree = "<group>"; };
		03E8AD2E8B75443E12D3D3BD12A9900E /* Pods-RunnerTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-RunnerTests.debug.xcconfig"; sourceTree = "<group>"; };
		04DF7B01599F65CB3B475370DA327281 /* PathProviderPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PathProviderPlugin.swift; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift"; sourceTree = "<group>"; };
		05273041D4D9796C519A1489A5E11E11 /* SDWebImage-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SDWebImage-prefix.pch"; sourceTree = "<group>"; };
		05351FE7C3B96314E9BCD9B4BF6EE6D5 /* sqflite_darwin.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = sqflite_darwin.debug.xcconfig; sourceTree = "<group>"; };
		068F7A2A72C969797DEAACD180194E27 /* SDImageLoader.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageLoader.m; path = SDWebImage/Core/SDImageLoader.m; sourceTree = "<group>"; };
		071549DBDD4BFC0BD7D2715539015DC2 /* sqflite_darwin-sqflite_darwin_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "sqflite_darwin-sqflite_darwin_privacy"; path = sqflite_darwin_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		0810676177E4E5AE719C8068D59C85EA /* DKImagePickerControllerResource.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKImagePickerControllerResource.swift; path = Sources/DKImagePickerController/Resource/DKImagePickerControllerResource.swift; sourceTree = "<group>"; };
		081EE680B588A08AEB6EA681D59E52B6 /* SDImageAWebPCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageAWebPCoder.m; path = SDWebImage/Core/SDImageAWebPCoder.m; sourceTree = "<group>"; };
		08297DFC1BEE93D385211CD515ACE820 /* DKAssetGroupDetailCameraCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKAssetGroupDetailCameraCell.swift; path = Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailCameraCell.swift; sourceTree = "<group>"; };
		0887384FF3482C3F4C998D0C0F2D7C94 /* Flutter.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Flutter.release.xcconfig; sourceTree = "<group>"; };
		0A37DE9A55F6CEA1AF18BDEF878BF1F5 /* SwiftyGif-SwiftyGif */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "SwiftyGif-SwiftyGif"; path = SwiftyGif.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		0A64BF1E08996EC1F0DF0FB68C96926E /* SDAsyncBlockOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAsyncBlockOperation.h; path = SDWebImage/Private/SDAsyncBlockOperation.h; sourceTree = "<group>"; };
		0AFB643DA4919253F749E2836A5AAFDC /* Pods-Runner-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Runner-Info.plist"; sourceTree = "<group>"; };
		0B96DC1C8B60D0F40EA56B17D5B7FC8E /* zh-Hans.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = "zh-Hans.lproj"; path = "DKPhotoGallery/Resource/Resources/zh-Hans.lproj"; sourceTree = "<group>"; };
		0C08A8DD6D68F39F4C1D100F1FA4AFD2 /* SDWebImageError.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageError.h; path = SDWebImage/Core/SDWebImageError.h; sourceTree = "<group>"; };
		0C7630AD87ADBD4A5E947D432D1F29AB /* UIImage+ForceDecode.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+ForceDecode.m"; path = "SDWebImage/Core/UIImage+ForceDecode.m"; sourceTree = "<group>"; };
		0D2F826E2347A8F47CC8359A75F95D2C /* SDImageCacheConfig.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCacheConfig.m; path = SDWebImage/Core/SDImageCacheConfig.m; sourceTree = "<group>"; };
		0DFF43AD2C53B6E68DC2F1E052DCBBB1 /* UIImage+MemoryCacheCost.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+MemoryCacheCost.m"; path = "SDWebImage/Core/UIImage+MemoryCacheCost.m"; sourceTree = "<group>"; };
		0E3476D86DFA146A1594E648B05D1BE8 /* Images.xcassets */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = DKPhotoGallery/Resource/Resources/Images.xcassets; sourceTree = "<group>"; };
		0E7D88775D9607B5B2669B687E24DF97 /* SDWebImageDownloaderOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloaderOperation.m; path = SDWebImage/Core/SDWebImageDownloaderOperation.m; sourceTree = "<group>"; };
		0FC2783B4AADE525C40AB282A3AB03BC /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework; sourceTree = DEVELOPER_DIR; };
		0FF02D582583464E21DF635A2FD0456E /* SDWebImageDownloader.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloader.h; path = SDWebImage/Core/SDWebImageDownloader.h; sourceTree = "<group>"; };
		0FF6F2DB836BE8463976732D1C1DE2FA /* SDFileAttributeHelper.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDFileAttributeHelper.h; path = SDWebImage/Private/SDFileAttributeHelper.h; sourceTree = "<group>"; };
		112E86A0F15DF6F1F777F8360FCEE0B8 /* da.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = da.lproj; path = Sources/DKImagePickerController/Resource/Resources/da.lproj; sourceTree = "<group>"; };
		113D9CE3642C265D44707E73D8FE4CE0 /* SwiftyGif-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "SwiftyGif-dummy.m"; sourceTree = "<group>"; };
		11AA7018F95317D959D9D2E76CBB2377 /* Pods-RunnerTests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-RunnerTests-acknowledgements.markdown"; sourceTree = "<group>"; };
		12155415260FB301F696655FE9BB0F0C /* SqfliteDarwinDatabase.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteDarwinDatabase.h; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h"; sourceTree = "<group>"; };
		134581C0343EFCFC8C17FABBB299F83C /* SDAnimatedImageView+WebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "SDAnimatedImageView+WebCache.h"; path = "SDWebImage/Core/SDAnimatedImageView+WebCache.h"; sourceTree = "<group>"; };
		14EF5E412236260E6FD9CF450C1BF554 /* ResourceBundle-SDWebImage-SDWebImage-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-SDWebImage-SDWebImage-Info.plist"; sourceTree = "<group>"; };
		15DF37520825A33A1CD95D9E4581E7FA /* SDWebImageDownloaderDecryptor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloaderDecryptor.h; path = SDWebImage/Core/SDWebImageDownloaderDecryptor.h; sourceTree = "<group>"; };
		163B3B630806B6DC3C48FF69996A353D /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = Sources/DKImagePickerController/Resource/Resources/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		166AC360FC0B94E28269D0F840E6E0C8 /* DKPhotoWebVC.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoWebVC.swift; path = DKPhotoGallery/Preview/QRCode/DKPhotoWebVC.swift; sourceTree = "<group>"; };
		178985C7B533CAB9E665328A8C8E0BF7 /* path_provider_foundation-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "path_provider_foundation-dummy.m"; sourceTree = "<group>"; };
		178C00F3E8FADF6109FA4C3E3D2D1C13 /* vi.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = vi.lproj; path = Sources/DKImagePickerController/Resource/Resources/vi.lproj; sourceTree = "<group>"; };
		193E1CD46454AB8774F082A207245060 /* SDWebImageIndicator.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageIndicator.h; path = SDWebImage/Core/SDWebImageIndicator.h; sourceTree = "<group>"; };
		1942AE028FE897380602862B4382B7A3 /* DKPhotoGalleryItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoGalleryItem.swift; path = DKPhotoGallery/DKPhotoGalleryItem.swift; sourceTree = "<group>"; };
		194BCBF3F052AD9EC63D993A4533F22E /* sqflite_darwin-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "sqflite_darwin-prefix.pch"; sourceTree = "<group>"; };
		198A8538874FC857E01267BE1F017B5A /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVFoundation.framework; sourceTree = DEVELOPER_DIR; };
		19EE0167D7DEA8CE74573DA880CD5231 /* SDDisplayLink.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDDisplayLink.h; path = SDWebImage/Private/SDDisplayLink.h; sourceTree = "<group>"; };
		1ACA1B3136E32A65C499F5A086825B21 /* SDWeakProxy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWeakProxy.m; path = SDWebImage/Private/SDWeakProxy.m; sourceTree = "<group>"; };
		1B136F41D5B5FAE96EA576E768A5771D /* Pods-Runner-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Runner-acknowledgements.plist"; sourceTree = "<group>"; };
		1BB31DB611B78E2CC38EAA0E03E17C4A /* SqflitePlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqflitePlugin.h; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h"; sourceTree = "<group>"; };
		1BB8FBB76EC625D4961877C0E0F9B572 /* FilePickerPlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FilePickerPlugin.m; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-6.2.1/ios/Classes/FilePickerPlugin.m"; sourceTree = "<group>"; };
		1C395147920BA0F2ED484BDC6FDA3D00 /* tr.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = tr.lproj; path = Sources/DKImagePickerController/Resource/Resources/tr.lproj; sourceTree = "<group>"; };
		1CB40042050ADD6A3661CB69BA75295D /* DKImageExtensionGallery.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKImageExtensionGallery.swift; path = Sources/Extensions/DKImageExtensionGallery.swift; sourceTree = "<group>"; };
		1D4742F4C898315E9D8FC262C1D0C5D1 /* DKAsset+Export.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "DKAsset+Export.swift"; path = "Sources/DKImageDataManager/Model/DKAsset+Export.swift"; sourceTree = "<group>"; };
		1DD88569F894189EF84A98201C8D51F1 /* DKPhotoBasePreviewVC.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoBasePreviewVC.swift; path = DKPhotoGallery/Preview/DKPhotoBasePreviewVC.swift; sourceTree = "<group>"; };
		1E6A2DFF164FD0FE61DED87A57FC18EF /* zh-Hant.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = "zh-Hant.lproj"; path = "Sources/DKImagePickerController/Resource/Resources/zh-Hant.lproj"; sourceTree = "<group>"; };
		20DF93D3A9423B1127F6B2A4F18C2D67 /* messages.g.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = messages.g.swift; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift"; sourceTree = "<group>"; };
		20FFBD460D3750371C3430443337070F /* DKImagePickerControllerBaseUIDelegate.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKImagePickerControllerBaseUIDelegate.swift; path = Sources/DKImagePickerController/DKImagePickerControllerBaseUIDelegate.swift; sourceTree = "<group>"; };
		2124873B53EFFAC993AB2DABE9C3AA0A /* UIImage+GIF.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+GIF.m"; path = "SDWebImage/Core/UIImage+GIF.m"; sourceTree = "<group>"; };
		21CA9480BB54BFAF4820FD52800AC42A /* SDImageCachesManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCachesManager.h; path = SDWebImage/Core/SDImageCachesManager.h; sourceTree = "<group>"; };
		21CE84D5EFA21CBC46826D3A54BEBADF /* DKImagePickerController-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "DKImagePickerController-Info.plist"; sourceTree = "<group>"; };
		21E3C16CD240C7F7929F6768CC223340 /* SDAnimatedImage.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAnimatedImage.m; path = SDWebImage/Core/SDAnimatedImage.m; sourceTree = "<group>"; };
		2233EE9EA50E777B556AA8119A3A20D2 /* hu.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = hu.lproj; path = Sources/DKImagePickerController/Resource/Resources/hu.lproj; sourceTree = "<group>"; };
		2400365BFCC706592D47F38B7877DBFB /* SDImageCodersManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCodersManager.m; path = SDWebImage/Core/SDImageCodersManager.m; sourceTree = "<group>"; };
		252E12216ED1E68BEE62EF6E200684E9 /* SDWebImageCompat.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageCompat.m; path = SDWebImage/Core/SDWebImageCompat.m; sourceTree = "<group>"; };
		25325060BDAEFF98510C260AB283BDD9 /* DKPhotoGalleryInteractiveTransition.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoGalleryInteractiveTransition.swift; path = DKPhotoGallery/Transition/DKPhotoGalleryInteractiveTransition.swift; sourceTree = "<group>"; };
		26A7F2063FCD4E6A2871535A1DF00677 /* ImageUtils.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = ImageUtils.m; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-6.2.1/ios/Classes/ImageUtils.m"; sourceTree = "<group>"; };
		274D5C2DFA7D1E312A79FE9BDFFCC517 /* SDWebImageManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageManager.h; path = SDWebImage/Core/SDWebImageManager.h; sourceTree = "<group>"; };
		276A3A2F03B7F73B7987B406B0FC38A0 /* file_picker.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = file_picker.modulemap; sourceTree = "<group>"; };
		277C2FBFE576DA601E0FECA3B79785DD /* SDImageCachesManagerOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCachesManagerOperation.m; path = SDWebImage/Private/SDImageCachesManagerOperation.m; sourceTree = "<group>"; };
		286C81353C130B4CEF304AB33D2FE608 /* NSBezierPath+SDRoundedCorners.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSBezierPath+SDRoundedCorners.m"; path = "SDWebImage/Private/NSBezierPath+SDRoundedCorners.m"; sourceTree = "<group>"; };
		28D3E4584ADD56452A2C7A14D8EF3EE7 /* sqflite_darwin.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = sqflite_darwin.modulemap; sourceTree = "<group>"; };
		28EB180F2D1FEEC56C83D5F1E529AF16 /* SDWebImage-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SDWebImage-umbrella.h"; sourceTree = "<group>"; };
		28F5A8AEFFDEE9163E61E979254AAE9C /* SDInternalMacros.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDInternalMacros.h; path = SDWebImage/Private/SDInternalMacros.h; sourceTree = "<group>"; };
		29702D0F4CE361960D3843D0FC1516EB /* UIImage+Transform.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+Transform.h"; path = "SDWebImage/Core/UIImage+Transform.h"; sourceTree = "<group>"; };
		2B18DB190CB3877B1931D632678E4A50 /* Photos.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Photos.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Photos.framework; sourceTree = DEVELOPER_DIR; };
		2B833000DF956D6F16410FCD83ECC83E /* DKPhotoGalleryTransitionController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoGalleryTransitionController.swift; path = DKPhotoGallery/Transition/DKPhotoGalleryTransitionController.swift; sourceTree = "<group>"; };
		2B858A22D990A1775CB93A235A5DE9E8 /* SqfliteDarwinDatabaseAdditions.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteDarwinDatabaseAdditions.h; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h"; sourceTree = "<group>"; };
		2C7A9003FBE1F803ED91B1154EBB0921 /* SDImageTransformer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageTransformer.h; path = SDWebImage/Core/SDImageTransformer.h; sourceTree = "<group>"; };
		2D5B453D1CB610A9D15EC826630ACD14 /* DKPhotoIncrementalIndicator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoIncrementalIndicator.swift; path = DKPhotoGallery/DKPhotoIncrementalIndicator.swift; sourceTree = "<group>"; };
		2D64536825B04F311AEA4501B2961110 /* SDWebImage-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "SDWebImage-Info.plist"; sourceTree = "<group>"; };
		2E1781EFA190759359CD823BFBF0D71E /* README.md */ = {isa = PBXFileReference; includeInIndex = 1; name = README.md; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/README.md"; sourceTree = "<group>"; };
		2F53528A4922001AE68DCF4961DF1F1A /* NSBezierPath+SDRoundedCorners.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSBezierPath+SDRoundedCorners.h"; path = "SDWebImage/Private/NSBezierPath+SDRoundedCorners.h"; sourceTree = "<group>"; };
		2F5E02AD32A21530C1955F0DFF054011 /* SDImageFrame.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageFrame.m; path = SDWebImage/Core/SDImageFrame.m; sourceTree = "<group>"; };
		308378B6ADC631E53C97B459FBCD7A32 /* file_picker */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = file_picker; path = file_picker.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		308FC1A1C9F81F5189B9B9A14EB50D32 /* SqfliteDatabase.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SqfliteDatabase.m; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m"; sourceTree = "<group>"; };
		309458B7B35BF1D679F7CEFB07290156 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE"; sourceTree = "<group>"; };
		30F15D573E77ADBAB56D2DE6F61C0717 /* sqflite_darwin.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = sqflite_darwin.release.xcconfig; sourceTree = "<group>"; };
		310215F06C772DEC0A9580CD438BAA0F /* ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist"; sourceTree = "<group>"; };
		3198DAF8EDB6AFE80875AA1ED942031D /* pt_BR.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = pt_BR.lproj; path = Sources/DKImagePickerController/Resource/Resources/pt_BR.lproj; sourceTree = "<group>"; };
		32DEA70F553151FFCCB8BD72F44902E9 /* DKPhotoPreviewFactory.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoPreviewFactory.swift; path = DKPhotoGallery/DKPhotoPreviewFactory.swift; sourceTree = "<group>"; };
		333901A5FDF5D2E3D72F5A2E5F152188 /* SDImageCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCache.m; path = SDWebImage/Core/SDImageCache.m; sourceTree = "<group>"; };
		338CEF1EA1E8276F4D3EEF011B36A4AD /* NSImage+Compatibility.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSImage+Compatibility.m"; path = "SDWebImage/Core/NSImage+Compatibility.m"; sourceTree = "<group>"; };
		347429E150A815EB04227977FB606088 /* de.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = de.lproj; path = Sources/DKImagePickerController/Resource/Resources/de.lproj; sourceTree = "<group>"; };
		3510BF004F0CD883594D808691CA9377 /* DKPhotoProgressIndicatorProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoProgressIndicatorProtocol.swift; path = DKPhotoGallery/Preview/DKPhotoProgressIndicatorProtocol.swift; sourceTree = "<group>"; };
		3628F6C8D9E374D4847A9A203CA99959 /* SDDiskCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDDiskCache.h; path = SDWebImage/Core/SDDiskCache.h; sourceTree = "<group>"; };
		36379569FFBC1115EE083C467AF5F8DD /* SDWebImageManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageManager.m; path = SDWebImage/Core/SDWebImageManager.m; sourceTree = "<group>"; };
		36CABB822BC8CFBE8AF794099CFEB6B7 /* UIImage+Metadata.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+Metadata.m"; path = "SDWebImage/Core/UIImage+Metadata.m"; sourceTree = "<group>"; };
		37230F72808ED6F5F6F430C96DBD5CF8 /* SqfliteOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteOperation.h; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h"; sourceTree = "<group>"; };
		37D28101EFF6EEA2D2FF8280F27CB047 /* SDCallbackQueue.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDCallbackQueue.m; path = SDWebImage/Core/SDCallbackQueue.m; sourceTree = "<group>"; };
		3AC30EA77C83D655A38667688DA692F4 /* DKPhotoGallery-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "DKPhotoGallery-dummy.m"; sourceTree = "<group>"; };
		3B60E3606FF684C80B30BD09241E1A44 /* SqfliteCursor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteCursor.h; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h"; sourceTree = "<group>"; };
		3C13667A8AC102064180EEC15D2530F8 /* UIView+WebCacheOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIView+WebCacheOperation.m"; path = "SDWebImage/Core/UIView+WebCacheOperation.m"; sourceTree = "<group>"; };
		3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "path_provider_foundation-path_provider_foundation_privacy"; path = path_provider_foundation_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		3DC112141773B19D88086A423E43782B /* SDWebImageDownloaderRequestModifier.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloaderRequestModifier.h; path = SDWebImage/Core/SDWebImageDownloaderRequestModifier.h; sourceTree = "<group>"; };
		3E08445AE0549D9136DB51FA2E12B062 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		3EDCF61AA4F4BD3B51F408EAF838517D /* DKImagePickerController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKImagePickerController.swift; path = Sources/DKImagePickerController/DKImagePickerController.swift; sourceTree = "<group>"; };
		3F77B111088E66AE9F679184A33A1BDC /* path_provider_foundation.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = path_provider_foundation.podspec; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		3FB7369B40BEC79986FBCFEFF425ACB7 /* DKPermissionView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPermissionView.swift; path = Sources/DKImagePickerController/View/DKPermissionView.swift; sourceTree = "<group>"; };
		3FBB19DA89A41EF55BCE054C3F8219E7 /* SDWeakProxy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWeakProxy.h; path = SDWebImage/Private/SDWeakProxy.h; sourceTree = "<group>"; };
		3FF59488A08EFCA4467233D767353363 /* SDWebImage.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SDWebImage.release.xcconfig; sourceTree = "<group>"; };
		3FFC936C299275AAE56A15B974064377 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE"; sourceTree = "<group>"; };
		40619F1585AED234B940722C7704ACC0 /* NSImage+Compatibility.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSImage+Compatibility.h"; path = "SDWebImage/Core/NSImage+Compatibility.h"; sourceTree = "<group>"; };
		4208203CE9D4E600FBD80CDB1B84246F /* DKAsset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKAsset.swift; path = Sources/DKImageDataManager/Model/DKAsset.swift; sourceTree = "<group>"; };
		4482A8841A1F1E5BDF45F88FF764F9E8 /* SDWebImageError.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageError.m; path = SDWebImage/Core/SDWebImageError.m; sourceTree = "<group>"; };
		44C6A649E2C9593B4DC800E4A2248313 /* UIImage+MemoryCacheCost.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+MemoryCacheCost.h"; path = "SDWebImage/Core/UIImage+MemoryCacheCost.h"; sourceTree = "<group>"; };
		455D12245BE072B1B8B2EB98F539B8DB /* Flutter.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Flutter.debug.xcconfig; sourceTree = "<group>"; };
		4591242795E8FDCC6C65962A5EB118F0 /* DKPhotoBaseImagePreviewVC.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoBaseImagePreviewVC.swift; path = DKPhotoGallery/Preview/ImagePreview/DKPhotoBaseImagePreviewVC.swift; sourceTree = "<group>"; };
		4728BDCB3F8F6B6F2A48BB8B5BA41AA6 /* SDWebImageDownloaderConfig.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloaderConfig.m; path = SDWebImage/Core/SDWebImageDownloaderConfig.m; sourceTree = "<group>"; };
		4843A411333A6B464F720237F4261CA7 /* DKPhotoGallery.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = DKPhotoGallery.modulemap; sourceTree = "<group>"; };
		487C5DA3B07A19BBB6E3909A2EF83CDF /* SwiftyGifManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SwiftyGifManager.swift; path = SwiftyGif/SwiftyGifManager.swift; sourceTree = "<group>"; };
		49569045C965D45C4CDCCED2FD6BDF83 /* SDWebImageDownloaderResponseModifier.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloaderResponseModifier.h; path = SDWebImage/Core/SDWebImageDownloaderResponseModifier.h; sourceTree = "<group>"; };
		495DB7B626C8CFBE27440C7A30932A26 /* sqflite_darwin-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "sqflite_darwin-Info.plist"; sourceTree = "<group>"; };
		499E3722E8DACDC2AAA6C15AD1796520 /* Pods-Runner-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-Runner-umbrella.h"; sourceTree = "<group>"; };
		4AC63E9D4FF571A8F0813F399A2729E5 /* SDImageGraphics.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageGraphics.h; path = SDWebImage/Core/SDImageGraphics.h; sourceTree = "<group>"; };
		4ADB5EEEE416A586DA49B2F0449BA7D0 /* file_picker.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = file_picker.podspec; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-6.2.1/ios/file_picker.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		4B0F8B496CBA607D4401BBF1ED8F34DB /* DKPhotoGallery.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = DKPhotoGallery.debug.xcconfig; sourceTree = "<group>"; };
		4B48C6A748A2F1E2BA91A219CD57969A /* ImageUtils.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ImageUtils.h; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-6.2.1/ios/Classes/ImageUtils.h"; sourceTree = "<group>"; };
		4B830B4BA8A92CA00F890A9BE58AFCA2 /* UIImage+Metadata.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+Metadata.h"; path = "SDWebImage/Core/UIImage+Metadata.h"; sourceTree = "<group>"; };
		4BDABE8C597FD860C3ED93A0A9ADBA7C /* SwiftyGif-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SwiftyGif-prefix.pch"; sourceTree = "<group>"; };
		4D3DB54C128E3D88C0C9C7DDEA475ED7 /* Pods-RunnerTests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-RunnerTests-dummy.m"; sourceTree = "<group>"; };
		4DB32310990B66E8F5A8B0D6EE1ACBA1 /* sqflite_darwin.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = sqflite_darwin.podspec; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		4E1127B1D755504DC3CFED311B7A7C58 /* SDImageIOAnimatedCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageIOAnimatedCoder.h; path = SDWebImage/Core/SDImageIOAnimatedCoder.h; sourceTree = "<group>"; };
		4E3F6404E6C26C9B0ACF2FD19B965CED /* DKAssetGroupGridLayout.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKAssetGroupGridLayout.swift; path = Sources/DKImagePickerController/View/DKAssetGroupGridLayout.swift; sourceTree = "<group>"; };
		4FE20B1CF8B318A8D9591ED1587D7592 /* sqflite_darwin */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = sqflite_darwin; path = sqflite_darwin.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		504BF3089EA68E184C483BC835B4249E /* SDWebImage.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImage.h; path = WebImage/SDWebImage.h; sourceTree = "<group>"; };
		50AC74851B225E7DCA01FC2EB7A3D1E8 /* file_picker-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "file_picker-Info.plist"; sourceTree = "<group>"; };
		526D021BF988C55A8A8CA5AD0E47E146 /* SDAssociatedObject.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAssociatedObject.h; path = SDWebImage/Private/SDAssociatedObject.h; sourceTree = "<group>"; };
		52B9FEE904A761B23281300065D431FE /* SDWebImagePrefetcher.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImagePrefetcher.h; path = SDWebImage/Core/SDWebImagePrefetcher.h; sourceTree = "<group>"; };
		538B48C36546DDA30EF65125984C05AD /* file_picker-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "file_picker-umbrella.h"; sourceTree = "<group>"; };
		53CF8F5F962D52D648117B8528335011 /* SDImageCacheDefine.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCacheDefine.h; path = SDWebImage/Core/SDImageCacheDefine.h; sourceTree = "<group>"; };
		53E673146B08BD6614925B39051F5479 /* es.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = es.lproj; path = Sources/DKImagePickerController/Resource/Resources/es.lproj; sourceTree = "<group>"; };
		54042645301ABCE32CA1225864679578 /* SDImageFrame.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageFrame.h; path = SDWebImage/Core/SDImageFrame.h; sourceTree = "<group>"; };
		544CE4FAB9748BB244C77D7C8BDDEBFC /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		54B746288D2A859351C5D7D38D87FA92 /* SDWebImageOptionsProcessor.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageOptionsProcessor.m; path = SDWebImage/Core/SDWebImageOptionsProcessor.m; sourceTree = "<group>"; };
		54FE0A6E8684179D959A0D19CD625441 /* DKPhotoPlayerPreviewVC.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoPlayerPreviewVC.swift; path = DKPhotoGallery/Preview/PlayerPreview/DKPhotoPlayerPreviewVC.swift; sourceTree = "<group>"; };
		55F67D1DD1ABE1B09C431AEBD34DEE83 /* SDWebImageCacheKeyFilter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageCacheKeyFilter.h; path = SDWebImage/Core/SDWebImageCacheKeyFilter.h; sourceTree = "<group>"; };
		56D90C42BA11BE7FBFF39A02376CD60D /* SqfliteDarwinResultSet.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteDarwinResultSet.h; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h"; sourceTree = "<group>"; };
		5804E591074A372E46F63D0DF1B8F89E /* DKPlayerView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPlayerView.swift; path = DKPhotoGallery/Preview/PlayerPreview/DKPlayerView.swift; sourceTree = "<group>"; };
		584A66BB8106A644D5AFEBF27AFF4F56 /* DKImageBaseManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKImageBaseManager.swift; path = Sources/DKImageDataManager/DKImageBaseManager.swift; sourceTree = "<group>"; };
		58DFE05D0C28250C4F795C338572A86E /* SDImageIOCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageIOCoder.m; path = SDWebImage/Core/SDImageIOCoder.m; sourceTree = "<group>"; };
		594A629AD1C412C799A3F8B292A83196 /* SqfliteDarwinDB.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteDarwinDB.h; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h"; sourceTree = "<group>"; };
		59BA15EF657BB236D624BF810C0FAE84 /* SDImageGraphics.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageGraphics.m; path = SDWebImage/Core/SDImageGraphics.m; sourceTree = "<group>"; };
		5AD4A601F33390204638051B079BF863 /* SDImageAssetManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageAssetManager.m; path = SDWebImage/Private/SDImageAssetManager.m; sourceTree = "<group>"; };
		5B29685B553F3F02916B86BAD24D18AD /* DKPhotoGallery.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoGallery.swift; path = DKPhotoGallery/DKPhotoGallery.swift; sourceTree = "<group>"; };
		5B579E55322D6CA76C1E3224B689E63E /* SDDisplayLink.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDDisplayLink.m; path = SDWebImage/Private/SDDisplayLink.m; sourceTree = "<group>"; };
		5BB8792AEB552A6DA7CBAB23E8F8D07C /* SwiftyGif-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SwiftyGif-umbrella.h"; sourceTree = "<group>"; };
		5D5F83FABF185E339BDD61B8A9CC26B3 /* DKAsset+Fetch.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "DKAsset+Fetch.swift"; path = "Sources/DKImageDataManager/Model/DKAsset+Fetch.swift"; sourceTree = "<group>"; };
		5E4FABC63538AC83AD5D1CC10A48D20C /* SDImageLoadersManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageLoadersManager.h; path = SDWebImage/Core/SDImageLoadersManager.h; sourceTree = "<group>"; };
		5EC40588954DF1EBCB3F100414A48355 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = DKPhotoGallery/Resource/Resources/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		5F20F82418D2781C08DEACAF881C7D20 /* SqfliteDarwinImport.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteDarwinImport.h; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h"; sourceTree = "<group>"; };
		5F9D93F33DB66DB8D2A0E81CAFA3790C /* SDImageCacheConfig.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCacheConfig.h; path = SDWebImage/Core/SDImageCacheConfig.h; sourceTree = "<group>"; };
		60656150275FC4AC88B9FEB08BA7CDAA /* UIButton+WebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIButton+WebCache.m"; path = "SDWebImage/Core/UIButton+WebCache.m"; sourceTree = "<group>"; };
		64C532391A7B5F56B8622A219800288E /* path_provider_foundation-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "path_provider_foundation-umbrella.h"; sourceTree = "<group>"; };
		664F757AF84876E95DD100931F5E51D9 /* SDWebImageTransitionInternal.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageTransitionInternal.h; path = SDWebImage/Private/SDWebImageTransitionInternal.h; sourceTree = "<group>"; };
		669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-Runner"; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		688142D94D862449FC21560EC6EEA81B /* ja.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = ja.lproj; path = Sources/DKImagePickerController/Resource/Resources/ja.lproj; sourceTree = "<group>"; };
		697F1BC979DA5F63FCDA032EB7A85FD0 /* AVKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVKit.framework; sourceTree = DEVELOPER_DIR; };
		697F580784149950D87E5036BE0E9973 /* DKAssetGroupCellItemProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKAssetGroupCellItemProtocol.swift; path = Sources/DKImagePickerController/View/Cell/DKAssetGroupCellItemProtocol.swift; sourceTree = "<group>"; };
		6B610070B8E29B8492490CCBDB5629F6 /* ru.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = ru.lproj; path = Sources/DKImagePickerController/Resource/Resources/ru.lproj; sourceTree = "<group>"; };
		6B9319B2649D7165C1BE2597DF519277 /* DKImagePickerController.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = DKImagePickerController.debug.xcconfig; sourceTree = "<group>"; };
		6BEDD4E76F3B805950249BF32D6FE195 /* UIImageView+HighlightedWebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImageView+HighlightedWebCache.h"; path = "SDWebImage/Core/UIImageView+HighlightedWebCache.h"; sourceTree = "<group>"; };
		6C3345B1B3CAEDF5B03B1F731FDC492E /* Pods-RunnerTests */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-RunnerTests"; path = Pods_RunnerTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		6C502322A90F283DE0AA7996C79F1DD5 /* UIView+WebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIView+WebCache.h"; path = "SDWebImage/Core/UIView+WebCache.h"; sourceTree = "<group>"; };
		6C869079073C6295F67F64D07EE6A665 /* SDWebImageDownloaderResponseModifier.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloaderResponseModifier.m; path = SDWebImage/Core/SDWebImageDownloaderResponseModifier.m; sourceTree = "<group>"; };
		6C97386223F9362E9C2E4EB9445ECD34 /* SDWebImageDefine.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDefine.h; path = SDWebImage/Core/SDWebImageDefine.h; sourceTree = "<group>"; };
		6DEDBFCA39F0B8CFFF15C2EBF6E94E0C /* ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist"; sourceTree = "<group>"; };
		6F994383150A741DF9B1FFB4FE6D1F76 /* SDImageCoderHelper.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCoderHelper.h; path = SDWebImage/Core/SDImageCoderHelper.h; sourceTree = "<group>"; };
		714586DDD6BB2DC815586BC92DE1DF59 /* UIView+WebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIView+WebCache.m"; path = "SDWebImage/Core/UIView+WebCache.m"; sourceTree = "<group>"; };
		7247E064ECCE473BC39CE9C6B1E7C0B3 /* DKImagePickerController-DKImagePickerController */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "DKImagePickerController-DKImagePickerController"; path = DKImagePickerController.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		728A5ED1DD60BAC8C8F382A87B976F84 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		72FD41EE19E21D1DA794248E7C5B03F2 /* SDWebImageTransition.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageTransition.h; path = SDWebImage/Core/SDWebImageTransition.h; sourceTree = "<group>"; };
		736EAF151B6A2EF5173EE77CC12F9DD2 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = SwiftyGif/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		746BBF33504A0699A90AAF0DF6BF1C3A /* FileInfo.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FileInfo.h; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-6.2.1/ios/Classes/FileInfo.h"; sourceTree = "<group>"; };
		74E9EE0A4F521C8E22CD4FD973883814 /* UIImageView+WebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImageView+WebCache.m"; path = "SDWebImage/Core/UIImageView+WebCache.m"; sourceTree = "<group>"; };
		7535BAB079BE7ECD688606B8DFB5E615 /* SDImageHEICCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageHEICCoder.h; path = SDWebImage/Core/SDImageHEICCoder.h; sourceTree = "<group>"; };
		755DC25759817D07088EC20F1737A233 /* path_provider_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "path_provider_foundation-Info.plist"; sourceTree = "<group>"; };
		75DCD7E22599C1BAAF8EE36561E572A0 /* SDImageFramePool.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageFramePool.h; path = SDWebImage/Private/SDImageFramePool.h; sourceTree = "<group>"; };
		767E2ED40586F6E12141BC5B86B46448 /* NSImageView+SwiftyGif.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSImageView+SwiftyGif.swift"; path = "SwiftyGif/NSImageView+SwiftyGif.swift"; sourceTree = "<group>"; };
		773BFCE41424EB8EBB72EF3F6A5FB719 /* Pods-Runner-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-Runner-frameworks.sh"; sourceTree = "<group>"; };
		78C26B66151FF0666D53FF95DFB24CC6 /* SDWebImageCacheSerializer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageCacheSerializer.h; path = SDWebImage/Core/SDWebImageCacheSerializer.h; sourceTree = "<group>"; };
		79F83F4D505FA40C1CD18A8029181D59 /* Pods-RunnerTests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-RunnerTests-acknowledgements.plist"; sourceTree = "<group>"; };
		7AD67EFB9A7864151301DC2890207AA2 /* SwiftyGif */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = SwiftyGif; path = SwiftyGif.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		7AF535030BB921937BBB79112499EBB5 /* SDImageIOAnimatedCoderInternal.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageIOAnimatedCoderInternal.h; path = SDWebImage/Private/SDImageIOAnimatedCoderInternal.h; sourceTree = "<group>"; };
		7B5E6D4EED9D7031B7EF9CBADD83B364 /* ImageIO.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ImageIO.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/ImageIO.framework; sourceTree = DEVELOPER_DIR; };
		7C26E48B6A32C3499D867872EB112538 /* DKAssetGroup.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKAssetGroup.swift; path = Sources/DKImageDataManager/Model/DKAssetGroup.swift; sourceTree = "<group>"; };
		7C7B792150E6976AD8EAB479D64122BC /* SDGraphicsImageRenderer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDGraphicsImageRenderer.h; path = SDWebImage/Core/SDGraphicsImageRenderer.h; sourceTree = "<group>"; };
		7E3461DFB3DF66071A606253691043A3 /* SDImageCachesManagerOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCachesManagerOperation.h; path = SDWebImage/Private/SDImageCachesManagerOperation.h; sourceTree = "<group>"; };
		7F528570A41D2A3DBF6C8365447FC67F /* SDWebImageOptionsProcessor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageOptionsProcessor.h; path = SDWebImage/Core/SDWebImageOptionsProcessor.h; sourceTree = "<group>"; };
		8012156CA8D5D683C941BD52301448F2 /* SDAnimatedImagePlayer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAnimatedImagePlayer.m; path = SDWebImage/Core/SDAnimatedImagePlayer.m; sourceTree = "<group>"; };
		809BE2F73F70AC9C53AB791EF55F89EE /* SDAssociatedObject.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAssociatedObject.m; path = SDWebImage/Private/SDAssociatedObject.m; sourceTree = "<group>"; };
		81C7F9DBD43A2C5DE5FFC88258C80F6D /* SwiftyGif.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SwiftyGif.release.xcconfig; sourceTree = "<group>"; };
		823EAB70C33F9BBC2D30E14237611EE6 /* DKAssetGroupListVC.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKAssetGroupListVC.swift; path = Sources/DKImagePickerController/View/DKAssetGroupListVC.swift; sourceTree = "<group>"; };
		82762ED739DDD5E6A82A1AD119D1DC5A /* SDInternalMacros.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDInternalMacros.m; path = SDWebImage/Private/SDInternalMacros.m; sourceTree = "<group>"; };
		829E16B336914A73C246CD3F1563611E /* DKImageAssetExporter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKImageAssetExporter.swift; path = Sources/DKImagePickerController/DKImageAssetExporter.swift; sourceTree = "<group>"; };
		83191A3DF10B46A748B80206E6DCA58B /* SDWebImage.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = SDWebImage.modulemap; sourceTree = "<group>"; };
		83A952E2F026F6B77F742162DC2F406D /* FilePickerPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FilePickerPlugin.h; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-6.2.1/ios/Classes/FilePickerPlugin.h"; sourceTree = "<group>"; };
		84F8812EAE588F093B026AD494074702 /* SDImageIOAnimatedCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageIOAnimatedCoder.m; path = SDWebImage/Core/SDImageIOAnimatedCoder.m; sourceTree = "<group>"; };
		86371477A8C16F168BFD3A274F96B823 /* sqflite_darwin-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "sqflite_darwin-dummy.m"; sourceTree = "<group>"; };
		879E08ECB5A6491E56AA823984677E59 /* DKPopoverViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPopoverViewController.swift; path = Sources/DKImagePickerController/DKPopoverViewController.swift; sourceTree = "<group>"; };
		87DB018C0EBE728B28D6A5AE5285AAC0 /* SDImageIOCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageIOCoder.h; path = SDWebImage/Core/SDImageIOCoder.h; sourceTree = "<group>"; };
		88022B2A5229903CAF13B27964E479CE /* SDImageAPNGCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageAPNGCoder.m; path = SDWebImage/Core/SDImageAPNGCoder.m; sourceTree = "<group>"; };
		896562C9511358EE67C8CBBEE34E6444 /* UIView+WebCacheState.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIView+WebCacheState.m"; path = "SDWebImage/Core/UIView+WebCacheState.m"; sourceTree = "<group>"; };
		8B040808633321DF5B09556AF2A4DE3E /* SDWebImageOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageOperation.m; path = SDWebImage/Core/SDWebImageOperation.m; sourceTree = "<group>"; };
		8B15D97E1E4B86D44B9D9D97ABFF88D1 /* DKImagePickerController-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "DKImagePickerController-umbrella.h"; sourceTree = "<group>"; };
		8CDB4904C17A77BF2CDFC740DD72E34C /* NSData+ImageContentType.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSData+ImageContentType.h"; path = "SDWebImage/Core/NSData+ImageContentType.h"; sourceTree = "<group>"; };
		8E41FDDCC3086A68D2AB2DE9DCBBFBA3 /* ar.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = ar.lproj; path = Sources/DKImagePickerController/Resource/Resources/ar.lproj; sourceTree = "<group>"; };
		8EF44FC4EE5A1070C421EEFE9B6B44D3 /* SDImageCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCoder.h; path = SDWebImage/Core/SDImageCoder.h; sourceTree = "<group>"; };
		9002FF03C62F1DB866747FD7D2380865 /* DKImageExtensionController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKImageExtensionController.swift; path = Sources/DKImagePickerController/DKImageExtensionController.swift; sourceTree = "<group>"; };
		90185AB3075BB983E6DB3C0B5F65FECC /* SDAnimatedImagePlayer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAnimatedImagePlayer.h; path = SDWebImage/Core/SDAnimatedImagePlayer.h; sourceTree = "<group>"; };
		90C34F503B1579DDFD3EE34CC2896D1E /* UIColor+SDHexString.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIColor+SDHexString.m"; path = "SDWebImage/Private/UIColor+SDHexString.m"; sourceTree = "<group>"; };
		9121B2897840B068C0E6BD952C182343 /* DKAssetGroupDetailImageCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKAssetGroupDetailImageCell.swift; path = Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailImageCell.swift; sourceTree = "<group>"; };
		916C59FA0D53BAB0D933C7AEA28B2D8B /* SDImageTransformer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageTransformer.m; path = SDWebImage/Core/SDImageTransformer.m; sourceTree = "<group>"; };
		91A04E67F7FE9F78BC324A1F2757C368 /* SDDiskCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDDiskCache.m; path = SDWebImage/Core/SDDiskCache.m; sourceTree = "<group>"; };
		91A1FCF72D4EFDF47CB92FC242E300AC /* SDAnimatedImage.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAnimatedImage.h; path = SDWebImage/Core/SDAnimatedImage.h; sourceTree = "<group>"; };
		91EDD8B56B94C4C584C6D7E49DCFCCAC /* DKPhotoContentAnimationView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoContentAnimationView.swift; path = DKPhotoGallery/Preview/DKPhotoContentAnimationView.swift; sourceTree = "<group>"; };
		9299189F45F01C0AB996C5963AA97ACD /* SwiftyGif.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SwiftyGif.h; path = SwiftyGif/SwiftyGif.h; sourceTree = "<group>"; };
		93E1F359B238F540B0795D9287E2F3EF /* SDImageHEICCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageHEICCoder.m; path = SDWebImage/Core/SDImageHEICCoder.m; sourceTree = "<group>"; };
		9498391D4C15AF61D113EA6FEEC1CD5E /* SDAnimatedImageView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAnimatedImageView.m; path = SDWebImage/Core/SDAnimatedImageView.m; sourceTree = "<group>"; };
		9511EA31BFCF2E016059E98B653E9B53 /* FileUtils.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FileUtils.h; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-6.2.1/ios/Classes/FileUtils.h"; sourceTree = "<group>"; };
		9538304586F81B4A67AF4A2C3B907159 /* UIImage+ExtendedCacheData.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+ExtendedCacheData.m"; path = "SDWebImage/Core/UIImage+ExtendedCacheData.m"; sourceTree = "<group>"; };
		95956B095C04FF537194DCA28F66FF38 /* DKImagePickerController.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = DKImagePickerController.release.xcconfig; sourceTree = "<group>"; };
		959C9CB804494791DEA4AC088B37A164 /* SqfliteDarwinDatabaseQueue.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SqfliteDarwinDatabaseQueue.m; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m"; sourceTree = "<group>"; };
		969C524BB7AC0F3CEE13E00722FDF441 /* DKAssetGroupDetailVideoCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKAssetGroupDetailVideoCell.swift; path = Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailVideoCell.swift; sourceTree = "<group>"; };
		979FDACF6F8EE8EC3CB4555AC7E17EB1 /* SDImageLoadersManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageLoadersManager.m; path = SDWebImage/Core/SDImageLoadersManager.m; sourceTree = "<group>"; };
		9802B7F6F402B20B8A338B418A577F8B /* SDCallbackQueue.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDCallbackQueue.h; path = SDWebImage/Core/SDCallbackQueue.h; sourceTree = "<group>"; };
		98ADA473B15A879BF5041B3AD6D79415 /* FileInfo.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FileInfo.m; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-6.2.1/ios/Classes/FileInfo.m"; sourceTree = "<group>"; };
		98EC63143D86891CEF60EAF54154907C /* ObjcAssociatedWeakObject.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ObjcAssociatedWeakObject.swift; path = SwiftyGif/ObjcAssociatedWeakObject.swift; sourceTree = "<group>"; };
		99115CD6710556527DF72A648E0E6DEF /* DKImagePickerController-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "DKImagePickerController-dummy.m"; sourceTree = "<group>"; };
		99347923C02D8250ACDFB15050A661AC /* DKPhotoGallery-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "DKPhotoGallery-prefix.pch"; sourceTree = "<group>"; };
		9A644C7ABD2777AF2D4541D200284C1E /* NSImage+SwiftyGif.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "NSImage+SwiftyGif.swift"; path = "SwiftyGif/NSImage+SwiftyGif.swift"; sourceTree = "<group>"; };
		9C4C564FCE56D9FEC88A0D69FF61656E /* SDWebImageCacheSerializer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageCacheSerializer.m; path = SDWebImage/Core/SDWebImageCacheSerializer.m; sourceTree = "<group>"; };
		9CFEA55F82E0CDC85EE91E1964CB31D3 /* SDFileAttributeHelper.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDFileAttributeHelper.m; path = SDWebImage/Private/SDFileAttributeHelper.m; sourceTree = "<group>"; };
		9CFF3FC67131B888135B0062F9DACD6E /* SDWebImageCacheKeyFilter.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageCacheKeyFilter.m; path = SDWebImage/Core/SDWebImageCacheKeyFilter.m; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		9E764D8CAB9CEF2030E6C627DEADC064 /* DKImageDataManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKImageDataManager.swift; path = Sources/DKImageDataManager/DKImageDataManager.swift; sourceTree = "<group>"; };
		9E9B12BCE5540D02A80250C3EC16D399 /* Pods-RunnerTests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-RunnerTests-umbrella.h"; sourceTree = "<group>"; };
		9F535C3DFAA4AAD14F4AA24E7D394F14 /* SDImageGIFCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageGIFCoder.m; path = SDWebImage/Core/SDImageGIFCoder.m; sourceTree = "<group>"; };
		A08D8B2447463F52ABBD47D88E4BA3E9 /* ResourceBundle-SwiftyGif-SwiftyGif-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-SwiftyGif-SwiftyGif-Info.plist"; sourceTree = "<group>"; };
		A1D16953B9BB4516D0C668C8A5B90DC0 /* SDmetamacros.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDmetamacros.h; path = SDWebImage/Private/SDmetamacros.h; sourceTree = "<group>"; };
		A2676DCDD2D5F740722D70C227DC2C00 /* UIImage+Transform.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+Transform.m"; path = "SDWebImage/Core/UIImage+Transform.m"; sourceTree = "<group>"; };
		A2ACAE17191CF6514EBA0AFB689F6908 /* sqflite_darwin-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "sqflite_darwin-umbrella.h"; sourceTree = "<group>"; };
		A422DFB0444CD0114193E01EC348D26D /* SDImageAssetManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageAssetManager.h; path = SDWebImage/Private/SDImageAssetManager.h; sourceTree = "<group>"; };
		A5A669CC4569721962BE4D662595F4EB /* UIButton+WebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIButton+WebCache.h"; path = "SDWebImage/Core/UIButton+WebCache.h"; sourceTree = "<group>"; };
		A7BD1B4BB7B7B7F323AED9971F4C8897 /* zh-Hans.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = "zh-Hans.lproj"; path = "Sources/DKImagePickerController/Resource/Resources/zh-Hans.lproj"; sourceTree = "<group>"; };
		A910FE54FECA1F4AB9FF751A7369D2CA /* SDWebImageIndicator.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageIndicator.m; path = SDWebImage/Core/SDWebImageIndicator.m; sourceTree = "<group>"; };
		A9BE1772A58103E17BC879908BE6C7F6 /* SDImageFramePool.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageFramePool.m; path = SDWebImage/Private/SDImageFramePool.m; sourceTree = "<group>"; };
		AAD87A5D5939F79B971D95C1E00D1631 /* SqfliteOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SqfliteOperation.m; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m"; sourceTree = "<group>"; };
		AAD98F33D07A4C4BB68B8FCBDD6BC9BE /* DKAssetGroupDetailVC.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKAssetGroupDetailVC.swift; path = Sources/DKImagePickerController/View/DKAssetGroupDetailVC.swift; sourceTree = "<group>"; };
		AC66E20519F07E1D618916DC51EBBF48 /* DKPhotoGallery.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = DKPhotoGallery.release.xcconfig; sourceTree = "<group>"; };
		AC8403BA1DA69DF47C2FE4D61B2C72D2 /* SDWebImageDownloaderDecryptor.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloaderDecryptor.m; path = SDWebImage/Core/SDWebImageDownloaderDecryptor.m; sourceTree = "<group>"; };
		AC8FE152530FBDCE460186B326C1A8FB /* Pods-RunnerTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-RunnerTests.release.xcconfig"; sourceTree = "<group>"; };
		ACB34A39EAA1E17A9640C36C6A50D723 /* SDAnimatedImageView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAnimatedImageView.h; path = SDWebImage/Core/SDAnimatedImageView.h; sourceTree = "<group>"; };
		AE157A33FEF959A214796BFF348717F6 /* path_provider_foundation */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = path_provider_foundation; path = path_provider_foundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		AF045921C014ECFE871641656F7A8263 /* UIView+WebCacheState.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIView+WebCacheState.h"; path = "SDWebImage/Core/UIView+WebCacheState.h"; sourceTree = "<group>"; };
		AF2308829CFEABB97817F0E52ED98D6A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = WebImage/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		AF3615DFF25CB5670F2C955060E9B3BA /* UIImageView+SwiftyGif.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIImageView+SwiftyGif.swift"; path = "SwiftyGif/UIImageView+SwiftyGif.swift"; sourceTree = "<group>"; };
		AF5810585D4898042EF96B68C6037F72 /* path_provider_foundation-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "path_provider_foundation-prefix.pch"; sourceTree = "<group>"; };
		AF9436FC3C179BE6332F89289A0D594D /* it.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = it.lproj; path = Sources/DKImagePickerController/Resource/Resources/it.lproj; sourceTree = "<group>"; };
		AFE11AFCB255526008A836A10B56A9F6 /* nb-NO.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = "nb-NO.lproj"; path = "Sources/DKImagePickerController/Resource/Resources/nb-NO.lproj"; sourceTree = "<group>"; };
		B00A5ECAED4CA0C16FF7C65365BC0334 /* Pods-RunnerTests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-RunnerTests-Info.plist"; sourceTree = "<group>"; };
		B04D9195CE7D91E33317BE7D5613E39E /* SDImageCoderHelper.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCoderHelper.m; path = SDWebImage/Core/SDImageCoderHelper.m; sourceTree = "<group>"; };
		B0B214D775196BA7CA8E17E53048A493 /* SDWebImage */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = SDWebImage; path = SDWebImage.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0BD57AB3672E53828D11C2A3368023A /* Pods-RunnerTests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-RunnerTests.modulemap"; sourceTree = "<group>"; };
		B1B247DE42D782218EFEA0CB0F6D6454 /* DKPhotoGallery */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = DKPhotoGallery; path = DKPhotoGallery.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B1DDFF0CB273AE6904403109B3178A1A /* Flutter.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; path = Flutter.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		B1F0658CBB539606C33BF2BE5566DAEB /* SDWebImagePrefetcher.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImagePrefetcher.m; path = SDWebImage/Core/SDWebImagePrefetcher.m; sourceTree = "<group>"; };
		B226930FC757308F96D78BC70158FCF2 /* DKPhotoImageDownloader.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoImageDownloader.swift; path = DKPhotoGallery/Preview/ImagePreview/DKPhotoImageDownloader.swift; sourceTree = "<group>"; };
		B281E855782C63501215FBC637F8DC9B /* SDWebImage.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SDWebImage.debug.xcconfig; sourceTree = "<group>"; };
		B289029A2A9E63EE71F2F75DC7FDDF25 /* SDImageCacheDefine.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCacheDefine.m; path = SDWebImage/Core/SDImageCacheDefine.m; sourceTree = "<group>"; };
		B3E1C31DB1E16039089AFC02E19FAED3 /* SDWebImageDownloader.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloader.m; path = SDWebImage/Core/SDWebImageDownloader.m; sourceTree = "<group>"; };
		B5B9FF3631E057751D1DB8A4858DDBEB /* DKPhotoGalleryContentVC.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoGalleryContentVC.swift; path = DKPhotoGallery/DKPhotoGalleryContentVC.swift; sourceTree = "<group>"; };
		B6FB2AE1358B06D39A0622A9623A6E90 /* SDWebImageDownloaderConfig.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloaderConfig.h; path = SDWebImage/Core/SDWebImageDownloaderConfig.h; sourceTree = "<group>"; };
		B703785507B22BAF0B800737429577EC /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		B71ED232D67949A5C2AE8CD69403697E /* DKPhotoQRCodeResultVC.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoQRCodeResultVC.swift; path = DKPhotoGallery/Preview/QRCode/DKPhotoQRCodeResultVC.swift; sourceTree = "<group>"; };
		B79ABF45C4496A4586DA561EA91B5866 /* ResourceBundle-DKPhotoGallery-DKPhotoGallery-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-DKPhotoGallery-DKPhotoGallery-Info.plist"; sourceTree = "<group>"; };
		B8394AFF286008A68FCF59270538841E /* UIImage+MultiFormat.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+MultiFormat.m"; path = "SDWebImage/Core/UIImage+MultiFormat.m"; sourceTree = "<group>"; };
		B85D4E7E4EAEF1DAD4C541A2D2E6F27D /* DKPhotoGallery-DKPhotoGallery */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "DKPhotoGallery-DKPhotoGallery"; path = DKPhotoGallery.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		B9CD50144D5D98D52914342223E6C11A /* Images.xcassets */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = Sources/DKImagePickerController/Resource/Resources/Images.xcassets; sourceTree = "<group>"; };
		BB03859F9C5341E9D17E770C6121EA66 /* SDImageGIFCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageGIFCoder.h; path = SDWebImage/Core/SDImageGIFCoder.h; sourceTree = "<group>"; };
		BD048AA3CEB4874E738992566F3C2588 /* UIImage+GIF.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+GIF.h"; path = "SDWebImage/Core/UIImage+GIF.h"; sourceTree = "<group>"; };
		BF0F881597059D9535AB60F9F91D7677 /* path_provider_foundation.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = path_provider_foundation.release.xcconfig; sourceTree = "<group>"; };
		C0FA2332A3665702D9B2ECB0498FF316 /* SDWebImageDefine.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDefine.m; path = SDWebImage/Core/SDWebImageDefine.m; sourceTree = "<group>"; };
		C18CCDB70542EF82D63836F264A1D7A1 /* SqfliteCursor.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SqfliteCursor.m; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m"; sourceTree = "<group>"; };
		C24C1A035F22D24883C6F1876F274C6F /* Pods-Runner.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-Runner.modulemap"; sourceTree = "<group>"; };
		C38450126D90A75E45DC7E90680DB0EF /* SqflitePluginPublic.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqflitePluginPublic.h; path = "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h"; sourceTree = "<group>"; };
		C39FBA296695B60A112108133BC9FF16 /* SDWebImageOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageOperation.h; path = SDWebImage/Core/SDWebImageOperation.h; sourceTree = "<group>"; };
		C50FD10CD19FF6999637BA5ECAF1681B /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		C5D5D3432B3C6231931E5F8389E9F792 /* SDWebImageTransition.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageTransition.m; path = SDWebImage/Core/SDWebImageTransition.m; sourceTree = "<group>"; };
		C6CD924D93C1E598BB9D0B593FC2E830 /* Base.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = Base.lproj; path = Sources/DKImagePickerController/Resource/Resources/Base.lproj; sourceTree = "<group>"; };
		C79B86A180E741505587DBDDD31D82D0 /* SqfliteDarwinDatabaseAdditions.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SqfliteDarwinDatabaseAdditions.m; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m"; sourceTree = "<group>"; };
		C9F846C845D7C08BCCE9325A674C7F00 /* DKAssetGroupDetailBaseCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKAssetGroupDetailBaseCell.swift; path = Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailBaseCell.swift; sourceTree = "<group>"; };
		CB484E8CFD74886D763544CC37AEC0AD /* SDMemoryCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDMemoryCache.h; path = SDWebImage/Core/SDMemoryCache.h; sourceTree = "<group>"; };
		CB507F3628C2744ADBD6115BD432C868 /* file_picker.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = file_picker.debug.xcconfig; sourceTree = "<group>"; };
		CB8932706B28A8B6A8A1A1FEF3423189 /* SqfliteDarwinDatabaseQueue.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteDarwinDatabaseQueue.h; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h"; sourceTree = "<group>"; };
		CD43F23FAB132293A968A5F2629F015C /* SqfliteDarwinDatabase.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SqfliteDarwinDatabase.m; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m"; sourceTree = "<group>"; };
		CDED959FEA7ECCAF5B87F7E23F2FDF74 /* SDAnimatedImageRep.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDAnimatedImageRep.h; path = SDWebImage/Core/SDAnimatedImageRep.h; sourceTree = "<group>"; };
		CE1CDB9D2119321BD845E825C048B9FA /* en.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = en.lproj; path = DKPhotoGallery/Resource/Resources/en.lproj; sourceTree = "<group>"; };
		CE48C851D036CBD2600AD30FE109D805 /* DKPhotoImageUtility.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoImageUtility.swift; path = DKPhotoGallery/Preview/ImagePreview/DKPhotoImageUtility.swift; sourceTree = "<group>"; };
		CEF5DE5C78B0CA1EAE3C50C5BD2CF484 /* DKPhotoPDFPreviewVC.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoPDFPreviewVC.swift; path = DKPhotoGallery/Preview/PDFPreview/DKPhotoPDFPreviewVC.swift; sourceTree = "<group>"; };
		CEFCB823E63805B6EED70E9D86E1EBF1 /* UIView+WebCacheOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIView+WebCacheOperation.h"; path = "SDWebImage/Core/UIView+WebCacheOperation.h"; sourceTree = "<group>"; };
		CF1281E58AA1045D4B7F33FC56691C42 /* SDWebImage-SDWebImage */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "SDWebImage-SDWebImage"; path = SDWebImage.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		D05F6AFDF66188951FC5DD9C8922661E /* SDDeviceHelper.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDDeviceHelper.h; path = SDWebImage/Private/SDDeviceHelper.h; sourceTree = "<group>"; };
		D0D54FF993CCC9BC5769D73BBC879CAD /* fr.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = fr.lproj; path = Sources/DKImagePickerController/Resource/Resources/fr.lproj; sourceTree = "<group>"; };
		D3EA1BABD76B44E160C4203748AB3759 /* DKPhotoGalleryResource.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoGalleryResource.swift; path = DKPhotoGallery/Resource/DKPhotoGalleryResource.swift; sourceTree = "<group>"; };
		D408E8FA31B7E347AD58F6E058D9FA0B /* NSData+ImageContentType.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSData+ImageContentType.m"; path = "SDWebImage/Core/NSData+ImageContentType.m"; sourceTree = "<group>"; };
		D6B3E3D64CA0E0B3F6CE198AA3DD2B80 /* UIImage+ExtendedCacheData.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+ExtendedCacheData.h"; path = "SDWebImage/Core/UIImage+ExtendedCacheData.h"; sourceTree = "<group>"; };
		D6D6C31AF0B01A79E49649748D95CC8D /* DKPhotoGallery-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "DKPhotoGallery-umbrella.h"; sourceTree = "<group>"; };
		D81065B2050933B69046A61F756EF33D /* SDImageCoder.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCoder.m; path = SDWebImage/Core/SDImageCoder.m; sourceTree = "<group>"; };
		D81657E55669EF5C52335B7E878D96BC /* SqfliteImport.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteImport.h; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h"; sourceTree = "<group>"; };
		DA4FBD87572142A21B8226CCAA644401 /* SqflitePlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SqflitePlugin.m; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m"; sourceTree = "<group>"; };
		DA911E92959967B9215184E7E33D1537 /* DKPhotoGalleryTransitionPresent.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoGalleryTransitionPresent.swift; path = DKPhotoGallery/Transition/DKPhotoGalleryTransitionPresent.swift; sourceTree = "<group>"; };
		DC15D00FBE9A01A092BDD51B2537C3DD /* file_picker-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "file_picker-prefix.pch"; sourceTree = "<group>"; };
		DD433F4B904B37303A119E0F5F06322E /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-6.2.1/LICENSE"; sourceTree = "<group>"; };
		DD4A071432370AF1466BC51056CAE90F /* SDImageCachesManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDImageCachesManager.m; path = SDWebImage/Core/SDImageCachesManager.m; sourceTree = "<group>"; };
		DD8D5D70EF7F01FB42EC000BDD1D0A02 /* SDWebImageDownloaderRequestModifier.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDWebImageDownloaderRequestModifier.m; path = SDWebImage/Core/SDWebImageDownloaderRequestModifier.m; sourceTree = "<group>"; };
		DD9701B9F93CDA659F27D81359790054 /* UIImageView+WebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImageView+WebCache.h"; path = "SDWebImage/Core/UIImageView+WebCache.h"; sourceTree = "<group>"; };
		DDBBD3E733F80E38B2CFA0EC7EA06927 /* DKPDFView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPDFView.swift; path = DKPhotoGallery/Preview/PDFPreview/DKPDFView.swift; sourceTree = "<group>"; };
		DDCFB0568851722C689867C3D8B9B3FF /* SDAnimatedImageView+WebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "SDAnimatedImageView+WebCache.m"; path = "SDWebImage/Core/SDAnimatedImageView+WebCache.m"; sourceTree = "<group>"; };
		DE8DBD29B147EA62BF91ADC387ECE763 /* DKPhotoImagePreviewVC.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoImagePreviewVC.swift; path = DKPhotoGallery/Preview/ImagePreview/DKPhotoImagePreviewVC.swift; sourceTree = "<group>"; };
		E01A2BB5D464E82E55335FA1E8D45F34 /* SDWebImageCompat.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageCompat.h; path = SDWebImage/Core/SDWebImageCompat.h; sourceTree = "<group>"; };
		E02C1C4A89BB4CC0BC278D8C4D0D8BD5 /* DKPhotoImageView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoImageView.swift; path = DKPhotoGallery/Preview/ImagePreview/DKPhotoImageView.swift; sourceTree = "<group>"; };
		E0844F0148D94DF619A8A29EFF1275B8 /* DKPhotoGallery-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "DKPhotoGallery-Info.plist"; sourceTree = "<group>"; };
		E28331D54906A1F44EEFEF035A48AE50 /* DKImagePickerController.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = DKImagePickerController.modulemap; sourceTree = "<group>"; };
		E2C12FE444D39CE575CAC618A1B31085 /* DKImageGroupDataManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKImageGroupDataManager.swift; path = Sources/DKImageDataManager/DKImageGroupDataManager.swift; sourceTree = "<group>"; };
		E48BE6928FA5CBD2A849564C56FA59CA /* SqfliteDarwinResultSet.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SqfliteDarwinResultSet.m; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m"; sourceTree = "<group>"; };
		E527F5CD3726095AD1461D752CF3CF11 /* file_picker-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "file_picker-dummy.m"; sourceTree = "<group>"; };
		E5B8BAD4DCE5B5C285F6A7E080EFC0A9 /* nl.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = nl.lproj; path = Sources/DKImagePickerController/Resource/Resources/nl.lproj; sourceTree = "<group>"; };
		E5EC70DD77A55601BA2F9BD3A62E67A0 /* DKImagePickerController-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "DKImagePickerController-prefix.pch"; sourceTree = "<group>"; };
		E736E0C9C5B73D27B8865E363E2108AC /* Pods-RunnerTests.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-RunnerTests.profile.xcconfig"; sourceTree = "<group>"; };
		E754497AF84F2E36D088C1EB20390ABB /* SwiftyGif.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SwiftyGif.debug.xcconfig; sourceTree = "<group>"; };
		E794494AB90477BA94C681E994ADF341 /* Pods-Runner-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-Runner-dummy.m"; sourceTree = "<group>"; };
		E836B6881A29F6F83BA986B92E9CCAFA /* Base.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = Base.lproj; path = DKPhotoGallery/Resource/Resources/Base.lproj; sourceTree = "<group>"; };
		E90D0CC7D650A88957252D78CB5E955B /* SDMemoryCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDMemoryCache.m; path = SDWebImage/Core/SDMemoryCache.m; sourceTree = "<group>"; };
		E95F4135A781F1B0F90A544CD0398420 /* DKImagePickerController */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = DKImagePickerController; path = DKImagePickerController.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		E9AF85467FC35B2F758574B1B2D48AF0 /* ur.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = ur.lproj; path = Sources/DKImagePickerController/Resource/Resources/ur.lproj; sourceTree = "<group>"; };
		EB4CAE5C287D7E8203BE39E247CC4EAD /* SDGraphicsImageRenderer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDGraphicsImageRenderer.m; path = SDWebImage/Core/SDGraphicsImageRenderer.m; sourceTree = "<group>"; };
		EC43565E336CD1F29FF80F384BADF2B6 /* SDWebImage-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "SDWebImage-dummy.m"; sourceTree = "<group>"; };
		ECDF6AA713870989438BA93B395DC557 /* Pods-Runner-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-Runner-acknowledgements.markdown"; sourceTree = "<group>"; };
		EE071368E1CBA781F80DBDEF94196176 /* ResourceBundle-DKImagePickerController-DKImagePickerController-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-DKImagePickerController-DKImagePickerController-Info.plist"; sourceTree = "<group>"; };
		EE5492D53DE03B1A0240C9CF7EFF6D97 /* FileUtils.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FileUtils.m; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-6.2.1/ios/Classes/FileUtils.m"; sourceTree = "<group>"; };
		EE998EF1C3F51183ED90CCC588BD9B25 /* UIImage+SwiftyGif.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIImage+SwiftyGif.swift"; path = "SwiftyGif/UIImage+SwiftyGif.swift"; sourceTree = "<group>"; };
		F006DD0FEB32CB3A5079027A22EB082B /* DKPhotoGalleryScrollView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoGalleryScrollView.swift; path = DKPhotoGallery/DKPhotoGalleryScrollView.swift; sourceTree = "<group>"; };
		F0C6E5556D24D604C521EE2EA6ECAEBC /* NSButton+WebCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSButton+WebCache.h"; path = "SDWebImage/Core/NSButton+WebCache.h"; sourceTree = "<group>"; };
		F0FE15373CF1F72CCAC4E44E93611A6A /* SDWebImageDownloaderOperation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDWebImageDownloaderOperation.h; path = SDWebImage/Core/SDWebImageDownloaderOperation.h; sourceTree = "<group>"; };
		F1C8DD2D2942F3E0C7109681EA579981 /* SwiftyGif-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "SwiftyGif-Info.plist"; sourceTree = "<group>"; };
		F381E77C793F0E36AFF6B1F0242CB05D /* DKPhotoGalleryTransitionDismiss.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoGalleryTransitionDismiss.swift; path = DKPhotoGallery/Transition/DKPhotoGalleryTransitionDismiss.swift; sourceTree = "<group>"; };
		F434B085C4FCAB1CBE1653D90C47855F /* SDImageCodersManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageCodersManager.h; path = SDWebImage/Core/SDImageCodersManager.h; sourceTree = "<group>"; };
		F44CF38DD4A526208425E4E6A8E2BDCE /* path_provider_foundation.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = path_provider_foundation.debug.xcconfig; sourceTree = "<group>"; };
		F4692486E6503F599D73E72884DAD501 /* DKPhotoProgressIndicator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DKPhotoProgressIndicator.swift; path = DKPhotoGallery/Preview/DKPhotoProgressIndicator.swift; sourceTree = "<group>"; };
		F499E0C710700093032636C2129A6C42 /* UIImageView+HighlightedWebCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImageView+HighlightedWebCache.m"; path = "SDWebImage/Core/UIImageView+HighlightedWebCache.m"; sourceTree = "<group>"; };
		F5701F66729EB0A97110D5D68C90DFD3 /* SwiftyGif.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = SwiftyGif.modulemap; sourceTree = "<group>"; };
		F6394929BDB64C0D50C759F3462F2568 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		F763ABEC120B4A237083642AA4BC5263 /* SDDeviceHelper.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDDeviceHelper.m; path = SDWebImage/Private/SDDeviceHelper.m; sourceTree = "<group>"; };
		F93249511041AC418ECE46605CB8AF22 /* SDImageAPNGCoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SDImageAPNGCoder.h; path = SDWebImage/Core/SDImageAPNGCoder.h; sourceTree = "<group>"; };
		F97632DB1EBEBF407112E2947DB9517C /* SqfliteDatabase.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteDatabase.h; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h"; sourceTree = "<group>"; };
		F9EC37C9B4576FF43647BD8E930BF79B /* path_provider_foundation.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = path_provider_foundation.modulemap; sourceTree = "<group>"; };
		FA75EDE95C90A9DFA033778F15C03450 /* SqfliteImportPublic.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SqfliteImportPublic.h; path = "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h"; sourceTree = "<group>"; };
		FBB7D94DCAE93443547F6AADE9EF7E64 /* ko.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = ko.lproj; path = Sources/DKImagePickerController/Resource/Resources/ko.lproj; sourceTree = "<group>"; };
		FC4C0D09130EA693500EF4707BA41BA4 /* UIImage+ForceDecode.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+ForceDecode.h"; path = "SDWebImage/Core/UIImage+ForceDecode.h"; sourceTree = "<group>"; };
		FCB225BAED5A78B4AB86F0D0987D57DE /* SDAnimatedImageRep.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAnimatedImageRep.m; path = SDWebImage/Core/SDAnimatedImageRep.m; sourceTree = "<group>"; };
		FDE5B89F25BE0FB60E4F5EE06DCC7A8F /* UIImage+MultiFormat.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+MultiFormat.h"; path = "SDWebImage/Core/UIImage+MultiFormat.h"; sourceTree = "<group>"; };
		FF26EEDEA50A4C0EA7BDBC663422F83F /* SDAsyncBlockOperation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SDAsyncBlockOperation.m; path = SDWebImage/Private/SDAsyncBlockOperation.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0D163DED2A31ACD07E773CB58B2195F2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				5AA7ACB018166A9BD333DB51828EDC98 /* AVFoundation.framework in Frameworks */,
				5A6DEFF05C4601D30618C55DF681AC38 /* AVKit.framework in Frameworks */,
				58A18F12AE88C977E18DB18FBACE0E0B /* Foundation.framework in Frameworks */,
				30BD26961928AEA215A5297842CB6E4D /* Photos.framework in Frameworks */,
				A4625ADE856E8516CABAAC85CA725239 /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		10B0764DB53D501FD926D205EABAF841 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		15182316F4D24F27EE087A88ED7855D7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				FF4DD793F0D555D227E93561C0E52318 /* Foundation.framework in Frameworks */,
				1F390C289814EF57E68B8E02E00EFE6D /* ImageIO.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		174C2354D7E2A8F78F06F5343CD02522 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				BD200458BB8240FFC506B0F8EA215A47 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1CBCE7221733C426C1B2ED03B70869E4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		241A66A4D3B843675D8BAEC21318C9BC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				152260C7CC12E6405E97D8B1B8DA30D2 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2C51CA6EC745ACB63E424757C710B36C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				E8B800C8DB1A0A612E05CE6661CCA6A9 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4380924F566AA01EB048DC15F9BC6D33 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				CEBD84922D2CCEF26C272418EC3EB3A6 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		591D7385C5908AED4F61D6BAC2D5DE02 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		685A6DE1409E7B8E2287E732E094FAB1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				AC9407F923D48AC84D5BC6CD071FBF73 /* Foundation.framework in Frameworks */,
				5248914B09DBDDD3FBC067C28DD7916D /* Photos.framework in Frameworks */,
				A3F22E0C025372B8A45961CE1D05B974 /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6CEC46B8C85E5BC46FCB38C010453F60 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7DAA98D5689FEDDEE92F88371717E17D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				8BA7E31ED5287C455116A3A2F21B05F0 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9E3EBACF1161CC6DC85BF369D416FE5D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F8940B4FE519CE7C44A02B67C682550B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				A496DFBD4C34DBE5BF401ABD15E29628 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FF9A41C9E9F436562484E4D22987E212 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00125F8FB631C1C13BFF52A8C392DECB /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				2058C62E2A6C846A19355D44F8795D96 /* Sources */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		02907D04D1F8BE272055277482499F7D /* .. */ = {
			isa = PBXGroup;
			children = (
				0EA36A80C910005B5D26BEE496864ACD /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		03678DFBC8EC505F2ABE3F8CC46AB174 /* personal-project */ = {
			isa = PBXGroup;
			children = (
				EF15B6FCEAA30EC63BAB08BDAC363DF4 /* kwaci-rag-app */,
			);
			name = "personal-project";
			path = "personal-project";
			sourceTree = "<group>";
		};
		0417B9EE7EA87408A518E863DDCEFC40 /* .. */ = {
			isa = PBXGroup;
			children = (
				3897255C078F318779EABC0FF32DAC9A /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		072290CA55EB77D00547F03D7EBB7E52 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				6227352CD2BF87230CBCAD7464CA2D5C /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		09592CEAD060B2BB48149A033B0E105F /* Resources */ = {
			isa = PBXGroup;
			children = (
				F6394929BDB64C0D50C759F3462F2568 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		0EA36A80C910005B5D26BEE496864ACD /* .. */ = {
			isa = PBXGroup;
			children = (
				616A30ACD2524746506A010E1D3FE32C /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		111360965D2B0C4F9F08C43DBFCBC4CB /* Resources */ = {
			isa = PBXGroup;
			children = (
				AF2308829CFEABB97817F0E52ED98D6A /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		13AAAC0B957BC4E4218F8F8724C16E90 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				F9EC37C9B4576FF43647BD8E930BF79B /* path_provider_foundation.modulemap */,
				178985C7B533CAB9E665328A8C8E0BF7 /* path_provider_foundation-dummy.m */,
				755DC25759817D07088EC20F1737A233 /* path_provider_foundation-Info.plist */,
				AF5810585D4898042EF96B68C6037F72 /* path_provider_foundation-prefix.pch */,
				64C532391A7B5F56B8622A219800288E /* path_provider_foundation-umbrella.h */,
				F44CF38DD4A526208425E4E6A8E2BDCE /* path_provider_foundation.debug.xcconfig */,
				BF0F881597059D9535AB60F9F91D7677 /* path_provider_foundation.release.xcconfig */,
				310215F06C772DEC0A9580CD438BAA0F /* ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/path_provider_foundation";
			sourceTree = "<group>";
		};
		1494B80D38F34945F0913BE4342E9059 /* personal-project */ = {
			isa = PBXGroup;
			children = (
				3837C922074BD12704BAD0829B9508FF /* kwaci-rag-app */,
			);
			name = "personal-project";
			path = "personal-project";
			sourceTree = "<group>";
		};
		17BF453FEB733C3578BA5A7B7543F8B0 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				E28331D54906A1F44EEFEF035A48AE50 /* DKImagePickerController.modulemap */,
				99115CD6710556527DF72A648E0E6DEF /* DKImagePickerController-dummy.m */,
				21CE84D5EFA21CBC46826D3A54BEBADF /* DKImagePickerController-Info.plist */,
				E5EC70DD77A55601BA2F9BD3A62E67A0 /* DKImagePickerController-prefix.pch */,
				8B15D97E1E4B86D44B9D9D97ABFF88D1 /* DKImagePickerController-umbrella.h */,
				6B9319B2649D7165C1BE2597DF519277 /* DKImagePickerController.debug.xcconfig */,
				95956B095C04FF537194DCA28F66FF38 /* DKImagePickerController.release.xcconfig */,
				EE071368E1CBA781F80DBDEF94196176 /* ResourceBundle-DKImagePickerController-DKImagePickerController-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/DKImagePickerController";
			sourceTree = "<group>";
		};
		1A6E08FB80E792A38461EAC423952930 /* Pods */ = {
			isa = PBXGroup;
			children = (
				97917D6CC97E2D7096C665050FA86DE5 /* DKImagePickerController */,
				B15A3EC9970BCFBF256A97D65CC41AAF /* DKPhotoGallery */,
				94B7C8098A5EC0A8AA96968BB00930AB /* SDWebImage */,
				B9CFDFCE0F42C0A949C0AEB0A29B63D5 /* SwiftyGif */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		1B045C16E5A4E7F40C3F97139A0DC553 /* kwaci_chat_app */ = {
			isa = PBXGroup;
			children = (
				656E21591D9B9BEDB2D849FC515C157E /* ios */,
			);
			name = kwaci_chat_app;
			path = kwaci_chat_app;
			sourceTree = "<group>";
		};
		1B7B52DAB3C46CBA5E00C47F955816B2 /* .. */ = {
			isa = PBXGroup;
			children = (
				514760A329CD9FB7F98982282D3F4F0D /* .. */,
				96158219043DDB372F401476199C652D /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		1CCBDAE63D1F8C00D06AB7AE8F4BEB1C /* plugins */ = {
			isa = PBXGroup;
			children = (
				4205CCCB7C2D90DC214B77BA1E5EDE7C /* file_picker */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		1CD460531622CDF55C4E69F98AA51E9D /* Resources */ = {
			isa = PBXGroup;
			children = (
				8E41FDDCC3086A68D2AB2DE9DCBBFBA3 /* ar.lproj */,
				C6CD924D93C1E598BB9D0B593FC2E830 /* Base.lproj */,
				112E86A0F15DF6F1F777F8360FCEE0B8 /* da.lproj */,
				347429E150A815EB04227977FB606088 /* de.lproj */,
				0317A879E8DB725D7E3A04332B0F04FE /* en.lproj */,
				53E673146B08BD6614925B39051F5479 /* es.lproj */,
				D0D54FF993CCC9BC5769D73BBC879CAD /* fr.lproj */,
				2233EE9EA50E777B556AA8119A3A20D2 /* hu.lproj */,
				B9CD50144D5D98D52914342223E6C11A /* Images.xcassets */,
				AF9436FC3C179BE6332F89289A0D594D /* it.lproj */,
				688142D94D862449FC21560EC6EEA81B /* ja.lproj */,
				FBB7D94DCAE93443547F6AADE9EF7E64 /* ko.lproj */,
				AFE11AFCB255526008A836A10B56A9F6 /* nb-NO.lproj */,
				E5B8BAD4DCE5B5C285F6A7E080EFC0A9 /* nl.lproj */,
				163B3B630806B6DC3C48FF69996A353D /* PrivacyInfo.xcprivacy */,
				3198DAF8EDB6AFE80875AA1ED942031D /* pt_BR.lproj */,
				6B610070B8E29B8492490CCBDB5629F6 /* ru.lproj */,
				1C395147920BA0F2ED484BDC6FDA3D00 /* tr.lproj */,
				E9AF85467FC35B2F758574B1B2D48AF0 /* ur.lproj */,
				178C00F3E8FADF6109FA4C3E3D2D1C13 /* vi.lproj */,
				A7BD1B4BB7B7B7F323AED9971F4C8897 /* zh-Hans.lproj */,
				1E6A2DFF164FD0FE61DED87A57FC18EF /* zh-Hant.lproj */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		2058C62E2A6C846A19355D44F8795D96 /* Sources */ = {
			isa = PBXGroup;
			children = (
				C81F0F882FD79E5964609E8A7EDA04D4 /* path_provider_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		263A00616C2B68DF4479A4FD7AFE8859 /* darwin */ = {
			isa = PBXGroup;
			children = (
				00125F8FB631C1C13BFF52A8C392DECB /* path_provider_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		26B34128CA78D1A466B83D97BBC9EE1D /* darwin */ = {
			isa = PBXGroup;
			children = (
				B8258D6FA9D98F4FDDB040335ABB3035 /* sqflite_darwin */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		28288DA17E00E7BDE92D5B1AC5356339 /* Sources */ = {
			isa = PBXGroup;
			children = (
				4671FDCFFD2B5AE2A3C1D139F97423E9 /* sqflite_darwin */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		286551134A78C5CC076679B0B16FA8EE /* Core */ = {
			isa = PBXGroup;
			children = (
				5B29685B553F3F02916B86BAD24D18AD /* DKPhotoGallery.swift */,
				B5B9FF3631E057751D1DB8A4858DDBEB /* DKPhotoGalleryContentVC.swift */,
				25325060BDAEFF98510C260AB283BDD9 /* DKPhotoGalleryInteractiveTransition.swift */,
				F006DD0FEB32CB3A5079027A22EB082B /* DKPhotoGalleryScrollView.swift */,
				2B833000DF956D6F16410FCD83ECC83E /* DKPhotoGalleryTransitionController.swift */,
				F381E77C793F0E36AFF6B1F0242CB05D /* DKPhotoGalleryTransitionDismiss.swift */,
				DA911E92959967B9215184E7E33D1537 /* DKPhotoGalleryTransitionPresent.swift */,
				2D5B453D1CB610A9D15EC826630ACD14 /* DKPhotoIncrementalIndicator.swift */,
				32DEA70F553151FFCCB8BD72F44902E9 /* DKPhotoPreviewFactory.swift */,
			);
			name = Core;
			sourceTree = "<group>";
		};
		2DB8426A477BA92773F00012EBA84D53 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				E806A4F97D48F5FD3B4CA1BC617EA1F5 /* Pods-Runner */,
				537E840C4F4E30B706FD9D64B15C53A7 /* Pods-RunnerTests */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		3542CE6648237DEC795B5C01CF83C83F /* kwaci-rag-app */ = {
			isa = PBXGroup;
			children = (
				1B045C16E5A4E7F40C3F97139A0DC553 /* kwaci_chat_app */,
			);
			name = "kwaci-rag-app";
			path = "kwaci-rag-app";
			sourceTree = "<group>";
		};
		3837C922074BD12704BAD0829B9508FF /* kwaci-rag-app */ = {
			isa = PBXGroup;
			children = (
				5B5648CBB6EB99A01D0F69D6D4C8FF10 /* kwaci_chat_app */,
			);
			name = "kwaci-rag-app";
			path = "kwaci-rag-app";
			sourceTree = "<group>";
		};
		3897255C078F318779EABC0FF32DAC9A /* .. */ = {
			isa = PBXGroup;
			children = (
				96AA3F257B353CFC394F759FD4EB15FF /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		39CFC3B522E1AF598895AB6717ED1E64 /* Documents */ = {
			isa = PBXGroup;
			children = (
				1494B80D38F34945F0913BE4342E9059 /* personal-project */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		3DC900A8123CD93874C2BF96744C86D1 /* Sources */ = {
			isa = PBXGroup;
			children = (
				E032A7A5A8C444180ABE802F03D91461 /* sqflite_darwin */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		415E8A53B736DA0D3F026235B48B5A49 /* .. */ = {
			isa = PBXGroup;
			children = (
				9CB4E84601DDA5FC90D571FDCC238ED0 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-6.2.1/ios";
			sourceTree = "<group>";
		};
		417735A5B289A020AD8BF2C9FE39674B /* Core */ = {
			isa = PBXGroup;
			children = (
				697F580784149950D87E5036BE0E9973 /* DKAssetGroupCellItemProtocol.swift */,
				C9F846C845D7C08BCCE9325A674C7F00 /* DKAssetGroupDetailBaseCell.swift */,
				08297DFC1BEE93D385211CD515ACE820 /* DKAssetGroupDetailCameraCell.swift */,
				9121B2897840B068C0E6BD952C182343 /* DKAssetGroupDetailImageCell.swift */,
				AAD98F33D07A4C4BB68B8FCBDD6BC9BE /* DKAssetGroupDetailVC.swift */,
				969C524BB7AC0F3CEE13E00722FDF441 /* DKAssetGroupDetailVideoCell.swift */,
				4E3F6404E6C26C9B0ACF2FD19B965CED /* DKAssetGroupGridLayout.swift */,
				823EAB70C33F9BBC2D30E14237611EE6 /* DKAssetGroupListVC.swift */,
				829E16B336914A73C246CD3F1563611E /* DKImageAssetExporter.swift */,
				9002FF03C62F1DB866747FD7D2380865 /* DKImageExtensionController.swift */,
				3EDCF61AA4F4BD3B51F408EAF838517D /* DKImagePickerController.swift */,
				20FFBD460D3750371C3430443337070F /* DKImagePickerControllerBaseUIDelegate.swift */,
				3FB7369B40BEC79986FBCFEFF425ACB7 /* DKPermissionView.swift */,
				879E08ECB5A6491E56AA823984677E59 /* DKPopoverViewController.swift */,
			);
			name = Core;
			sourceTree = "<group>";
		};
		4205CCCB7C2D90DC214B77BA1E5EDE7C /* file_picker */ = {
			isa = PBXGroup;
			children = (
				E41D352625789D32714EBB5DDFC9F317 /* ios */,
			);
			name = file_picker;
			path = file_picker;
			sourceTree = "<group>";
		};
		44910DDE73CE45BF6AFADAC413B2B57C /* ios */ = {
			isa = PBXGroup;
			children = (
				52218616C0CE007722678AEADCF5425F /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		44FC3E95A2F839E8D8B5D1D74CFF3153 /* PhotoGallery */ = {
			isa = PBXGroup;
			children = (
				1CB40042050ADD6A3661CB69BA75295D /* DKImageExtensionGallery.swift */,
			);
			name = PhotoGallery;
			sourceTree = "<group>";
		};
		45F3C41BC9FE8DEB7EB0F3291A51CE1E /* sqflite_darwin */ = {
			isa = PBXGroup;
			children = (
				F827E4AC4BC8F004CD4A47D7E048530B /* .. */,
				95549A593F6F8B9A84D1A740F9BB8FFC /* Pod */,
				5D47FD2DFA9B7263F0BFDD7D5007340D /* Support Files */,
			);
			name = sqflite_darwin;
			path = ../.symlinks/plugins/sqflite_darwin/darwin;
			sourceTree = "<group>";
		};
		4671FDCFFD2B5AE2A3C1D139F97423E9 /* sqflite_darwin */ = {
			isa = PBXGroup;
			children = (
				09592CEAD060B2BB48149A033B0E105F /* Resources */,
			);
			name = sqflite_darwin;
			path = sqflite_darwin;
			sourceTree = "<group>";
		};
		4B40D60934949F7C9BB5F24D53719637 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				CA0C3C2D119121608DACDAF59CD19DB2 /* Sources */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		4C2DD3E0BD7946251882FDBECF38482F /* .. */ = {
			isa = PBXGroup;
			children = (
				5C03A619D6F09E96D0E5089947A8F5ED /* .. */,
				7D34A3CADBB6A57BFD954A7B0A898F8D /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		4D7A4FF4A253E87683E549C2F72E0CB3 /* Development Pods */ = {
			isa = PBXGroup;
			children = (
				CAD5159642E76E8E0C4795C9E95A908A /* file_picker */,
				768138C9C7F4ED13BC0A124D5D412D1A /* Flutter */,
				E9CD685A9C35501505F4106E7CF15A90 /* path_provider_foundation */,
				45F3C41BC9FE8DEB7EB0F3291A51CE1E /* sqflite_darwin */,
			);
			name = "Development Pods";
			sourceTree = "<group>";
		};
		4E27FC7D9A20DCB7B96108B46FE045DD /* kwaci_chat_app */ = {
			isa = PBXGroup;
			children = (
				44910DDE73CE45BF6AFADAC413B2B57C /* ios */,
			);
			name = kwaci_chat_app;
			path = kwaci_chat_app;
			sourceTree = "<group>";
		};
		4F67F886C769FC288A6F8471C24BBD7D /* Resources */ = {
			isa = PBXGroup;
			children = (
				E836B6881A29F6F83BA986B92E9CCAFA /* Base.lproj */,
				CE1CDB9D2119321BD845E825C048B9FA /* en.lproj */,
				0E3476D86DFA146A1594E648B05D1BE8 /* Images.xcassets */,
				5EC40588954DF1EBCB3F100414A48355 /* PrivacyInfo.xcprivacy */,
				0B96DC1C8B60D0F40EA56B17D5B7FC8E /* zh-Hans.lproj */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		50C259F1F7548BBB88C2D0C87EFF8678 /* plugins */ = {
			isa = PBXGroup;
			children = (
				C17729B1AE16F7147F11B3B660D5D73A /* sqflite_darwin */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		514760A329CD9FB7F98982282D3F4F0D /* .. */ = {
			isa = PBXGroup;
			children = (
				E6BEDF33F5291497319EE520975458CE /* Documents */,
			);
			name = ..;
			path = .;
			sourceTree = "<group>";
		};
		52218616C0CE007722678AEADCF5425F /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				A8FCB2A4CCECFA2CDCC4D8906D730EB1 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		533D72958DBD6977D93B3EA07783CF6B /* kwaci_chat_app */ = {
			isa = PBXGroup;
			children = (
				DAB43E89884030ECB6BB40AB5D3B85FB /* ios */,
			);
			name = kwaci_chat_app;
			path = kwaci_chat_app;
			sourceTree = "<group>";
		};
		537E840C4F4E30B706FD9D64B15C53A7 /* Pods-RunnerTests */ = {
			isa = PBXGroup;
			children = (
				B0BD57AB3672E53828D11C2A3368023A /* Pods-RunnerTests.modulemap */,
				11AA7018F95317D959D9D2E76CBB2377 /* Pods-RunnerTests-acknowledgements.markdown */,
				79F83F4D505FA40C1CD18A8029181D59 /* Pods-RunnerTests-acknowledgements.plist */,
				4D3DB54C128E3D88C0C9C7DDEA475ED7 /* Pods-RunnerTests-dummy.m */,
				B00A5ECAED4CA0C16FF7C65365BC0334 /* Pods-RunnerTests-Info.plist */,
				9E9B12BCE5540D02A80250C3EC16D399 /* Pods-RunnerTests-umbrella.h */,
				03E8AD2E8B75443E12D3D3BD12A9900E /* Pods-RunnerTests.debug.xcconfig */,
				E736E0C9C5B73D27B8865E363E2108AC /* Pods-RunnerTests.profile.xcconfig */,
				AC8FE152530FBDCE460186B326C1A8FB /* Pods-RunnerTests.release.xcconfig */,
			);
			name = "Pods-RunnerTests";
			path = "Target Support Files/Pods-RunnerTests";
			sourceTree = "<group>";
		};
		58EB343BD68CE81E1AD61F300B37F3C2 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				50C259F1F7548BBB88C2D0C87EFF8678 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		5B5648CBB6EB99A01D0F69D6D4C8FF10 /* kwaci_chat_app */ = {
			isa = PBXGroup;
			children = (
				9C43DEC96C9306FF504B3650626D5915 /* ios */,
			);
			name = kwaci_chat_app;
			path = kwaci_chat_app;
			sourceTree = "<group>";
		};
		5C03A619D6F09E96D0E5089947A8F5ED /* .. */ = {
			isa = PBXGroup;
			children = (
				740DFEB30227193F2FD63C3EA4B2AB0F /* Documents */,
			);
			name = ..;
			path = .;
			sourceTree = "<group>";
		};
		5D47FD2DFA9B7263F0BFDD7D5007340D /* Support Files */ = {
			isa = PBXGroup;
			children = (
				6DEDBFCA39F0B8CFFF15C2EBF6E94E0C /* ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist */,
				28D3E4584ADD56452A2C7A14D8EF3EE7 /* sqflite_darwin.modulemap */,
				86371477A8C16F168BFD3A274F96B823 /* sqflite_darwin-dummy.m */,
				495DB7B626C8CFBE27440C7A30932A26 /* sqflite_darwin-Info.plist */,
				194BCBF3F052AD9EC63D993A4533F22E /* sqflite_darwin-prefix.pch */,
				A2ACAE17191CF6514EBA0AFB689F6908 /* sqflite_darwin-umbrella.h */,
				05351FE7C3B96314E9BCD9B4BF6EE6D5 /* sqflite_darwin.debug.xcconfig */,
				30F15D573E77ADBAB56D2DE6F61C0717 /* sqflite_darwin.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/sqflite_darwin";
			sourceTree = "<group>";
		};
		616A30ACD2524746506A010E1D3FE32C /* .. */ = {
			isa = PBXGroup;
			children = (
				6DC0EA95A318CB612619B544370CA525 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		6227352CD2BF87230CBCAD7464CA2D5C /* plugins */ = {
			isa = PBXGroup;
			children = (
				A1C90E5035F9FAF70E8135F0318CBB33 /* sqflite_darwin */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		656E21591D9B9BEDB2D849FC515C157E /* ios */ = {
			isa = PBXGroup;
			children = (
				72EF1D338614255B4FD653A2BEC0CB89 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		67C831826B7074644B421D670A3D75BE /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				20DF93D3A9423B1127F6B2A4F18C2D67 /* messages.g.swift */,
				04DF7B01599F65CB3B475370DA327281 /* PathProviderPlugin.swift */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		6DC0EA95A318CB612619B544370CA525 /* .. */ = {
			isa = PBXGroup;
			children = (
				1B7B52DAB3C46CBA5E00C47F955816B2 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		72EF1D338614255B4FD653A2BEC0CB89 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				C33B76977C60A6EF04E2546DF3EEB3BC /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		740DFEB30227193F2FD63C3EA4B2AB0F /* Documents */ = {
			isa = PBXGroup;
			children = (
				75EED2CA961680199BC951D05C704A74 /* personal-project */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		75D01C821AC4E0D59EE46593AA192BFE /* sqflite_darwin */ = {
			isa = PBXGroup;
			children = (
				FA75EDE95C90A9DFA033778F15C03450 /* SqfliteImportPublic.h */,
				C38450126D90A75E45DC7E90680DB0EF /* SqflitePluginPublic.h */,
			);
			name = sqflite_darwin;
			path = sqflite_darwin;
			sourceTree = "<group>";
		};
		75EED2CA961680199BC951D05C704A74 /* personal-project */ = {
			isa = PBXGroup;
			children = (
				C86A61F4645F6F6DF5A13EBA0620C47D /* kwaci-rag-app */,
			);
			name = "personal-project";
			path = "personal-project";
			sourceTree = "<group>";
		};
		768138C9C7F4ED13BC0A124D5D412D1A /* Flutter */ = {
			isa = PBXGroup;
			children = (
				CF9CC1E7C6FB14A68EB513DEE8FC76F6 /* Pod */,
				F6B966D7C48307F240352AC8F7AA7219 /* Support Files */,
			);
			name = Flutter;
			path = ../Flutter;
			sourceTree = "<group>";
		};
		7A071A72A12C6458304A92128D45B9EC /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				263A00616C2B68DF4479A4FD7AFE8859 /* darwin */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		7D34A3CADBB6A57BFD954A7B0A898F8D /* Documents */ = {
			isa = PBXGroup;
			children = (
				D19BBFDAF736A643BF07E7775E29CB79 /* personal-project */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		89A4BC091B72A4B85E37C24B055E1A14 /* .. */ = {
			isa = PBXGroup;
			children = (
				0417B9EE7EA87408A518E863DDCEFC40 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		8BF51341E5F0A4D3A34C166DA1ACD715 /* Resource */ = {
			isa = PBXGroup;
			children = (
				0810676177E4E5AE719C8068D59C85EA /* DKImagePickerControllerResource.swift */,
				1CD460531622CDF55C4E69F98AA51E9D /* Resources */,
			);
			name = Resource;
			sourceTree = "<group>";
		};
		8CE2C8006883FFF4AB28632058A31557 /* kwaci-rag-app */ = {
			isa = PBXGroup;
			children = (
				533D72958DBD6977D93B3EA07783CF6B /* kwaci_chat_app */,
			);
			name = "kwaci-rag-app";
			path = "kwaci-rag-app";
			sourceTree = "<group>";
		};
		8FF1F91DBDE1596139948E879F1FFD2D /* include */ = {
			isa = PBXGroup;
			children = (
				75D01C821AC4E0D59EE46593AA192BFE /* sqflite_darwin */,
			);
			name = include;
			path = include;
			sourceTree = "<group>";
		};
		94B7C8098A5EC0A8AA96968BB00930AB /* SDWebImage */ = {
			isa = PBXGroup;
			children = (
				D97B7C3B33CDB23DACA27EB1A41FC826 /* Core */,
				B84A96F00D284D50C8EAE77CE45F2517 /* Support Files */,
			);
			name = SDWebImage;
			path = SDWebImage;
			sourceTree = "<group>";
		};
		95549A593F6F8B9A84D1A740F9BB8FFC /* Pod */ = {
			isa = PBXGroup;
			children = (
				309458B7B35BF1D679F7CEFB07290156 /* LICENSE */,
				2E1781EFA190759359CD823BFBF0D71E /* README.md */,
				4DB32310990B66E8F5A8B0D6EE1ACBA1 /* sqflite_darwin.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		96158219043DDB372F401476199C652D /* Documents */ = {
			isa = PBXGroup;
			children = (
				03678DFBC8EC505F2ABE3F8CC46AB174 /* personal-project */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		96AA3F257B353CFC394F759FD4EB15FF /* .. */ = {
			isa = PBXGroup;
			children = (
				FB68E1B5A1092FD5D705F1F84B0D5F27 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		97917D6CC97E2D7096C665050FA86DE5 /* DKImagePickerController */ = {
			isa = PBXGroup;
			children = (
				417735A5B289A020AD8BF2C9FE39674B /* Core */,
				EDF1005B1B7ED4F0C2936223281F8427 /* ImageDataManager */,
				44FC3E95A2F839E8D8B5D1D74CFF3153 /* PhotoGallery */,
				8BF51341E5F0A4D3A34C166DA1ACD715 /* Resource */,
				17BF453FEB733C3578BA5A7B7543F8B0 /* Support Files */,
			);
			name = DKImagePickerController;
			path = DKImagePickerController;
			sourceTree = "<group>";
		};
		98901C3EA2113611119DD8A5435B2DE1 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				4843A411333A6B464F720237F4261CA7 /* DKPhotoGallery.modulemap */,
				3AC30EA77C83D655A38667688DA692F4 /* DKPhotoGallery-dummy.m */,
				E0844F0148D94DF619A8A29EFF1275B8 /* DKPhotoGallery-Info.plist */,
				99347923C02D8250ACDFB15050A661AC /* DKPhotoGallery-prefix.pch */,
				D6D6C31AF0B01A79E49649748D95CC8D /* DKPhotoGallery-umbrella.h */,
				4B0F8B496CBA607D4401BBF1ED8F34DB /* DKPhotoGallery.debug.xcconfig */,
				AC66E20519F07E1D618916DC51EBBF48 /* DKPhotoGallery.release.xcconfig */,
				B79ABF45C4496A4586DA561EA91B5866 /* ResourceBundle-DKPhotoGallery-DKPhotoGallery-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/DKPhotoGallery";
			sourceTree = "<group>";
		};
		9C43DEC96C9306FF504B3650626D5915 /* ios */ = {
			isa = PBXGroup;
			children = (
				FF40A9865B2CCA6C50D137A1589D80E6 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		9CB4E84601DDA5FC90D571FDCC238ED0 /* .. */ = {
			isa = PBXGroup;
			children = (
				A0993720CEDA4E5C98E2F3F0A79BB6B8 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		9FBAF9DE1CB432C574D8D8B47DAB8972 /* .. */ = {
			isa = PBXGroup;
			children = (
				E683D3C03046432B0C25822465ABA864 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		A0993720CEDA4E5C98E2F3F0A79BB6B8 /* .. */ = {
			isa = PBXGroup;
			children = (
				9FBAF9DE1CB432C574D8D8B47DAB8972 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		A19A8C488F467434CF224EA87EC67D22 /* Resources */ = {
			isa = PBXGroup;
			children = (
				3E08445AE0549D9136DB51FA2E12B062 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		A1C90E5035F9FAF70E8135F0318CBB33 /* sqflite_darwin */ = {
			isa = PBXGroup;
			children = (
				CB5147B6EA8CE17DCF54B053A3596A30 /* darwin */,
			);
			name = sqflite_darwin;
			path = sqflite_darwin;
			sourceTree = "<group>";
		};
		A81439EE9636CA0908833A58AB894C8E /* .. */ = {
			isa = PBXGroup;
			children = (
				39CFC3B522E1AF598895AB6717ED1E64 /* Documents */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		A8FCB2A4CCECFA2CDCC4D8906D730EB1 /* plugins */ = {
			isa = PBXGroup;
			children = (
				7A071A72A12C6458304A92128D45B9EC /* path_provider_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		ACF11E49E12195551017683244BC5E81 /* .. */ = {
			isa = PBXGroup;
			children = (
				4C2DD3E0BD7946251882FDBECF38482F /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		AF8BD8B3C44E1D73CCC32D3B05247D94 /* Preview */ = {
			isa = PBXGroup;
			children = (
				DDBBD3E733F80E38B2CFA0EC7EA06927 /* DKPDFView.swift */,
				4591242795E8FDCC6C65962A5EB118F0 /* DKPhotoBaseImagePreviewVC.swift */,
				1DD88569F894189EF84A98201C8D51F1 /* DKPhotoBasePreviewVC.swift */,
				91EDD8B56B94C4C584C6D7E49DCFCCAC /* DKPhotoContentAnimationView.swift */,
				B226930FC757308F96D78BC70158FCF2 /* DKPhotoImageDownloader.swift */,
				DE8DBD29B147EA62BF91ADC387ECE763 /* DKPhotoImagePreviewVC.swift */,
				CE48C851D036CBD2600AD30FE109D805 /* DKPhotoImageUtility.swift */,
				E02C1C4A89BB4CC0BC278D8C4D0D8BD5 /* DKPhotoImageView.swift */,
				CEF5DE5C78B0CA1EAE3C50C5BD2CF484 /* DKPhotoPDFPreviewVC.swift */,
				54FE0A6E8684179D959A0D19CD625441 /* DKPhotoPlayerPreviewVC.swift */,
				F4692486E6503F599D73E72884DAD501 /* DKPhotoProgressIndicator.swift */,
				3510BF004F0CD883594D808691CA9377 /* DKPhotoProgressIndicatorProtocol.swift */,
				B71ED232D67949A5C2AE8CD69403697E /* DKPhotoQRCodeResultVC.swift */,
				166AC360FC0B94E28269D0F840E6E0C8 /* DKPhotoWebVC.swift */,
				5804E591074A372E46F63D0DF1B8F89E /* DKPlayerView.swift */,
			);
			name = Preview;
			sourceTree = "<group>";
		};
		B15A3EC9970BCFBF256A97D65CC41AAF /* DKPhotoGallery */ = {
			isa = PBXGroup;
			children = (
				286551134A78C5CC076679B0B16FA8EE /* Core */,
				C943ED9A775DB76836AB271043A9BFF5 /* Model */,
				AF8BD8B3C44E1D73CCC32D3B05247D94 /* Preview */,
				BA97A99B3D83E80A6FFDFB999D764BEF /* Resource */,
				98901C3EA2113611119DD8A5435B2DE1 /* Support Files */,
			);
			name = DKPhotoGallery;
			path = DKPhotoGallery;
			sourceTree = "<group>";
		};
		B8258D6FA9D98F4FDDB040335ABB3035 /* sqflite_darwin */ = {
			isa = PBXGroup;
			children = (
				3DC900A8123CD93874C2BF96744C86D1 /* Sources */,
			);
			name = sqflite_darwin;
			path = sqflite_darwin;
			sourceTree = "<group>";
		};
		B84A96F00D284D50C8EAE77CE45F2517 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				14EF5E412236260E6FD9CF450C1BF554 /* ResourceBundle-SDWebImage-SDWebImage-Info.plist */,
				83191A3DF10B46A748B80206E6DCA58B /* SDWebImage.modulemap */,
				EC43565E336CD1F29FF80F384BADF2B6 /* SDWebImage-dummy.m */,
				2D64536825B04F311AEA4501B2961110 /* SDWebImage-Info.plist */,
				05273041D4D9796C519A1489A5E11E11 /* SDWebImage-prefix.pch */,
				28EB180F2D1FEEC56C83D5F1E529AF16 /* SDWebImage-umbrella.h */,
				B281E855782C63501215FBC637F8DC9B /* SDWebImage.debug.xcconfig */,
				3FF59488A08EFCA4467233D767353363 /* SDWebImage.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/SDWebImage";
			sourceTree = "<group>";
		};
		B87BBCDBAFF41E35A4C7EC0D524C78C4 /* personal-project */ = {
			isa = PBXGroup;
			children = (
				8CE2C8006883FFF4AB28632058A31557 /* kwaci-rag-app */,
			);
			name = "personal-project";
			path = "personal-project";
			sourceTree = "<group>";
		};
		B9CFDFCE0F42C0A949C0AEB0A29B63D5 /* SwiftyGif */ = {
			isa = PBXGroup;
			children = (
				9A644C7ABD2777AF2D4541D200284C1E /* NSImage+SwiftyGif.swift */,
				767E2ED40586F6E12141BC5B86B46448 /* NSImageView+SwiftyGif.swift */,
				98EC63143D86891CEF60EAF54154907C /* ObjcAssociatedWeakObject.swift */,
				9299189F45F01C0AB996C5963AA97ACD /* SwiftyGif.h */,
				487C5DA3B07A19BBB6E3909A2EF83CDF /* SwiftyGifManager.swift */,
				EE998EF1C3F51183ED90CCC588BD9B25 /* UIImage+SwiftyGif.swift */,
				AF3615DFF25CB5670F2C955060E9B3BA /* UIImageView+SwiftyGif.swift */,
				D8EDE95833EBCA0688599C3EA3DBB270 /* Resources */,
				BCEF122AB47CF776B6DEC6A4FDEB60B8 /* Support Files */,
			);
			name = SwiftyGif;
			path = SwiftyGif;
			sourceTree = "<group>";
		};
		BA97A99B3D83E80A6FFDFB999D764BEF /* Resource */ = {
			isa = PBXGroup;
			children = (
				D3EA1BABD76B44E160C4203748AB3759 /* DKPhotoGalleryResource.swift */,
				4F67F886C769FC288A6F8471C24BBD7D /* Resources */,
			);
			name = Resource;
			sourceTree = "<group>";
		};
		BAA35C7CE233DF3F72EEAF27FB756E37 /* Pod */ = {
			isa = PBXGroup;
			children = (
				3FFC936C299275AAE56A15B974064377 /* LICENSE */,
				3F77B111088E66AE9F679184A33A1BDC /* path_provider_foundation.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		BCEF122AB47CF776B6DEC6A4FDEB60B8 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				A08D8B2447463F52ABBD47D88E4BA3E9 /* ResourceBundle-SwiftyGif-SwiftyGif-Info.plist */,
				F5701F66729EB0A97110D5D68C90DFD3 /* SwiftyGif.modulemap */,
				113D9CE3642C265D44707E73D8FE4CE0 /* SwiftyGif-dummy.m */,
				F1C8DD2D2942F3E0C7109681EA579981 /* SwiftyGif-Info.plist */,
				4BDABE8C597FD860C3ED93A0A9ADBA7C /* SwiftyGif-prefix.pch */,
				5BB8792AEB552A6DA7CBAB23E8F8D07C /* SwiftyGif-umbrella.h */,
				E754497AF84F2E36D088C1EB20390ABB /* SwiftyGif.debug.xcconfig */,
				81C7F9DBD43A2C5DE5FFC88258C80F6D /* SwiftyGif.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/SwiftyGif";
			sourceTree = "<group>";
		};
		C17729B1AE16F7147F11B3B660D5D73A /* sqflite_darwin */ = {
			isa = PBXGroup;
			children = (
				26B34128CA78D1A466B83D97BBC9EE1D /* darwin */,
			);
			name = sqflite_darwin;
			path = sqflite_darwin;
			sourceTree = "<group>";
		};
		C1A8230737624DF3CCCA41984CE2C497 /* kwaci_chat_app */ = {
			isa = PBXGroup;
			children = (
				EEA4838B475BA9C7469FD1EB9C34A944 /* ios */,
			);
			name = kwaci_chat_app;
			path = kwaci_chat_app;
			sourceTree = "<group>";
		};
		C33B76977C60A6EF04E2546DF3EEB3BC /* plugins */ = {
			isa = PBXGroup;
			children = (
				CE06EF887A11629F45DDCD5318B73C30 /* path_provider_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		C81F0F882FD79E5964609E8A7EDA04D4 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				A19A8C488F467434CF224EA87EC67D22 /* Resources */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		C86A61F4645F6F6DF5A13EBA0620C47D /* kwaci-rag-app */ = {
			isa = PBXGroup;
			children = (
				4E27FC7D9A20DCB7B96108B46FE045DD /* kwaci_chat_app */,
			);
			name = "kwaci-rag-app";
			path = "kwaci-rag-app";
			sourceTree = "<group>";
		};
		C943ED9A775DB76836AB271043A9BFF5 /* Model */ = {
			isa = PBXGroup;
			children = (
				1942AE028FE897380602862B4382B7A3 /* DKPhotoGalleryItem.swift */,
			);
			name = Model;
			sourceTree = "<group>";
		};
		CA0C3C2D119121608DACDAF59CD19DB2 /* Sources */ = {
			isa = PBXGroup;
			children = (
				67C831826B7074644B421D670A3D75BE /* path_provider_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		CAD5159642E76E8E0C4795C9E95A908A /* file_picker */ = {
			isa = PBXGroup;
			children = (
				415E8A53B736DA0D3F026235B48B5A49 /* .. */,
				E12DF68C288C127C5FD16480B9C5DA81 /* Pod */,
				EF1111E106A941895FB62E9D7050900C /* Support Files */,
			);
			name = file_picker;
			path = ../.symlinks/plugins/file_picker/ios;
			sourceTree = "<group>";
		};
		CB5147B6EA8CE17DCF54B053A3596A30 /* darwin */ = {
			isa = PBXGroup;
			children = (
				CD2788834959240DE83AA040DCA37921 /* sqflite_darwin */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		CD2788834959240DE83AA040DCA37921 /* sqflite_darwin */ = {
			isa = PBXGroup;
			children = (
				28288DA17E00E7BDE92D5B1AC5356339 /* Sources */,
			);
			name = sqflite_darwin;
			path = sqflite_darwin;
			sourceTree = "<group>";
		};
		CE06EF887A11629F45DDCD5318B73C30 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				DEAE503957E2A86B28F9DE93A98CF850 /* darwin */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				4D7A4FF4A253E87683E549C2F72E0CB3 /* Development Pods */,
				D68CA58901FBF589D75F5E40F1EAF5BA /* Frameworks */,
				1A6E08FB80E792A38461EAC423952930 /* Pods */,
				D376FA4E96F8A331CAD4704E55D34F61 /* Products */,
				2DB8426A477BA92773F00012EBA84D53 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		CF9CC1E7C6FB14A68EB513DEE8FC76F6 /* Pod */ = {
			isa = PBXGroup;
			children = (
				B1DDFF0CB273AE6904403109B3178A1A /* Flutter.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		D19BBFDAF736A643BF07E7775E29CB79 /* personal-project */ = {
			isa = PBXGroup;
			children = (
				3542CE6648237DEC795B5C01CF83C83F /* kwaci-rag-app */,
			);
			name = "personal-project";
			path = "personal-project";
			sourceTree = "<group>";
		};
		D22E6242D11F3B035CF72F4F0A3BECC3 /* .. */ = {
			isa = PBXGroup;
			children = (
				FDF80E738F2C8AD00A37503743ECD554 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		D376FA4E96F8A331CAD4704E55D34F61 /* Products */ = {
			isa = PBXGroup;
			children = (
				E95F4135A781F1B0F90A544CD0398420 /* DKImagePickerController */,
				7247E064ECCE473BC39CE9C6B1E7C0B3 /* DKImagePickerController-DKImagePickerController */,
				B1B247DE42D782218EFEA0CB0F6D6454 /* DKPhotoGallery */,
				B85D4E7E4EAEF1DAD4C541A2D2E6F27D /* DKPhotoGallery-DKPhotoGallery */,
				308378B6ADC631E53C97B459FBCD7A32 /* file_picker */,
				AE157A33FEF959A214796BFF348717F6 /* path_provider_foundation */,
				3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */,
				669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */,
				6C3345B1B3CAEDF5B03B1F731FDC492E /* Pods-RunnerTests */,
				B0B214D775196BA7CA8E17E53048A493 /* SDWebImage */,
				CF1281E58AA1045D4B7F33FC56691C42 /* SDWebImage-SDWebImage */,
				4FE20B1CF8B318A8D9591ED1587D7592 /* sqflite_darwin */,
				071549DBDD4BFC0BD7D2715539015DC2 /* sqflite_darwin-sqflite_darwin_privacy */,
				7AD67EFB9A7864151301DC2890207AA2 /* SwiftyGif */,
				0A37DE9A55F6CEA1AF18BDEF878BF1F5 /* SwiftyGif-SwiftyGif */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		D68CA58901FBF589D75F5E40F1EAF5BA /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				D824B5647F0647A3B20B283142253DC1 /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		D824B5647F0647A3B20B283142253DC1 /* iOS */ = {
			isa = PBXGroup;
			children = (
				198A8538874FC857E01267BE1F017B5A /* AVFoundation.framework */,
				697F1BC979DA5F63FCDA032EB7A85FD0 /* AVKit.framework */,
				544CE4FAB9748BB244C77D7C8BDDEBFC /* Foundation.framework */,
				7B5E6D4EED9D7031B7EF9CBADD83B364 /* ImageIO.framework */,
				2B18DB190CB3877B1931D632678E4A50 /* Photos.framework */,
				0FC2783B4AADE525C40AB282A3AB03BC /* UIKit.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		D8EDE95833EBCA0688599C3EA3DBB270 /* Resources */ = {
			isa = PBXGroup;
			children = (
				736EAF151B6A2EF5173EE77CC12F9DD2 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		D97B7C3B33CDB23DACA27EB1A41FC826 /* Core */ = {
			isa = PBXGroup;
			children = (
				2F53528A4922001AE68DCF4961DF1F1A /* NSBezierPath+SDRoundedCorners.h */,
				286C81353C130B4CEF304AB33D2FE608 /* NSBezierPath+SDRoundedCorners.m */,
				F0C6E5556D24D604C521EE2EA6ECAEBC /* NSButton+WebCache.h */,
				025480373CD1CBC8A8C690C859E2EFA6 /* NSButton+WebCache.m */,
				8CDB4904C17A77BF2CDFC740DD72E34C /* NSData+ImageContentType.h */,
				D408E8FA31B7E347AD58F6E058D9FA0B /* NSData+ImageContentType.m */,
				40619F1585AED234B940722C7704ACC0 /* NSImage+Compatibility.h */,
				338CEF1EA1E8276F4D3EEF011B36A4AD /* NSImage+Compatibility.m */,
				91A1FCF72D4EFDF47CB92FC242E300AC /* SDAnimatedImage.h */,
				21E3C16CD240C7F7929F6768CC223340 /* SDAnimatedImage.m */,
				90185AB3075BB983E6DB3C0B5F65FECC /* SDAnimatedImagePlayer.h */,
				8012156CA8D5D683C941BD52301448F2 /* SDAnimatedImagePlayer.m */,
				CDED959FEA7ECCAF5B87F7E23F2FDF74 /* SDAnimatedImageRep.h */,
				FCB225BAED5A78B4AB86F0D0987D57DE /* SDAnimatedImageRep.m */,
				ACB34A39EAA1E17A9640C36C6A50D723 /* SDAnimatedImageView.h */,
				9498391D4C15AF61D113EA6FEEC1CD5E /* SDAnimatedImageView.m */,
				134581C0343EFCFC8C17FABBB299F83C /* SDAnimatedImageView+WebCache.h */,
				DDCFB0568851722C689867C3D8B9B3FF /* SDAnimatedImageView+WebCache.m */,
				526D021BF988C55A8A8CA5AD0E47E146 /* SDAssociatedObject.h */,
				809BE2F73F70AC9C53AB791EF55F89EE /* SDAssociatedObject.m */,
				0A64BF1E08996EC1F0DF0FB68C96926E /* SDAsyncBlockOperation.h */,
				FF26EEDEA50A4C0EA7BDBC663422F83F /* SDAsyncBlockOperation.m */,
				9802B7F6F402B20B8A338B418A577F8B /* SDCallbackQueue.h */,
				37D28101EFF6EEA2D2FF8280F27CB047 /* SDCallbackQueue.m */,
				D05F6AFDF66188951FC5DD9C8922661E /* SDDeviceHelper.h */,
				F763ABEC120B4A237083642AA4BC5263 /* SDDeviceHelper.m */,
				3628F6C8D9E374D4847A9A203CA99959 /* SDDiskCache.h */,
				91A04E67F7FE9F78BC324A1F2757C368 /* SDDiskCache.m */,
				19EE0167D7DEA8CE74573DA880CD5231 /* SDDisplayLink.h */,
				5B579E55322D6CA76C1E3224B689E63E /* SDDisplayLink.m */,
				0FF6F2DB836BE8463976732D1C1DE2FA /* SDFileAttributeHelper.h */,
				9CFEA55F82E0CDC85EE91E1964CB31D3 /* SDFileAttributeHelper.m */,
				7C7B792150E6976AD8EAB479D64122BC /* SDGraphicsImageRenderer.h */,
				EB4CAE5C287D7E8203BE39E247CC4EAD /* SDGraphicsImageRenderer.m */,
				F93249511041AC418ECE46605CB8AF22 /* SDImageAPNGCoder.h */,
				88022B2A5229903CAF13B27964E479CE /* SDImageAPNGCoder.m */,
				A422DFB0444CD0114193E01EC348D26D /* SDImageAssetManager.h */,
				5AD4A601F33390204638051B079BF863 /* SDImageAssetManager.m */,
				00AE2D6DD6DF51C4C2B09FB40A83486B /* SDImageAWebPCoder.h */,
				081EE680B588A08AEB6EA681D59E52B6 /* SDImageAWebPCoder.m */,
				00933C67E356438676C296252CBE29B7 /* SDImageCache.h */,
				333901A5FDF5D2E3D72F5A2E5F152188 /* SDImageCache.m */,
				5F9D93F33DB66DB8D2A0E81CAFA3790C /* SDImageCacheConfig.h */,
				0D2F826E2347A8F47CC8359A75F95D2C /* SDImageCacheConfig.m */,
				53CF8F5F962D52D648117B8528335011 /* SDImageCacheDefine.h */,
				B289029A2A9E63EE71F2F75DC7FDDF25 /* SDImageCacheDefine.m */,
				21CA9480BB54BFAF4820FD52800AC42A /* SDImageCachesManager.h */,
				DD4A071432370AF1466BC51056CAE90F /* SDImageCachesManager.m */,
				7E3461DFB3DF66071A606253691043A3 /* SDImageCachesManagerOperation.h */,
				277C2FBFE576DA601E0FECA3B79785DD /* SDImageCachesManagerOperation.m */,
				8EF44FC4EE5A1070C421EEFE9B6B44D3 /* SDImageCoder.h */,
				D81065B2050933B69046A61F756EF33D /* SDImageCoder.m */,
				6F994383150A741DF9B1FFB4FE6D1F76 /* SDImageCoderHelper.h */,
				B04D9195CE7D91E33317BE7D5613E39E /* SDImageCoderHelper.m */,
				F434B085C4FCAB1CBE1653D90C47855F /* SDImageCodersManager.h */,
				2400365BFCC706592D47F38B7877DBFB /* SDImageCodersManager.m */,
				54042645301ABCE32CA1225864679578 /* SDImageFrame.h */,
				2F5E02AD32A21530C1955F0DFF054011 /* SDImageFrame.m */,
				75DCD7E22599C1BAAF8EE36561E572A0 /* SDImageFramePool.h */,
				A9BE1772A58103E17BC879908BE6C7F6 /* SDImageFramePool.m */,
				BB03859F9C5341E9D17E770C6121EA66 /* SDImageGIFCoder.h */,
				9F535C3DFAA4AAD14F4AA24E7D394F14 /* SDImageGIFCoder.m */,
				4AC63E9D4FF571A8F0813F399A2729E5 /* SDImageGraphics.h */,
				59BA15EF657BB236D624BF810C0FAE84 /* SDImageGraphics.m */,
				7535BAB079BE7ECD688606B8DFB5E615 /* SDImageHEICCoder.h */,
				93E1F359B238F540B0795D9287E2F3EF /* SDImageHEICCoder.m */,
				4E1127B1D755504DC3CFED311B7A7C58 /* SDImageIOAnimatedCoder.h */,
				84F8812EAE588F093B026AD494074702 /* SDImageIOAnimatedCoder.m */,
				7AF535030BB921937BBB79112499EBB5 /* SDImageIOAnimatedCoderInternal.h */,
				87DB018C0EBE728B28D6A5AE5285AAC0 /* SDImageIOCoder.h */,
				58DFE05D0C28250C4F795C338572A86E /* SDImageIOCoder.m */,
				02763698D1944FC236DFC2F999C0F24D /* SDImageLoader.h */,
				068F7A2A72C969797DEAACD180194E27 /* SDImageLoader.m */,
				5E4FABC63538AC83AD5D1CC10A48D20C /* SDImageLoadersManager.h */,
				979FDACF6F8EE8EC3CB4555AC7E17EB1 /* SDImageLoadersManager.m */,
				2C7A9003FBE1F803ED91B1154EBB0921 /* SDImageTransformer.h */,
				916C59FA0D53BAB0D933C7AEA28B2D8B /* SDImageTransformer.m */,
				28F5A8AEFFDEE9163E61E979254AAE9C /* SDInternalMacros.h */,
				82762ED739DDD5E6A82A1AD119D1DC5A /* SDInternalMacros.m */,
				CB484E8CFD74886D763544CC37AEC0AD /* SDMemoryCache.h */,
				E90D0CC7D650A88957252D78CB5E955B /* SDMemoryCache.m */,
				A1D16953B9BB4516D0C668C8A5B90DC0 /* SDmetamacros.h */,
				3FBB19DA89A41EF55BCE054C3F8219E7 /* SDWeakProxy.h */,
				1ACA1B3136E32A65C499F5A086825B21 /* SDWeakProxy.m */,
				504BF3089EA68E184C483BC835B4249E /* SDWebImage.h */,
				55F67D1DD1ABE1B09C431AEBD34DEE83 /* SDWebImageCacheKeyFilter.h */,
				9CFF3FC67131B888135B0062F9DACD6E /* SDWebImageCacheKeyFilter.m */,
				78C26B66151FF0666D53FF95DFB24CC6 /* SDWebImageCacheSerializer.h */,
				9C4C564FCE56D9FEC88A0D69FF61656E /* SDWebImageCacheSerializer.m */,
				E01A2BB5D464E82E55335FA1E8D45F34 /* SDWebImageCompat.h */,
				252E12216ED1E68BEE62EF6E200684E9 /* SDWebImageCompat.m */,
				6C97386223F9362E9C2E4EB9445ECD34 /* SDWebImageDefine.h */,
				C0FA2332A3665702D9B2ECB0498FF316 /* SDWebImageDefine.m */,
				0FF02D582583464E21DF635A2FD0456E /* SDWebImageDownloader.h */,
				B3E1C31DB1E16039089AFC02E19FAED3 /* SDWebImageDownloader.m */,
				B6FB2AE1358B06D39A0622A9623A6E90 /* SDWebImageDownloaderConfig.h */,
				4728BDCB3F8F6B6F2A48BB8B5BA41AA6 /* SDWebImageDownloaderConfig.m */,
				15DF37520825A33A1CD95D9E4581E7FA /* SDWebImageDownloaderDecryptor.h */,
				AC8403BA1DA69DF47C2FE4D61B2C72D2 /* SDWebImageDownloaderDecryptor.m */,
				F0FE15373CF1F72CCAC4E44E93611A6A /* SDWebImageDownloaderOperation.h */,
				0E7D88775D9607B5B2669B687E24DF97 /* SDWebImageDownloaderOperation.m */,
				3DC112141773B19D88086A423E43782B /* SDWebImageDownloaderRequestModifier.h */,
				DD8D5D70EF7F01FB42EC000BDD1D0A02 /* SDWebImageDownloaderRequestModifier.m */,
				49569045C965D45C4CDCCED2FD6BDF83 /* SDWebImageDownloaderResponseModifier.h */,
				6C869079073C6295F67F64D07EE6A665 /* SDWebImageDownloaderResponseModifier.m */,
				0C08A8DD6D68F39F4C1D100F1FA4AFD2 /* SDWebImageError.h */,
				4482A8841A1F1E5BDF45F88FF764F9E8 /* SDWebImageError.m */,
				193E1CD46454AB8774F082A207245060 /* SDWebImageIndicator.h */,
				A910FE54FECA1F4AB9FF751A7369D2CA /* SDWebImageIndicator.m */,
				274D5C2DFA7D1E312A79FE9BDFFCC517 /* SDWebImageManager.h */,
				36379569FFBC1115EE083C467AF5F8DD /* SDWebImageManager.m */,
				C39FBA296695B60A112108133BC9FF16 /* SDWebImageOperation.h */,
				8B040808633321DF5B09556AF2A4DE3E /* SDWebImageOperation.m */,
				7F528570A41D2A3DBF6C8365447FC67F /* SDWebImageOptionsProcessor.h */,
				54B746288D2A859351C5D7D38D87FA92 /* SDWebImageOptionsProcessor.m */,
				52B9FEE904A761B23281300065D431FE /* SDWebImagePrefetcher.h */,
				B1F0658CBB539606C33BF2BE5566DAEB /* SDWebImagePrefetcher.m */,
				72FD41EE19E21D1DA794248E7C5B03F2 /* SDWebImageTransition.h */,
				C5D5D3432B3C6231931E5F8389E9F792 /* SDWebImageTransition.m */,
				664F757AF84876E95DD100931F5E51D9 /* SDWebImageTransitionInternal.h */,
				A5A669CC4569721962BE4D662595F4EB /* UIButton+WebCache.h */,
				60656150275FC4AC88B9FEB08BA7CDAA /* UIButton+WebCache.m */,
				037CFB958236492D2431DAD90447D27F /* UIColor+SDHexString.h */,
				90C34F503B1579DDFD3EE34CC2896D1E /* UIColor+SDHexString.m */,
				D6B3E3D64CA0E0B3F6CE198AA3DD2B80 /* UIImage+ExtendedCacheData.h */,
				9538304586F81B4A67AF4A2C3B907159 /* UIImage+ExtendedCacheData.m */,
				FC4C0D09130EA693500EF4707BA41BA4 /* UIImage+ForceDecode.h */,
				0C7630AD87ADBD4A5E947D432D1F29AB /* UIImage+ForceDecode.m */,
				BD048AA3CEB4874E738992566F3C2588 /* UIImage+GIF.h */,
				2124873B53EFFAC993AB2DABE9C3AA0A /* UIImage+GIF.m */,
				44C6A649E2C9593B4DC800E4A2248313 /* UIImage+MemoryCacheCost.h */,
				0DFF43AD2C53B6E68DC2F1E052DCBBB1 /* UIImage+MemoryCacheCost.m */,
				4B830B4BA8A92CA00F890A9BE58AFCA2 /* UIImage+Metadata.h */,
				36CABB822BC8CFBE8AF794099CFEB6B7 /* UIImage+Metadata.m */,
				FDE5B89F25BE0FB60E4F5EE06DCC7A8F /* UIImage+MultiFormat.h */,
				B8394AFF286008A68FCF59270538841E /* UIImage+MultiFormat.m */,
				29702D0F4CE361960D3843D0FC1516EB /* UIImage+Transform.h */,
				A2676DCDD2D5F740722D70C227DC2C00 /* UIImage+Transform.m */,
				6BEDD4E76F3B805950249BF32D6FE195 /* UIImageView+HighlightedWebCache.h */,
				F499E0C710700093032636C2129A6C42 /* UIImageView+HighlightedWebCache.m */,
				DD9701B9F93CDA659F27D81359790054 /* UIImageView+WebCache.h */,
				74E9EE0A4F521C8E22CD4FD973883814 /* UIImageView+WebCache.m */,
				6C502322A90F283DE0AA7996C79F1DD5 /* UIView+WebCache.h */,
				714586DDD6BB2DC815586BC92DE1DF59 /* UIView+WebCache.m */,
				CEFCB823E63805B6EED70E9D86E1EBF1 /* UIView+WebCacheOperation.h */,
				3C13667A8AC102064180EEC15D2530F8 /* UIView+WebCacheOperation.m */,
				AF045921C014ECFE871641656F7A8263 /* UIView+WebCacheState.h */,
				896562C9511358EE67C8CBBEE34E6444 /* UIView+WebCacheState.m */,
				111360965D2B0C4F9F08C43DBFCBC4CB /* Resources */,
			);
			name = Core;
			sourceTree = "<group>";
		};
		DAB43E89884030ECB6BB40AB5D3B85FB /* ios */ = {
			isa = PBXGroup;
			children = (
				072290CA55EB77D00547F03D7EBB7E52 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		DEAE503957E2A86B28F9DE93A98CF850 /* darwin */ = {
			isa = PBXGroup;
			children = (
				4B40D60934949F7C9BB5F24D53719637 /* path_provider_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		E032A7A5A8C444180ABE802F03D91461 /* sqflite_darwin */ = {
			isa = PBXGroup;
			children = (
				3B60E3606FF684C80B30BD09241E1A44 /* SqfliteCursor.h */,
				C18CCDB70542EF82D63836F264A1D7A1 /* SqfliteCursor.m */,
				12155415260FB301F696655FE9BB0F0C /* SqfliteDarwinDatabase.h */,
				CD43F23FAB132293A968A5F2629F015C /* SqfliteDarwinDatabase.m */,
				2B858A22D990A1775CB93A235A5DE9E8 /* SqfliteDarwinDatabaseAdditions.h */,
				C79B86A180E741505587DBDDD31D82D0 /* SqfliteDarwinDatabaseAdditions.m */,
				CB8932706B28A8B6A8A1A1FEF3423189 /* SqfliteDarwinDatabaseQueue.h */,
				959C9CB804494791DEA4AC088B37A164 /* SqfliteDarwinDatabaseQueue.m */,
				594A629AD1C412C799A3F8B292A83196 /* SqfliteDarwinDB.h */,
				5F20F82418D2781C08DEACAF881C7D20 /* SqfliteDarwinImport.h */,
				56D90C42BA11BE7FBFF39A02376CD60D /* SqfliteDarwinResultSet.h */,
				E48BE6928FA5CBD2A849564C56FA59CA /* SqfliteDarwinResultSet.m */,
				F97632DB1EBEBF407112E2947DB9517C /* SqfliteDatabase.h */,
				308FC1A1C9F81F5189B9B9A14EB50D32 /* SqfliteDatabase.m */,
				D81657E55669EF5C52335B7E878D96BC /* SqfliteImport.h */,
				37230F72808ED6F5F6F430C96DBD5CF8 /* SqfliteOperation.h */,
				AAD87A5D5939F79B971D95C1E00D1631 /* SqfliteOperation.m */,
				1BB31DB611B78E2CC38EAA0E03E17C4A /* SqflitePlugin.h */,
				DA4FBD87572142A21B8226CCAA644401 /* SqflitePlugin.m */,
				8FF1F91DBDE1596139948E879F1FFD2D /* include */,
			);
			name = sqflite_darwin;
			path = sqflite_darwin;
			sourceTree = "<group>";
		};
		E12DF68C288C127C5FD16480B9C5DA81 /* Pod */ = {
			isa = PBXGroup;
			children = (
				4ADB5EEEE416A586DA49B2F0449BA7D0 /* file_picker.podspec */,
				DD433F4B904B37303A119E0F5F06322E /* LICENSE */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		E41D352625789D32714EBB5DDFC9F317 /* ios */ = {
			isa = PBXGroup;
			children = (
				F2FA91F454A940183AE06DBA65D8A0D8 /* Classes */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		E683D3C03046432B0C25822465ABA864 /* .. */ = {
			isa = PBXGroup;
			children = (
				A81439EE9636CA0908833A58AB894C8E /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		E6BEDF33F5291497319EE520975458CE /* Documents */ = {
			isa = PBXGroup;
			children = (
				B87BBCDBAFF41E35A4C7EC0D524C78C4 /* personal-project */,
			);
			name = Documents;
			path = Documents;
			sourceTree = "<group>";
		};
		E806A4F97D48F5FD3B4CA1BC617EA1F5 /* Pods-Runner */ = {
			isa = PBXGroup;
			children = (
				C24C1A035F22D24883C6F1876F274C6F /* Pods-Runner.modulemap */,
				ECDF6AA713870989438BA93B395DC557 /* Pods-Runner-acknowledgements.markdown */,
				1B136F41D5B5FAE96EA576E768A5771D /* Pods-Runner-acknowledgements.plist */,
				E794494AB90477BA94C681E994ADF341 /* Pods-Runner-dummy.m */,
				773BFCE41424EB8EBB72EF3F6A5FB719 /* Pods-Runner-frameworks.sh */,
				0AFB643DA4919253F749E2836A5AAFDC /* Pods-Runner-Info.plist */,
				499E3722E8DACDC2AAA6C15AD1796520 /* Pods-Runner-umbrella.h */,
				728A5ED1DD60BAC8C8F382A87B976F84 /* Pods-Runner.debug.xcconfig */,
				C50FD10CD19FF6999637BA5ECAF1681B /* Pods-Runner.profile.xcconfig */,
				B703785507B22BAF0B800737429577EC /* Pods-Runner.release.xcconfig */,
			);
			name = "Pods-Runner";
			path = "Target Support Files/Pods-Runner";
			sourceTree = "<group>";
		};
		E9CD685A9C35501505F4106E7CF15A90 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				EC2FBA921F6FD4CE30B0819FEA0EF930 /* .. */,
				BAA35C7CE233DF3F72EEAF27FB756E37 /* Pod */,
				13AAAC0B957BC4E4218F8F8724C16E90 /* Support Files */,
			);
			name = path_provider_foundation;
			path = ../.symlinks/plugins/path_provider_foundation/darwin;
			sourceTree = "<group>";
		};
		EC2FBA921F6FD4CE30B0819FEA0EF930 /* .. */ = {
			isa = PBXGroup;
			children = (
				89A4BC091B72A4B85E37C24B055E1A14 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources";
			sourceTree = "<group>";
		};
		EDF1005B1B7ED4F0C2936223281F8427 /* ImageDataManager */ = {
			isa = PBXGroup;
			children = (
				4208203CE9D4E600FBD80CDB1B84246F /* DKAsset.swift */,
				1D4742F4C898315E9D8FC262C1D0C5D1 /* DKAsset+Export.swift */,
				5D5F83FABF185E339BDD61B8A9CC26B3 /* DKAsset+Fetch.swift */,
				7C26E48B6A32C3499D867872EB112538 /* DKAssetGroup.swift */,
				584A66BB8106A644D5AFEBF27AFF4F56 /* DKImageBaseManager.swift */,
				9E764D8CAB9CEF2030E6C627DEADC064 /* DKImageDataManager.swift */,
				E2C12FE444D39CE575CAC618A1B31085 /* DKImageGroupDataManager.swift */,
			);
			name = ImageDataManager;
			sourceTree = "<group>";
		};
		EEA4838B475BA9C7469FD1EB9C34A944 /* ios */ = {
			isa = PBXGroup;
			children = (
				58EB343BD68CE81E1AD61F300B37F3C2 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		EF1111E106A941895FB62E9D7050900C /* Support Files */ = {
			isa = PBXGroup;
			children = (
				276A3A2F03B7F73B7987B406B0FC38A0 /* file_picker.modulemap */,
				E527F5CD3726095AD1461D752CF3CF11 /* file_picker-dummy.m */,
				50AC74851B225E7DCA01FC2EB7A3D1E8 /* file_picker-Info.plist */,
				DC15D00FBE9A01A092BDD51B2537C3DD /* file_picker-prefix.pch */,
				538B48C36546DDA30EF65125984C05AD /* file_picker-umbrella.h */,
				CB507F3628C2744ADBD6115BD432C868 /* file_picker.debug.xcconfig */,
				02DAA13E6CB0364A1D6EBC7A26C8BC22 /* file_picker.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/file_picker";
			sourceTree = "<group>";
		};
		EF15B6FCEAA30EC63BAB08BDAC363DF4 /* kwaci-rag-app */ = {
			isa = PBXGroup;
			children = (
				C1A8230737624DF3CCCA41984CE2C497 /* kwaci_chat_app */,
			);
			name = "kwaci-rag-app";
			path = "kwaci-rag-app";
			sourceTree = "<group>";
		};
		F2FA91F454A940183AE06DBA65D8A0D8 /* Classes */ = {
			isa = PBXGroup;
			children = (
				746BBF33504A0699A90AAF0DF6BF1C3A /* FileInfo.h */,
				98ADA473B15A879BF5041B3AD6D79415 /* FileInfo.m */,
				83A952E2F026F6B77F742162DC2F406D /* FilePickerPlugin.h */,
				1BB8FBB76EC625D4961877C0E0F9B572 /* FilePickerPlugin.m */,
				9511EA31BFCF2E016059E98B653E9B53 /* FileUtils.h */,
				EE5492D53DE03B1A0240C9CF7EFF6D97 /* FileUtils.m */,
				4B48C6A748A2F1E2BA91A219CD57969A /* ImageUtils.h */,
				26A7F2063FCD4E6A2871535A1DF00677 /* ImageUtils.m */,
			);
			name = Classes;
			path = Classes;
			sourceTree = "<group>";
		};
		F6B966D7C48307F240352AC8F7AA7219 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				455D12245BE072B1B8B2EB98F539B8DB /* Flutter.debug.xcconfig */,
				0887384FF3482C3F4C998D0C0F2D7C94 /* Flutter.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Pods/Target Support Files/Flutter";
			sourceTree = "<group>";
		};
		F827E4AC4BC8F004CD4A47D7E048530B /* .. */ = {
			isa = PBXGroup;
			children = (
				D22E6242D11F3B035CF72F4F0A3BECC3 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources";
			sourceTree = "<group>";
		};
		FB68E1B5A1092FD5D705F1F84B0D5F27 /* .. */ = {
			isa = PBXGroup;
			children = (
				ACF11E49E12195551017683244BC5E81 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		FDF80E738F2C8AD00A37503743ECD554 /* .. */ = {
			isa = PBXGroup;
			children = (
				02907D04D1F8BE272055277482499F7D /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		FF40A9865B2CCA6C50D137A1589D80E6 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				1CCBDAE63D1F8C00D06AB7AE8F4BEB1C /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		049FE6105D6544CD66FAFD875B6385F5 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				A9E7282741F0E6E07170CC19B997C71D /* NSBezierPath+SDRoundedCorners.h in Headers */,
				8AFE4E11CFA9D6BBE900061E961CC807 /* NSButton+WebCache.h in Headers */,
				548ECE54A90307FE3F5345D6DC709499 /* NSData+ImageContentType.h in Headers */,
				1A9A8F94BA9B2D08557D79AE1A7A24F4 /* NSImage+Compatibility.h in Headers */,
				9E9A0BFB5B841445946F259ECE390631 /* SDAnimatedImage.h in Headers */,
				E4B5C68FD3DC266A04D4D496C2720D99 /* SDAnimatedImagePlayer.h in Headers */,
				7FACF4B1CB684CD901CCC039D54D9957 /* SDAnimatedImageRep.h in Headers */,
				BE851FC371BCCACD1D018945A88D8D2C /* SDAnimatedImageView.h in Headers */,
				C0B6E2F9D080CD87F58BEABFFCAF63C3 /* SDAnimatedImageView+WebCache.h in Headers */,
				086880AB1FFF15ADE6BACED51B0A10D7 /* SDAssociatedObject.h in Headers */,
				6B61792C661E8B939635FEC5561B4D8C /* SDAsyncBlockOperation.h in Headers */,
				3376EBA340CCF5916A93365EF8DB9478 /* SDCallbackQueue.h in Headers */,
				7D968E112E601623AAD444BE04A5F922 /* SDDeviceHelper.h in Headers */,
				EDDDC9C8CAAC3EE0D2848F99221B404D /* SDDiskCache.h in Headers */,
				AD83558AB73FF7106AD42998FB17F0F6 /* SDDisplayLink.h in Headers */,
				068EE2BFCD9869F3482F0CA621F1E3FF /* SDFileAttributeHelper.h in Headers */,
				E2B1125C5CAB7C33EDD8E744CFB18118 /* SDGraphicsImageRenderer.h in Headers */,
				7B143193409558126EE4F4DC0330338B /* SDImageAPNGCoder.h in Headers */,
				B6B1DCFBD2BE228A10FE747CBA058145 /* SDImageAssetManager.h in Headers */,
				324AA14FDCAA7101CD2637507DEE8387 /* SDImageAWebPCoder.h in Headers */,
				6B39D201D52530DCFF681730B0D094A2 /* SDImageCache.h in Headers */,
				C0406DA2265E37B706A6147B6610F840 /* SDImageCacheConfig.h in Headers */,
				3F44797CDC1B48689B7AF1F70A0503D2 /* SDImageCacheDefine.h in Headers */,
				F19CFB681450153772E11ED7BB795F62 /* SDImageCachesManager.h in Headers */,
				5ABD26486C5C5CD33F97B0E36B0C1551 /* SDImageCachesManagerOperation.h in Headers */,
				6C450F143FA798F37D14D2D829A2B44C /* SDImageCoder.h in Headers */,
				23053B38F426C67DAA0AEF05D662F5B3 /* SDImageCoderHelper.h in Headers */,
				7110985989D72D210332C31DB8DA3566 /* SDImageCodersManager.h in Headers */,
				D5A1D37AE11F4F7A462A1211A995AB3A /* SDImageFrame.h in Headers */,
				510D8A0B13E38B0184E23FE20121390A /* SDImageFramePool.h in Headers */,
				4DE609D64E8450C7B0CA49C54E11A437 /* SDImageGIFCoder.h in Headers */,
				278303399A5839329320379983235C2D /* SDImageGraphics.h in Headers */,
				9BCBB08937EEC52DD116CB066C55A8D5 /* SDImageHEICCoder.h in Headers */,
				147167D55FDCFE4AAB312AB16D90A89B /* SDImageIOAnimatedCoder.h in Headers */,
				DEBF7477BF25113EF3E4F33B85C9E070 /* SDImageIOAnimatedCoderInternal.h in Headers */,
				290E3ADDEB5B8DEA3EE65C3D5B29F6ED /* SDImageIOCoder.h in Headers */,
				4B34BCA8A854401BE9AA60D04B620102 /* SDImageLoader.h in Headers */,
				A3FAEAF60836F44BC35F22B04A8E51EF /* SDImageLoadersManager.h in Headers */,
				AD4D02ADC0131FFD1F5A6AEE93E78B9B /* SDImageTransformer.h in Headers */,
				BC927B13E69C7FE5A03B50D658FD3F7B /* SDInternalMacros.h in Headers */,
				CC7D9D556D9C31F069B1E66A173BD3D8 /* SDMemoryCache.h in Headers */,
				C76ACC97EB13E542E55159719AD66F08 /* SDmetamacros.h in Headers */,
				F66F432C6DCDC38A4612C938980D1AB7 /* SDWeakProxy.h in Headers */,
				D33C42C118850292C5FED7F43E733225 /* SDWebImage.h in Headers */,
				074A9652FFB4790E36600DE59E36C980 /* SDWebImage-umbrella.h in Headers */,
				91288D91F937EB83521BA4A656F9FA96 /* SDWebImageCacheKeyFilter.h in Headers */,
				1C27C3C1BBB4F6BB8552C7F5B2819E20 /* SDWebImageCacheSerializer.h in Headers */,
				E681E0A5396267D4199116423D1E1656 /* SDWebImageCompat.h in Headers */,
				71B98C5CDE17A252AF0DFE252A5591C4 /* SDWebImageDefine.h in Headers */,
				19A36F47A24047A22A3DBB9AA5C5E47E /* SDWebImageDownloader.h in Headers */,
				0CC891AEF7AEC2366F15F8B52C12B55D /* SDWebImageDownloaderConfig.h in Headers */,
				504BDAE9EC84472751996A01FE6FE5EF /* SDWebImageDownloaderDecryptor.h in Headers */,
				06602B786B5C16AD1FBA445A4064BFDE /* SDWebImageDownloaderOperation.h in Headers */,
				8D9465B014109D15740EFEE5962D4DAB /* SDWebImageDownloaderRequestModifier.h in Headers */,
				9563B81D26A63ADAC9F892FF20BFD6A4 /* SDWebImageDownloaderResponseModifier.h in Headers */,
				7398852A92A8022C4C99E927B47A891C /* SDWebImageError.h in Headers */,
				06CBBD253C541754C8DA43FD5D930BBF /* SDWebImageIndicator.h in Headers */,
				A4EEE153531F362A113DF55B67EAB12A /* SDWebImageManager.h in Headers */,
				55156F828612CD7261B51BF7D73A3C43 /* SDWebImageOperation.h in Headers */,
				925A7D8A4F1AC70B87DBB97CA689EAD8 /* SDWebImageOptionsProcessor.h in Headers */,
				4C397E3C975AE4A17F61470FF3E81EED /* SDWebImagePrefetcher.h in Headers */,
				A647070903B6CD5B12163DB89389586B /* SDWebImageTransition.h in Headers */,
				120933850149F94F7325BD3971F26EDD /* SDWebImageTransitionInternal.h in Headers */,
				972C8BA1346E91E9797241BF97D010E7 /* UIButton+WebCache.h in Headers */,
				0359537355B4B312FFC8D625AAA750B8 /* UIColor+SDHexString.h in Headers */,
				8A9E15908375B00041AB6EE4437F9F20 /* UIImage+ExtendedCacheData.h in Headers */,
				03AC14B534B1222D6704672EF0D7C621 /* UIImage+ForceDecode.h in Headers */,
				33FE25FE9D906483259EE5C75E2BDA02 /* UIImage+GIF.h in Headers */,
				02B9991D0D6FC6F2558078A11F063889 /* UIImage+MemoryCacheCost.h in Headers */,
				F91FDDBA788037B15CB431D4D65CDB43 /* UIImage+Metadata.h in Headers */,
				77D45BA1978F313656E390A9CC048060 /* UIImage+MultiFormat.h in Headers */,
				55075053B5292AB8C5D23FAFAD8F8FF2 /* UIImage+Transform.h in Headers */,
				A4F87AB0E8B8439772147231B8747C47 /* UIImageView+HighlightedWebCache.h in Headers */,
				FEA145D9CFEB4AFF9708D6F89551B5E1 /* UIImageView+WebCache.h in Headers */,
				3AA426D9310F16A66DA067D7FABAEC8C /* UIView+WebCache.h in Headers */,
				A9E98C7C9ED354084E74509D40CA274F /* UIView+WebCacheOperation.h in Headers */,
				1AE4057C2E9D5CE8ECC1E3CF5F057BDF /* UIView+WebCacheState.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0F00ABDBAB51EFF0DC48618B6AC1429C /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				3F087BC352E4E9B5678D6E9EDC7EAF6B /* DKImagePickerController-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1977633FB31E6DD9F84635E2A98C9E34 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				C6FBD973D2652FED4919C7BAEBEBCFE1 /* file_picker-umbrella.h in Headers */,
				2BF7C1AA748932E55AB80665DB1D1740 /* FileInfo.h in Headers */,
				122F8872E1D00DB6FA0877C26E9BA16F /* FilePickerPlugin.h in Headers */,
				BD09BB348998C2EBB24F9DCDB3DCC44B /* FileUtils.h in Headers */,
				8C46A70E66827A4F102C828595FCAE2B /* ImageUtils.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3EDC6BE0B2553C1412F3A2BA4A3EFCE4 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				2631CEC725AA8C0549E5D2980171E0B0 /* path_provider_foundation-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		88331786A4401595DDB5D60225EDA526 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				6B3B1BDAF4CB41CC3284343552321227 /* Pods-Runner-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A68C5F7E7ED2DC561D219562C09BB396 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				D48210CEBF1E59F5F8734D54807E1AC4 /* sqflite_darwin-umbrella.h in Headers */,
				31308839CCC1B53E9F3BAE31BF261759 /* SqfliteCursor.h in Headers */,
				2EAFA69DF31F8EA6FC5E9F204B7A9B3B /* SqfliteDarwinDatabase.h in Headers */,
				8DEBD5860446E4DF39482F4202E700BB /* SqfliteDarwinDatabaseAdditions.h in Headers */,
				0447458B575FC97F4202172E28CFD17C /* SqfliteDarwinDatabaseQueue.h in Headers */,
				F161FE59EEEA186344C3DD6251AF57E2 /* SqfliteDarwinDB.h in Headers */,
				E134B4A94F733FD002943EA8668D57CF /* SqfliteDarwinImport.h in Headers */,
				23F3D313BD8BCDE25356247DD21EB424 /* SqfliteDarwinResultSet.h in Headers */,
				6F06EEE2B41EDE7C90E9BDC800220BB8 /* SqfliteDatabase.h in Headers */,
				9B9239D7A4B142CA737EF14847422EB9 /* SqfliteImport.h in Headers */,
				F7EAB2A7E514C004342D47A0E79E4CE2 /* SqfliteImportPublic.h in Headers */,
				36524C6893C49D93D17AB9A9FC0F9F99 /* SqfliteOperation.h in Headers */,
				37252DE0C242ADD277A3C92981C76C20 /* SqflitePlugin.h in Headers */,
				E1E534DDF2AE95A227D3C3E2A408E411 /* SqflitePluginPublic.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A9A24AAC9C764EB0196CEA4B7AC74B65 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				EB3BBE3BB9F48ADBB15EF97D00758A85 /* SwiftyGif.h in Headers */,
				9F3D33F8C2DDF65AC2676DB32180A50F /* SwiftyGif-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E96B5B7C67CF450FCA013689558B8345 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				8B884FC13DCEBF47DD7C0E38DE271993 /* DKPhotoGallery-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FC4DA9BD27A4914E75F803590E4C4B8B /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				EB2DC96CDCF638AB89007D2DB0F3119A /* Pods-RunnerTests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		15AEBAFA43912F898FC2E1A73025C946 /* SwiftyGif */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 83EEBFCF2734736C883614ABD96B2C4D /* Build configuration list for PBXNativeTarget "SwiftyGif" */;
			buildPhases = (
				A9A24AAC9C764EB0196CEA4B7AC74B65 /* Headers */,
				3EF510EE93084E03DA3845A62038C53F /* Sources */,
				F8940B4FE519CE7C44A02B67C682550B /* Frameworks */,
				88A217B1E3B043801E34972BA36C7F9D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				933FA60DC42604B67EEB485C6102DF6E /* PBXTargetDependency */,
			);
			name = SwiftyGif;
			productName = SwiftyGif;
			productReference = 7AD67EFB9A7864151301DC2890207AA2 /* SwiftyGif */;
			productType = "com.apple.product-type.framework";
		};
		176E48377247D5011E906CB1A39E71BA /* SwiftyGif-SwiftyGif */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8EB075CB7C93C9D5AB6636A2D3EBADB2 /* Build configuration list for PBXNativeTarget "SwiftyGif-SwiftyGif" */;
			buildPhases = (
				6E4F7D36CE926A14A3750240BDCEBBC7 /* Sources */,
				9E3EBACF1161CC6DC85BF369D416FE5D /* Frameworks */,
				5EBB8A78931AD9D9FF34ABC3EE9EBAF5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "SwiftyGif-SwiftyGif";
			productName = SwiftyGif;
			productReference = 0A37DE9A55F6CEA1AF18BDEF878BF1F5 /* SwiftyGif-SwiftyGif */;
			productType = "com.apple.product-type.bundle";
		};
		3232F0C0E7C65B232832393F9ADDD8C3 /* Pods-RunnerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D52099AA1537D0F3745166E1889F6CA3 /* Build configuration list for PBXNativeTarget "Pods-RunnerTests" */;
			buildPhases = (
				FC4DA9BD27A4914E75F803590E4C4B8B /* Headers */,
				001CCB0FEA80CD1C6BF534C6D9C6283A /* Sources */,
				4380924F566AA01EB048DC15F9BC6D33 /* Frameworks */,
				4A765108DEAFDEBF078F71CDDBE3414E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				46EFD74D2B78A930DB1C6739741D8836 /* PBXTargetDependency */,
			);
			name = "Pods-RunnerTests";
			productName = Pods_RunnerTests;
			productReference = 6C3345B1B3CAEDF5B03B1F731FDC492E /* Pods-RunnerTests */;
			productType = "com.apple.product-type.framework";
		};
		3847153A6E5EEFB86565BA840768F429 /* SDWebImage */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8CCB4203D2C337DE5C06A7A3BDCCB375 /* Build configuration list for PBXNativeTarget "SDWebImage" */;
			buildPhases = (
				049FE6105D6544CD66FAFD875B6385F5 /* Headers */,
				DC6106B7A314C7DC49C3BDCE4EF85380 /* Sources */,
				15182316F4D24F27EE087A88ED7855D7 /* Frameworks */,
				CA715BC1F8D4057E14C1239E1389CC74 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8422A1F84B10C0E8407B5BC625CC448A /* PBXTargetDependency */,
			);
			name = SDWebImage;
			productName = SDWebImage;
			productReference = B0B214D775196BA7CA8E17E53048A493 /* SDWebImage */;
			productType = "com.apple.product-type.framework";
		};
		56F581DDCB0A032454E604885E17AE3C /* path_provider_foundation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FC3EA6EE526A2F7266A44B11E3A1AD9A /* Build configuration list for PBXNativeTarget "path_provider_foundation" */;
			buildPhases = (
				3EDC6BE0B2553C1412F3A2BA4A3EFCE4 /* Headers */,
				05895E3E1AC56002880CEF9A9AC4086F /* Sources */,
				2C51CA6EC745ACB63E424757C710B36C /* Frameworks */,
				D412229AEE091A9D36A8553E8AA56B31 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				DB02C81205BFF4A09B1FECFF8AC36450 /* PBXTargetDependency */,
				3EFA8B8B9507FC320BBF2E973429562C /* PBXTargetDependency */,
			);
			name = path_provider_foundation;
			productName = path_provider_foundation;
			productReference = AE157A33FEF959A214796BFF348717F6 /* path_provider_foundation */;
			productType = "com.apple.product-type.framework";
		};
		6178324C15B5DCE31C127429F0C8EE8F /* sqflite_darwin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 84AAE47AFE07B5E560E9FE58ABDEEEDA /* Build configuration list for PBXNativeTarget "sqflite_darwin" */;
			buildPhases = (
				A68C5F7E7ED2DC561D219562C09BB396 /* Headers */,
				85F611869208A646B549018BCB5A4528 /* Sources */,
				174C2354D7E2A8F78F06F5343CD02522 /* Frameworks */,
				2A31367DF3EAEB8BCF9F9579F4B65AF6 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				FD9F7329DC5D779ADDFD9B37D8018E7A /* PBXTargetDependency */,
				41A4C9FC98019B71B314B2AB50D84BD6 /* PBXTargetDependency */,
			);
			name = sqflite_darwin;
			productName = sqflite_darwin;
			productReference = 4FE20B1CF8B318A8D9591ED1587D7592 /* sqflite_darwin */;
			productType = "com.apple.product-type.framework";
		};
		6244DB400F69AD751FA848C697E64C56 /* DKImagePickerController-DKImagePickerController */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 329CA53F21C02C8ECD7D839FF41AB425 /* Build configuration list for PBXNativeTarget "DKImagePickerController-DKImagePickerController" */;
			buildPhases = (
				B89CB190BB5F8514C4798E33BFAE23FD /* Sources */,
				10B0764DB53D501FD926D205EABAF841 /* Frameworks */,
				57D0BCF858FFC9DF6EDF52BCB76AADE6 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DKImagePickerController-DKImagePickerController";
			productName = DKImagePickerController;
			productReference = 7247E064ECCE473BC39CE9C6B1E7C0B3 /* DKImagePickerController-DKImagePickerController */;
			productType = "com.apple.product-type.bundle";
		};
		7328189F8C34132A51A6532A51BBBC06 /* DKPhotoGallery */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6707F929D05408792FD34CC7EC435BD0 /* Build configuration list for PBXNativeTarget "DKPhotoGallery" */;
			buildPhases = (
				E96B5B7C67CF450FCA013689558B8345 /* Headers */,
				8434EE1C8FB505926DB4AE80FE591F06 /* Sources */,
				0D163DED2A31ACD07E773CB58B2195F2 /* Frameworks */,
				368CE6B6A18C9F85C2B1A266E36479B1 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				D64D9BC698A91F26A5F83B76F0E7743B /* PBXTargetDependency */,
				3D7ED30C649EDFB0C07271C750A2C4C7 /* PBXTargetDependency */,
				87975D4C4563F04C323EC6166C3441E5 /* PBXTargetDependency */,
			);
			name = DKPhotoGallery;
			productName = DKPhotoGallery;
			productReference = B1B247DE42D782218EFEA0CB0F6D6454 /* DKPhotoGallery */;
			productType = "com.apple.product-type.framework";
		};
		76ECBFA16F19926BEDC92324235D3BE7 /* DKPhotoGallery-DKPhotoGallery */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2DD09EF9AB60A917430373768FD40F6F /* Build configuration list for PBXNativeTarget "DKPhotoGallery-DKPhotoGallery" */;
			buildPhases = (
				7C5443771FC094727E056A601E83BD73 /* Sources */,
				1CBCE7221733C426C1B2ED03B70869E4 /* Frameworks */,
				C7C3888445662185F83D2103ED93C1DB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DKPhotoGallery-DKPhotoGallery";
			productName = DKPhotoGallery;
			productReference = B85D4E7E4EAEF1DAD4C541A2D2E6F27D /* DKPhotoGallery-DKPhotoGallery */;
			productType = "com.apple.product-type.bundle";
		};
		8B74B458B450D74B75744B87BD747314 /* Pods-Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6EE4C99DE626F72DDA97CFB5811256BB /* Build configuration list for PBXNativeTarget "Pods-Runner" */;
			buildPhases = (
				88331786A4401595DDB5D60225EDA526 /* Headers */,
				225BE4B33B1425E7B3EEF140C19FCA66 /* Sources */,
				241A66A4D3B843675D8BAEC21318C9BC /* Frameworks */,
				9A0EBC15F9BB9C14A63BB1FBF4A626F7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B33BAEC7A29A8EB56F96FBA37B166608 /* PBXTargetDependency */,
				45B2EE17B38B6E417ADA530FBEBBD363 /* PBXTargetDependency */,
				C2AB6247883DD976FE5BCBD9C81F9B71 /* PBXTargetDependency */,
				73BECA96F8C4001271F8782E20499183 /* PBXTargetDependency */,
				B6D688C4E03E4E16FDB7B48AD6D03A83 /* PBXTargetDependency */,
				E841EFCA53FD9865FAD4D5125B73DBE6 /* PBXTargetDependency */,
				46AF3364CF2278B505DB1AF17E78A298 /* PBXTargetDependency */,
				9DE0C3F149CDA39C61ECEAA3203D80F6 /* PBXTargetDependency */,
			);
			name = "Pods-Runner";
			productName = Pods_Runner;
			productReference = 669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */;
			productType = "com.apple.product-type.framework";
		};
		94CFBA7D633ECA58DF85C327B035E6A3 /* SDWebImage-SDWebImage */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 47C73998E49BB745A58B8F4B1CE770D2 /* Build configuration list for PBXNativeTarget "SDWebImage-SDWebImage" */;
			buildPhases = (
				E0E574C3BE85C6B2D599767072DB2F89 /* Sources */,
				FF9A41C9E9F436562484E4D22987E212 /* Frameworks */,
				7D2ACE5E7205F05EF02E2AFB49840281 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "SDWebImage-SDWebImage";
			productName = SDWebImage;
			productReference = CF1281E58AA1045D4B7F33FC56691C42 /* SDWebImage-SDWebImage */;
			productType = "com.apple.product-type.bundle";
		};
		9F5CAB8B25787EBAAFCC237AEDC74E2D /* sqflite_darwin-sqflite_darwin_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = ED6CCBED66B7688A94EA7CA8E476F678 /* Build configuration list for PBXNativeTarget "sqflite_darwin-sqflite_darwin_privacy" */;
			buildPhases = (
				E268C29F7DC9A21904E2FB6696FA2B4E /* Sources */,
				6CEC46B8C85E5BC46FCB38C010453F60 /* Frameworks */,
				39FBD4A90D00EB0E4CD47633CF91B626 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "sqflite_darwin-sqflite_darwin_privacy";
			productName = sqflite_darwin_privacy;
			productReference = 071549DBDD4BFC0BD7D2715539015DC2 /* sqflite_darwin-sqflite_darwin_privacy */;
			productType = "com.apple.product-type.bundle";
		};
		A47785EC1549E51E61B1C4CB35A8EF3A /* file_picker */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 324AC0D6E6195CEFE4F4D9BC70B22A01 /* Build configuration list for PBXNativeTarget "file_picker" */;
			buildPhases = (
				1977633FB31E6DD9F84635E2A98C9E34 /* Headers */,
				14DDA23916A9CAE112ED760F9A90C586 /* Sources */,
				7DAA98D5689FEDDEE92F88371717E17D /* Frameworks */,
				295084B75CB1995B1F2235A861E3A040 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				863203EF9DF7028B5D0D72E74FA648F2 /* PBXTargetDependency */,
				959A791025C71E074DDD4F733F2EE1F4 /* PBXTargetDependency */,
			);
			name = file_picker;
			productName = file_picker;
			productReference = 308378B6ADC631E53C97B459FBCD7A32 /* file_picker */;
			productType = "com.apple.product-type.framework";
		};
		A4BE7D9267EB551461B2837139E4A9D6 /* DKImagePickerController */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 818723CB51B1DF95B98A3D66391E2239 /* Build configuration list for PBXNativeTarget "DKImagePickerController" */;
			buildPhases = (
				0F00ABDBAB51EFF0DC48618B6AC1429C /* Headers */,
				8E5C97F86EC98B7C7F3F577A4A326FCC /* Sources */,
				685A6DE1409E7B8E2287E732E094FAB1 /* Frameworks */,
				85C8CCEE24E7A34C156B6A9EADB60682 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A2B667CCB16B2F0EA7BC78D29A148FB4 /* PBXTargetDependency */,
				2694495DA747AFBF6526C7B86103CD51 /* PBXTargetDependency */,
			);
			name = DKImagePickerController;
			productName = DKImagePickerController;
			productReference = E95F4135A781F1B0F90A544CD0398420 /* DKImagePickerController */;
			productType = "com.apple.product-type.framework";
		};
		CACE6618E7996464E38687E13F67D945 /* path_provider_foundation-path_provider_foundation_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F66FDD50CC63E6B632FFB742A90739E9 /* Build configuration list for PBXNativeTarget "path_provider_foundation-path_provider_foundation_privacy" */;
			buildPhases = (
				27EA7BE09F46F020DFD119C42E71FA73 /* Sources */,
				591D7385C5908AED4F61D6BAC2D5DE02 /* Frameworks */,
				10220BAC7962E3A8251ED28470DC5B1B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "path_provider_foundation-path_provider_foundation_privacy";
			productName = path_provider_foundation_privacy;
			productReference = 3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */;
			productType = "com.apple.product-type.bundle";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				ar,
				da,
				de,
				en,
				es,
				fr,
				hu,
				it,
				ja,
				ko,
				"nb-NO",
				nl,
				pt_BR,
				ru,
				tr,
				ur,
				vi,
				"zh-Hans",
				"zh-Hant",
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = D376FA4E96F8A331CAD4704E55D34F61 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A4BE7D9267EB551461B2837139E4A9D6 /* DKImagePickerController */,
				6244DB400F69AD751FA848C697E64C56 /* DKImagePickerController-DKImagePickerController */,
				7328189F8C34132A51A6532A51BBBC06 /* DKPhotoGallery */,
				76ECBFA16F19926BEDC92324235D3BE7 /* DKPhotoGallery-DKPhotoGallery */,
				A47785EC1549E51E61B1C4CB35A8EF3A /* file_picker */,
				1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */,
				56F581DDCB0A032454E604885E17AE3C /* path_provider_foundation */,
				CACE6618E7996464E38687E13F67D945 /* path_provider_foundation-path_provider_foundation_privacy */,
				8B74B458B450D74B75744B87BD747314 /* Pods-Runner */,
				3232F0C0E7C65B232832393F9ADDD8C3 /* Pods-RunnerTests */,
				3847153A6E5EEFB86565BA840768F429 /* SDWebImage */,
				94CFBA7D633ECA58DF85C327B035E6A3 /* SDWebImage-SDWebImage */,
				6178324C15B5DCE31C127429F0C8EE8F /* sqflite_darwin */,
				9F5CAB8B25787EBAAFCC237AEDC74E2D /* sqflite_darwin-sqflite_darwin_privacy */,
				15AEBAFA43912F898FC2E1A73025C946 /* SwiftyGif */,
				176E48377247D5011E906CB1A39E71BA /* SwiftyGif-SwiftyGif */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		10220BAC7962E3A8251ED28470DC5B1B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F786403A51AF7C1C59EE05EA9FCC02F7 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		295084B75CB1995B1F2235A861E3A040 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2A31367DF3EAEB8BCF9F9579F4B65AF6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				BA582B8FC100119E53525F87B43AD8DC /* sqflite_darwin-sqflite_darwin_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		368CE6B6A18C9F85C2B1A266E36479B1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				CBD8771C8E7F52A8CD946F49B7469D34 /* DKPhotoGallery-DKPhotoGallery in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		39FBD4A90D00EB0E4CD47633CF91B626 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8F7B88F6234BA1FEDCD4477BEB4FFD05 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4A765108DEAFDEBF078F71CDDBE3414E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		57D0BCF858FFC9DF6EDF52BCB76AADE6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				5F80DDC737DBEEA7CCAC2924715CCBD0 /* ar.lproj in Resources */,
				8C76F79BD3C045A6D5A140C542E22A7D /* Base.lproj in Resources */,
				7644DB2B5EB37264A050A53D35454174 /* da.lproj in Resources */,
				A914797AFBD5B7FB21C4ABEFEDDF874D /* de.lproj in Resources */,
				097DC93583DF2306BD9F0F1DF59F388D /* en.lproj in Resources */,
				28873B7252A10AB97FA1ECB3B3FA027B /* es.lproj in Resources */,
				5B60A30157CEDDD4B1D70499975ACAE4 /* fr.lproj in Resources */,
				C44988533307F4EFCE1CD1B33235F2D3 /* hu.lproj in Resources */,
				BBDBF36B701BB90281B2445BCEAD4918 /* Images.xcassets in Resources */,
				28CDE7105B5CBA846AAA1A1C6513010E /* it.lproj in Resources */,
				82BA5B5EAC25E58F2AE92200746E34B2 /* ja.lproj in Resources */,
				348E8334AF62A1304F67CC1507757025 /* ko.lproj in Resources */,
				DEF4F43DE43C0154931606703FF52FEB /* nb-NO.lproj in Resources */,
				1EE390ED5D95BA1BEB2C3EE0B84C3ADC /* nl.lproj in Resources */,
				054BE0A371B5FE1BF7B69594AB09B203 /* PrivacyInfo.xcprivacy in Resources */,
				D20330684BC80847CF02A9307CF3A67F /* pt_BR.lproj in Resources */,
				97E9901255A66133778F2C09F0ACF7E5 /* ru.lproj in Resources */,
				30774628EB6346486D87EBD245C76288 /* tr.lproj in Resources */,
				453782C5694D74646FBF7563DA3CF18B /* ur.lproj in Resources */,
				1ED77531D5C1FA6489DD98ECF1213B8B /* vi.lproj in Resources */,
				5CA70AF50AFCFEE736476D4C600287B1 /* zh-Hans.lproj in Resources */,
				CA7D652DDCB378D0F7C6DB1565AF4AD9 /* zh-Hant.lproj in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5EBB8A78931AD9D9FF34ABC3EE9EBAF5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				EC03FEEF428A14F362E729D495A3C4F8 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D2ACE5E7205F05EF02E2AFB49840281 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				099AC07AB5F88C8101B52BBC1B876C46 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		85C8CCEE24E7A34C156B6A9EADB60682 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				36D613CDE8BF8DAD84C834B120FAAC6D /* DKImagePickerController-DKImagePickerController in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		88A217B1E3B043801E34972BA36C7F9D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				4C475CB455ED065B025E9EE589BD1D67 /* SwiftyGif-SwiftyGif in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9A0EBC15F9BB9C14A63BB1FBF4A626F7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C7C3888445662185F83D2103ED93C1DB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				EB902F7BC26360A2E2AC0D63C776DC1E /* Base.lproj in Resources */,
				0E37E8E9CD8245FB1993052B666A0EAE /* en.lproj in Resources */,
				D2BD71914A7AE0AF1A511969BF0440ED /* Images.xcassets in Resources */,
				3151AED946F9E3E04317FDFA441B37BE /* PrivacyInfo.xcprivacy in Resources */,
				EE51D9C7F8F2DF2BB9EDA2E308F9D751 /* zh-Hans.lproj in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CA715BC1F8D4057E14C1239E1389CC74 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				81E5028FDB97C8B59FCD935F7FB569E8 /* SDWebImage-SDWebImage in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D412229AEE091A9D36A8553E8AA56B31 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				14F8DD4CD78B04D048D64E6B0C8F4D0B /* path_provider_foundation-path_provider_foundation_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		001CCB0FEA80CD1C6BF534C6D9C6283A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				B375EA128E6579366091BAA390BBDD34 /* Pods-RunnerTests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		05895E3E1AC56002880CEF9A9AC4086F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				92D06CE697A81B385ED6CD55784CB620 /* messages.g.swift in Sources */,
				E7798C39E72D9BEA2B31DD6A0E00CFEB /* path_provider_foundation-dummy.m in Sources */,
				8C47508E707EAC61A796BC803D08EB26 /* PathProviderPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		14DDA23916A9CAE112ED760F9A90C586 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				5B326344BE346F974BC65B100AD9FBA1 /* file_picker-dummy.m in Sources */,
				965825CD5671D76D064FC44AA7DC307F /* FileInfo.m in Sources */,
				0ECBB230C0F9E389D500B568892A8342 /* FilePickerPlugin.m in Sources */,
				C075194826F8FEA0DDCED4FA97BFAF61 /* FileUtils.m in Sources */,
				FE1E8F08A2FFEDAE06C61226A5B24BD7 /* ImageUtils.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		225BE4B33B1425E7B3EEF140C19FCA66 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F15CE987444233246CB3AFC495B8F340 /* Pods-Runner-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		27EA7BE09F46F020DFD119C42E71FA73 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3EF510EE93084E03DA3845A62038C53F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8856919872BF7B18BF09701D2F7CFF70 /* NSImage+SwiftyGif.swift in Sources */,
				86B487591AB6C88FFF205591E0983E21 /* NSImageView+SwiftyGif.swift in Sources */,
				30002454870A7768082422F3D005AD55 /* ObjcAssociatedWeakObject.swift in Sources */,
				FAA8407C7F9306A714DB8A7A36D9B42E /* SwiftyGif-dummy.m in Sources */,
				FB87E154938D16236DBAD6896BA0579F /* SwiftyGifManager.swift in Sources */,
				8356FF5D3991FCED071276462C7C9FD4 /* UIImage+SwiftyGif.swift in Sources */,
				B2AB5F342E163C9A02F2A744F9E51D25 /* UIImageView+SwiftyGif.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6E4F7D36CE926A14A3750240BDCEBBC7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C5443771FC094727E056A601E83BD73 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8434EE1C8FB505926DB4AE80FE591F06 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				E0565EBFF313BEC5F5B6F879A7C9AE1F /* DKPDFView.swift in Sources */,
				0BB41CF66B36C589B187A619D5EE8716 /* DKPhotoBaseImagePreviewVC.swift in Sources */,
				76F12104EE23A5C0EEA1B40525368A10 /* DKPhotoBasePreviewVC.swift in Sources */,
				A835CFE15179C71BBCE2799792214E71 /* DKPhotoContentAnimationView.swift in Sources */,
				6FB12AA44E086D44C20D7EBF67D45642 /* DKPhotoGallery.swift in Sources */,
				9252D8EDCFB679F837A682021E943126 /* DKPhotoGallery-dummy.m in Sources */,
				D0EC45DD91E4A325BB5F359C9469E0E0 /* DKPhotoGalleryContentVC.swift in Sources */,
				D515A7E1E6CFA8483A15072786A8D43D /* DKPhotoGalleryInteractiveTransition.swift in Sources */,
				E46A03ED55E9AE3A3A19BE18ACE230BD /* DKPhotoGalleryItem.swift in Sources */,
				8923F7F3B70FE87328B5CF7A2B6D664F /* DKPhotoGalleryResource.swift in Sources */,
				3544ED679AF22F9558ED08A66F3C0C1C /* DKPhotoGalleryScrollView.swift in Sources */,
				8AC73E81EDD4DF17337EBE276AED2552 /* DKPhotoGalleryTransitionController.swift in Sources */,
				F2911D5F8704D0E009939E0F8301C05D /* DKPhotoGalleryTransitionDismiss.swift in Sources */,
				3FBDFBBE5F2D55C8C163772E879A63FC /* DKPhotoGalleryTransitionPresent.swift in Sources */,
				608658907B13F7FB09D7CE4E819AB230 /* DKPhotoImageDownloader.swift in Sources */,
				78672CC7A069804A99E6C16A6123FFD1 /* DKPhotoImagePreviewVC.swift in Sources */,
				0C770BDEB8B2C4EA09AB8700FBC7EEF4 /* DKPhotoImageUtility.swift in Sources */,
				3B44C7A8263FB4C7D635CCA0D6526901 /* DKPhotoImageView.swift in Sources */,
				0FB76DC5F9BBE8514F6A0B7E2DD7EADB /* DKPhotoIncrementalIndicator.swift in Sources */,
				7519B64A6A501181500A040F46E090BD /* DKPhotoPDFPreviewVC.swift in Sources */,
				219E7EAE58315F178DE5FA636F411113 /* DKPhotoPlayerPreviewVC.swift in Sources */,
				D8CCE06F26DBC8605B6334EA053C8C15 /* DKPhotoPreviewFactory.swift in Sources */,
				86BCBB024DBF6D4BEF294E1EE1A3BA73 /* DKPhotoProgressIndicator.swift in Sources */,
				6F9E1250A95526D8E23F9F8BC29D513C /* DKPhotoProgressIndicatorProtocol.swift in Sources */,
				43256922E1EC61FED22FB24F009B69B7 /* DKPhotoQRCodeResultVC.swift in Sources */,
				D058D5E13A137E3CCC36CA373030B2CB /* DKPhotoWebVC.swift in Sources */,
				80E5A48B0C6099699D040A7D22FF3DAC /* DKPlayerView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		85F611869208A646B549018BCB5A4528 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				338767F71B0307EF3F31F230E1E35658 /* sqflite_darwin-dummy.m in Sources */,
				318D194CABEF87A5657BD250DE8EB0CB /* SqfliteCursor.m in Sources */,
				75F07F1ACFC462BB6B8E8756F9F3A535 /* SqfliteDarwinDatabase.m in Sources */,
				6C5DA7CDF65678819796A1747D34C3C5 /* SqfliteDarwinDatabaseAdditions.m in Sources */,
				BF91D04769A71CC361475A164A76C339 /* SqfliteDarwinDatabaseQueue.m in Sources */,
				E7A410A2214B204F97DE766EF8C55E46 /* SqfliteDarwinResultSet.m in Sources */,
				F58F506D3681F42D07166B150C385539 /* SqfliteDatabase.m in Sources */,
				296F8A617ED41772C6271F5F4722EB4F /* SqfliteOperation.m in Sources */,
				79EB8980D8E28EA25CA288E6A0BC77E7 /* SqflitePlugin.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E5C97F86EC98B7C7F3F577A4A326FCC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				E3BB5A38C15DE1E77A178BFE2F37A9FE /* DKAsset.swift in Sources */,
				315E7B4EF554DA3DC6372BF65F077EB0 /* DKAsset+Export.swift in Sources */,
				76287F90002CFFA382123C28CC5244E2 /* DKAsset+Fetch.swift in Sources */,
				579F55EEFFADF96E8F936DBA91D26494 /* DKAssetGroup.swift in Sources */,
				70AFA2FF809336C31996F765B6E27ECB /* DKAssetGroupCellItemProtocol.swift in Sources */,
				CA4F19C7BE5762453A6E06571EACFBDE /* DKAssetGroupDetailBaseCell.swift in Sources */,
				B4ABE8192B2833C834180C0BEC4F70E1 /* DKAssetGroupDetailCameraCell.swift in Sources */,
				4F5BEC7214A0F38643B34E86192EFAD3 /* DKAssetGroupDetailImageCell.swift in Sources */,
				9FAD87CE8A3DBF726B3828948D8BCDFA /* DKAssetGroupDetailVC.swift in Sources */,
				BC2C6328EDE1D0DF60CAAAD876439F5A /* DKAssetGroupDetailVideoCell.swift in Sources */,
				973D3BE20482FA5B58D514168F6EB279 /* DKAssetGroupGridLayout.swift in Sources */,
				2D6937E8D7AADEFCA71B193ADBB99AA5 /* DKAssetGroupListVC.swift in Sources */,
				E18FE89C7EF64202F1DB6524F8A0EF46 /* DKImageAssetExporter.swift in Sources */,
				5EC1F06BAB7BF265CAFEFC30E0094B8A /* DKImageBaseManager.swift in Sources */,
				7F05966E7064A4D2E39B026A6FCFC112 /* DKImageDataManager.swift in Sources */,
				11EE828AE0664EA1EA51FB0A63FF8DA1 /* DKImageExtensionController.swift in Sources */,
				E500A469F4B7F4030AA7BDE00DE7F0CA /* DKImageExtensionGallery.swift in Sources */,
				6F5D88E42C8A2AD03FC90F03938EDE11 /* DKImageGroupDataManager.swift in Sources */,
				6DAC480A0F8D8839897DEB2540669240 /* DKImagePickerController.swift in Sources */,
				10A218FABBA3B58065077701DE0B6770 /* DKImagePickerController-dummy.m in Sources */,
				D32A7684739CBFAB19BC46BBF882F2D6 /* DKImagePickerControllerBaseUIDelegate.swift in Sources */,
				119E21840A276EDD5B1E5DE62D1BEB5C /* DKImagePickerControllerResource.swift in Sources */,
				D90A62E984B38C99F6FFD58FC0F874ED /* DKPermissionView.swift in Sources */,
				C3B82132FF6A981D9583B58FB3F987DC /* DKPopoverViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B89CB190BB5F8514C4798E33BFAE23FD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DC6106B7A314C7DC49C3BDCE4EF85380 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				CCE4753173C2E39ACA5E0DD3925AE25E /* NSBezierPath+SDRoundedCorners.m in Sources */,
				E735DF76708213075A78F196849B3C82 /* NSButton+WebCache.m in Sources */,
				9837731FBA98BDC9B1E887C2BAF9B046 /* NSData+ImageContentType.m in Sources */,
				BF3A2ED3CE6B849E3A51DC0CD4BFB3D3 /* NSImage+Compatibility.m in Sources */,
				2CD13FADE3A6DE41D016518A46E19588 /* SDAnimatedImage.m in Sources */,
				67E75C46BF8CF42A0DBEC70B80B8DDC0 /* SDAnimatedImagePlayer.m in Sources */,
				438B85BA7F8FDAE067BAC863A0274714 /* SDAnimatedImageRep.m in Sources */,
				BD7309FCDB34A4895520C6238A714E17 /* SDAnimatedImageView.m in Sources */,
				51993AF62016F6D66E193B5D92E000D7 /* SDAnimatedImageView+WebCache.m in Sources */,
				25290B066A075D5A18F1E9A5D5FEFAB8 /* SDAssociatedObject.m in Sources */,
				B4F8AA271D84DB5D965B45ABD760667D /* SDAsyncBlockOperation.m in Sources */,
				E85F172BF3897EF74D9A186C8A461C25 /* SDCallbackQueue.m in Sources */,
				21EAE8080DDCDE6C0567E9034DACFB83 /* SDDeviceHelper.m in Sources */,
				D33568D0387FBBDF795D22DCB3BF7684 /* SDDiskCache.m in Sources */,
				B94AA8D6D50641B4F326222C793D6E38 /* SDDisplayLink.m in Sources */,
				B3020B2F10E079F4EFB38C79F0D1C7B9 /* SDFileAttributeHelper.m in Sources */,
				ACE9C2359BBA68AE6BE49CCDC88148C9 /* SDGraphicsImageRenderer.m in Sources */,
				B7490E541F7092A16BED82284A554320 /* SDImageAPNGCoder.m in Sources */,
				D3184C912EC512CB297AC3C457679B8D /* SDImageAssetManager.m in Sources */,
				D2A1863914ADD3808E28EFEE36F9F745 /* SDImageAWebPCoder.m in Sources */,
				2D3E80ED15E72912790CB52C60C338E8 /* SDImageCache.m in Sources */,
				E5CE9457D9F99E7FF25847729CC14678 /* SDImageCacheConfig.m in Sources */,
				25784F0865063087BD75D3F88C8F56C4 /* SDImageCacheDefine.m in Sources */,
				AD05FDAF1737B527D7D7C6D90777504A /* SDImageCachesManager.m in Sources */,
				B23FF189A0A31D7463FE3A4A14F7D5E2 /* SDImageCachesManagerOperation.m in Sources */,
				4E11E426F54801885A8F52AD3629B7A2 /* SDImageCoder.m in Sources */,
				661A1CEBB05B09F3423053819286C61F /* SDImageCoderHelper.m in Sources */,
				886008242516899AE2C9A24616E7CF8B /* SDImageCodersManager.m in Sources */,
				E57C82BF0701E3B2BB07E153152AC9B8 /* SDImageFrame.m in Sources */,
				C311CB575B532E46D6AC6023D1A52B4E /* SDImageFramePool.m in Sources */,
				AB4762B65BD6BC39F1690568A5AC65C2 /* SDImageGIFCoder.m in Sources */,
				E38DF98EDD47E8857D58C7FB23A86913 /* SDImageGraphics.m in Sources */,
				0358550B94AADC665DF43A09A7D8D4D5 /* SDImageHEICCoder.m in Sources */,
				7FBECA30D19BD58A07D5F9A14B4001BC /* SDImageIOAnimatedCoder.m in Sources */,
				64DC61DF7C573EC98DCBD205EC82B5A0 /* SDImageIOCoder.m in Sources */,
				764BA112A87BF3E4305289B919657613 /* SDImageLoader.m in Sources */,
				DA550F4AA20F9275348FC460ECFB072A /* SDImageLoadersManager.m in Sources */,
				D3BBCFFB4D27E74DB0DEEF2420881EA9 /* SDImageTransformer.m in Sources */,
				2C35AC8AE8EE7D71F86D9607A9E1B52F /* SDInternalMacros.m in Sources */,
				1330A87C24E8C5FA52A26AA3D88CDE05 /* SDMemoryCache.m in Sources */,
				85A74687A36FA9AC42D5879F7B6DC77A /* SDWeakProxy.m in Sources */,
				E317425280058F109FA44E0510E87A60 /* SDWebImage-dummy.m in Sources */,
				00CC8622F9CECEE6738C6EBA224F3AFD /* SDWebImageCacheKeyFilter.m in Sources */,
				5A87A01E01BADD80B7F787211FE62CFF /* SDWebImageCacheSerializer.m in Sources */,
				******************************** /* SDWebImageCompat.m in Sources */,
				3DD24B476D326068A41603316B5828AD /* SDWebImageDefine.m in Sources */,
				E22F4AB0D410A011F3A06E2261BC3A31 /* SDWebImageDownloader.m in Sources */,
				7CF1A21255474FAB679F692CE4017727 /* SDWebImageDownloaderConfig.m in Sources */,
				58DBA636318A2DE38E4B05E6F40FB6CC /* SDWebImageDownloaderDecryptor.m in Sources */,
				1068E74C6249E12C8ABF9099D9C91FCF /* SDWebImageDownloaderOperation.m in Sources */,
				0B890FA352D8E20B899DCD8926ACB25D /* SDWebImageDownloaderRequestModifier.m in Sources */,
				5DC05FFD99CCBC2B34BCDB0BD4531F41 /* SDWebImageDownloaderResponseModifier.m in Sources */,
				536C4F61524406AA282F819D1D45EBBD /* SDWebImageError.m in Sources */,
				20302FF56E778E318E04E911C6F0168D /* SDWebImageIndicator.m in Sources */,
				8B988774AF987BA232BC8528324A4385 /* SDWebImageManager.m in Sources */,
				4AFE10FC1E2D073EA45F55E86C38CDA5 /* SDWebImageOperation.m in Sources */,
				64FE0A9CD95376F0A12EDB8231D76DC7 /* SDWebImageOptionsProcessor.m in Sources */,
				F9A533EE7A611BAF853870B79F81D344 /* SDWebImagePrefetcher.m in Sources */,
				C5D3E4F2E67D93DF0C9725894ACF986C /* SDWebImageTransition.m in Sources */,
				E6E723CBE2432A2CF436CFCD59F4ADE0 /* UIButton+WebCache.m in Sources */,
				8EBD628337E5C63FCF3DB19BDAC50309 /* UIColor+SDHexString.m in Sources */,
				DFBEDDCEDCF84BE53B269597F5BC2AAC /* UIImage+ExtendedCacheData.m in Sources */,
				8485C9AA039D08009FFC0967D73DC168 /* UIImage+ForceDecode.m in Sources */,
				E1FAE945E39BF2D458779D3E3397329D /* UIImage+GIF.m in Sources */,
				7ACCFEA085D709C92408802375005F5D /* UIImage+MemoryCacheCost.m in Sources */,
				0DEA96F80583EDB0B1A439B79C14F29F /* UIImage+Metadata.m in Sources */,
				87754ED42DFC97D0E2B7361A67F8D054 /* UIImage+MultiFormat.m in Sources */,
				190694EC8099CAC8B71049AF2C126F21 /* UIImage+Transform.m in Sources */,
				1A3C83553E599473DCEE49142595AD6A /* UIImageView+HighlightedWebCache.m in Sources */,
				87C16A172BC1F27B82C0E2E24555C9F8 /* UIImageView+WebCache.m in Sources */,
				24072965FA936091E8C49752F5202E5F /* UIView+WebCache.m in Sources */,
				54BE9077826608AE9BAB99E4266B57CC /* UIView+WebCacheOperation.m in Sources */,
				744D4F5B29895D716CD82020B26F01CF /* UIView+WebCacheState.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E0E574C3BE85C6B2D599767072DB2F89 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E268C29F7DC9A21904E2FB6696FA2B4E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		2694495DA747AFBF6526C7B86103CD51 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = DKPhotoGallery;
			target = 7328189F8C34132A51A6532A51BBBC06 /* DKPhotoGallery */;
			targetProxy = ACD4D7FECE83BB14C9ACD58ACEF1119A /* PBXContainerItemProxy */;
		};
		3D7ED30C649EDFB0C07271C750A2C4C7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SDWebImage;
			target = 3847153A6E5EEFB86565BA840768F429 /* SDWebImage */;
			targetProxy = 02F46ED09627EEEA3831CF8E44C93CE1 /* PBXContainerItemProxy */;
		};
		3EFA8B8B9507FC320BBF2E973429562C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "path_provider_foundation-path_provider_foundation_privacy";
			target = CACE6618E7996464E38687E13F67D945 /* path_provider_foundation-path_provider_foundation_privacy */;
			targetProxy = 3DF5D4E35AFAC83AF4C54C91376B67FD /* PBXContainerItemProxy */;
		};
		41A4C9FC98019B71B314B2AB50D84BD6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "sqflite_darwin-sqflite_darwin_privacy";
			target = 9F5CAB8B25787EBAAFCC237AEDC74E2D /* sqflite_darwin-sqflite_darwin_privacy */;
			targetProxy = 199155410D8FA21F3D4D84A1EAFE1B1E /* PBXContainerItemProxy */;
		};
		45B2EE17B38B6E417ADA530FBEBBD363 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = DKPhotoGallery;
			target = 7328189F8C34132A51A6532A51BBBC06 /* DKPhotoGallery */;
			targetProxy = E72C1EF213EB149AA874E04CCC3B6FF5 /* PBXContainerItemProxy */;
		};
		46AF3364CF2278B505DB1AF17E78A298 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = path_provider_foundation;
			target = 56F581DDCB0A032454E604885E17AE3C /* path_provider_foundation */;
			targetProxy = 38448869EC5CF02029C640C31718F661 /* PBXContainerItemProxy */;
		};
		46EFD74D2B78A930DB1C6739741D8836 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Pods-Runner";
			target = 8B74B458B450D74B75744B87BD747314 /* Pods-Runner */;
			targetProxy = C4C068B61CE62C2C9C8178F63A7F1199 /* PBXContainerItemProxy */;
		};
		73BECA96F8C4001271F8782E20499183 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SDWebImage;
			target = 3847153A6E5EEFB86565BA840768F429 /* SDWebImage */;
			targetProxy = D1ED40538B4ED6C3EEAB9C21FA9F37CB /* PBXContainerItemProxy */;
		};
		8422A1F84B10C0E8407B5BC625CC448A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "SDWebImage-SDWebImage";
			target = 94CFBA7D633ECA58DF85C327B035E6A3 /* SDWebImage-SDWebImage */;
			targetProxy = EEEC926AAF76B996B89A3ACFEEBB06BC /* PBXContainerItemProxy */;
		};
		863203EF9DF7028B5D0D72E74FA648F2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = DKImagePickerController;
			target = A4BE7D9267EB551461B2837139E4A9D6 /* DKImagePickerController */;
			targetProxy = D149441C6300B3F4269A2AA6D7D579B1 /* PBXContainerItemProxy */;
		};
		87975D4C4563F04C323EC6166C3441E5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SwiftyGif;
			target = 15AEBAFA43912F898FC2E1A73025C946 /* SwiftyGif */;
			targetProxy = 8F12AF90F106C7EAF82E0A39D800CE2E /* PBXContainerItemProxy */;
		};
		933FA60DC42604B67EEB485C6102DF6E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "SwiftyGif-SwiftyGif";
			target = 176E48377247D5011E906CB1A39E71BA /* SwiftyGif-SwiftyGif */;
			targetProxy = B3E9233E13B5CC96691AC50ECD81AED2 /* PBXContainerItemProxy */;
		};
		959A791025C71E074DDD4F733F2EE1F4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = B2C043E84CC050918C6666EA60B96A65 /* PBXContainerItemProxy */;
		};
		9DE0C3F149CDA39C61ECEAA3203D80F6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = sqflite_darwin;
			target = 6178324C15B5DCE31C127429F0C8EE8F /* sqflite_darwin */;
			targetProxy = 6705DECE40C2FF2559590A45B710C657 /* PBXContainerItemProxy */;
		};
		A2B667CCB16B2F0EA7BC78D29A148FB4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "DKImagePickerController-DKImagePickerController";
			target = 6244DB400F69AD751FA848C697E64C56 /* DKImagePickerController-DKImagePickerController */;
			targetProxy = 0D6F83386095938A7856CC464F0C9BD6 /* PBXContainerItemProxy */;
		};
		B33BAEC7A29A8EB56F96FBA37B166608 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = DKImagePickerController;
			target = A4BE7D9267EB551461B2837139E4A9D6 /* DKImagePickerController */;
			targetProxy = 5A76A97DAA1D340B2A9DD175B50EB972 /* PBXContainerItemProxy */;
		};
		B6D688C4E03E4E16FDB7B48AD6D03A83 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SwiftyGif;
			target = 15AEBAFA43912F898FC2E1A73025C946 /* SwiftyGif */;
			targetProxy = A20673A6CFCF63F865282BB91A835FB6 /* PBXContainerItemProxy */;
		};
		C2AB6247883DD976FE5BCBD9C81F9B71 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 7DE8AA217AF75A3F228BA0888FFE04E5 /* PBXContainerItemProxy */;
		};
		D64D9BC698A91F26A5F83B76F0E7743B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "DKPhotoGallery-DKPhotoGallery";
			target = 76ECBFA16F19926BEDC92324235D3BE7 /* DKPhotoGallery-DKPhotoGallery */;
			targetProxy = D6EBD15BD3E355837C2E0519D5F117AD /* PBXContainerItemProxy */;
		};
		DB02C81205BFF4A09B1FECFF8AC36450 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 41FD029F7B9AEF28B08D8A147EC1C2C1 /* PBXContainerItemProxy */;
		};
		E841EFCA53FD9865FAD4D5125B73DBE6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = file_picker;
			target = A47785EC1549E51E61B1C4CB35A8EF3A /* file_picker */;
			targetProxy = F46900BE8A2627E8C84C9438B20914AC /* PBXContainerItemProxy */;
		};
		FD9F7329DC5D779ADDFD9B37D8018E7A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 59B23514118AACB9825FE4252C318E9A /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0316CA68318A621B67362408814959DE /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BF0F881597059D9535AB60F9F91D7677 /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/path_provider_foundation/path_provider_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = path_provider_foundation;
				PRODUCT_NAME = path_provider_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		07CC20E35EA1ADA2C80DB6446E94E356 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C50FD10CD19FF6999637BA5ECAF1681B /* Pods-Runner.profile.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		125CC82BA04753E0C6FF7F86C3A10D81 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B703785507B22BAF0B800737429577EC /* Pods-Runner.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		162FDF3A324F3E4E46A4AE1A1AA123BF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BF0F881597059D9535AB60F9F91D7677 /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/path_provider_foundation/path_provider_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = path_provider_foundation;
				PRODUCT_NAME = path_provider_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		1FDB383208B70F9609E29C0E0B74F9BA /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4B0F8B496CBA607D4401BBF1ED8F34DB /* DKPhotoGallery.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKPhotoGallery";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = DKPhotoGallery;
				INFOPLIST_FILE = "Target Support Files/DKPhotoGallery/ResourceBundle-DKPhotoGallery-DKPhotoGallery-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = DKPhotoGallery;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		23DBC39CF4940B5F895BDE8C353AD6ED /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 30F15D573E77ADBAB56D2DE6F61C0717 /* sqflite_darwin.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/sqflite_darwin";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = sqflite_darwin;
				INFOPLIST_FILE = "Target Support Files/sqflite_darwin/ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = sqflite_darwin_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		25C30DFA7D6F466B7CA1D1B8C1F3C3D9 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B281E855782C63501215FBC637F8DC9B /* SDWebImage.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SDWebImage/SDWebImage-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SDWebImage/SDWebImage-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SDWebImage/SDWebImage.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_MODULE_NAME = SDWebImage;
				PRODUCT_NAME = SDWebImage;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		279FC69031AF13CBBBE09063D8C04B9F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AC66E20519F07E1D618916DC51EBBF48 /* DKPhotoGallery.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap";
				PRODUCT_MODULE_NAME = DKPhotoGallery;
				PRODUCT_NAME = DKPhotoGallery;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		2965280C5AFD8871A2402C716CE94565 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AC66E20519F07E1D618916DC51EBBF48 /* DKPhotoGallery.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKPhotoGallery";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = DKPhotoGallery;
				INFOPLIST_FILE = "Target Support Files/DKPhotoGallery/ResourceBundle-DKPhotoGallery-DKPhotoGallery-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = DKPhotoGallery;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		2B9E26EAE2CD392AD762421F663075A1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		2E4608C055BE4F4E675FECAEBC4CC2A4 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AC8FE152530FBDCE460186B326C1A8FB /* Pods-RunnerTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		36AAAD1F431BC9DE7072FDF81AD31F96 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 95956B095C04FF537194DCA28F66FF38 /* DKImagePickerController.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap";
				PRODUCT_MODULE_NAME = DKImagePickerController;
				PRODUCT_NAME = DKImagePickerController;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		37EEF86F6DC5C8B9B50F8965DE749506 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BF0F881597059D9535AB60F9F91D7677 /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/path_provider_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = path_provider_foundation;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = path_provider_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		39DD04A85064F5A014919489A4D0DBFC /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F44CF38DD4A526208425E4E6A8E2BDCE /* path_provider_foundation.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/path_provider_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = path_provider_foundation;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = path_provider_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		3B1E73ADD0238142131083C61B31114A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B281E855782C63501215FBC637F8DC9B /* SDWebImage.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SDWebImage";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = SDWebImage;
				INFOPLIST_FILE = "Target Support Files/SDWebImage/ResourceBundle-SDWebImage-SDWebImage-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = SDWebImage;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		49576D0EB8E2D44214B92672291C392C /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 81C7F9DBD43A2C5DE5FFC88258C80F6D /* SwiftyGif.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SwiftyGif/SwiftyGif-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SwiftyGif/SwiftyGif.modulemap";
				PRODUCT_MODULE_NAME = SwiftyGif;
				PRODUCT_NAME = SwiftyGif;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		4C4FA66F27DB9ED633BA3E903EA4D324 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BF0F881597059D9535AB60F9F91D7677 /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/path_provider_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = path_provider_foundation;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = path_provider_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		5451603A499CCBDDDEDF960EFDE16228 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E754497AF84F2E36D088C1EB20390ABB /* SwiftyGif.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SwiftyGif/SwiftyGif-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SwiftyGif/SwiftyGif.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_MODULE_NAME = SwiftyGif;
				PRODUCT_NAME = SwiftyGif;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		5597FAF856114ECBAA693AAD057A8D32 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 95956B095C04FF537194DCA28F66FF38 /* DKImagePickerController.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = DKImagePickerController;
				INFOPLIST_FILE = "Target Support Files/DKImagePickerController/ResourceBundle-DKImagePickerController-DKImagePickerController-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = DKImagePickerController;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		590EF58CEFD38E2B2AEF344CD9B78362 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3FF59488A08EFCA4467233D767353363 /* SDWebImage.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SDWebImage/SDWebImage-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SDWebImage/SDWebImage-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SDWebImage/SDWebImage.modulemap";
				PRODUCT_MODULE_NAME = SDWebImage;
				PRODUCT_NAME = SDWebImage;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		5AF1AC88E5F655C93D1E3B5012F6FDC8 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_PROFILE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Profile;
		};
		63FAF33E1C55B71A5F5A8B3CC8749F99 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		6A760541CBDB99B13D4E2FA039FBBDD8 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4B0F8B496CBA607D4401BBF1ED8F34DB /* DKPhotoGallery.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_MODULE_NAME = DKPhotoGallery;
				PRODUCT_NAME = DKPhotoGallery;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		6D2BF51933E25FBF5448F3360D8101B1 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 81C7F9DBD43A2C5DE5FFC88258C80F6D /* SwiftyGif.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SwiftyGif/SwiftyGif-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SwiftyGif/SwiftyGif.modulemap";
				PRODUCT_MODULE_NAME = SwiftyGif;
				PRODUCT_NAME = SwiftyGif;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		6E51CC3F72E4D37E7877F30063A73BFE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 02DAA13E6CB0364A1D6EBC7A26C8BC22 /* file_picker.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/file_picker/file_picker-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/file_picker/file_picker-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/file_picker/file_picker.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = file_picker;
				PRODUCT_NAME = file_picker;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		70F1E3D053D812E3D7E8E74934CC9053 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 455D12245BE072B1B8B2EB98F539B8DB /* Flutter.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				ONLY_ACTIVE_ARCH = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		76E053E47C7357CFF4EA96DF8337031F /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0887384FF3482C3F4C998D0C0F2D7C94 /* Flutter.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		7A011717A03E07E2CF4806BA82A3176F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CB507F3628C2744ADBD6115BD432C868 /* file_picker.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/file_picker/file_picker-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/file_picker/file_picker-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/file_picker/file_picker.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = file_picker;
				PRODUCT_NAME = file_picker;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		80F56863016940777A8ADFB51F222F34 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E754497AF84F2E36D088C1EB20390ABB /* SwiftyGif.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SwiftyGif";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = SwiftyGif;
				INFOPLIST_FILE = "Target Support Files/SwiftyGif/ResourceBundle-SwiftyGif-SwiftyGif-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = SwiftyGif;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		857BB3D5EB9F8B8635F38EE59B6FCA5D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 03E8AD2E8B75443E12D3D3BD12A9900E /* Pods-RunnerTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		86C5BBE7CBBA66350B4E55FDF50CE26D /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E736E0C9C5B73D27B8865E363E2108AC /* Pods-RunnerTests.profile.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		8745F1117F0D4914948FEC5169A599E4 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AC66E20519F07E1D618916DC51EBBF48 /* DKPhotoGallery.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap";
				PRODUCT_MODULE_NAME = DKPhotoGallery;
				PRODUCT_NAME = DKPhotoGallery;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		8DB187F44CB861124A988725F467A7D4 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 02DAA13E6CB0364A1D6EBC7A26C8BC22 /* file_picker.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/file_picker/file_picker-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/file_picker/file_picker-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/file_picker/file_picker.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = file_picker;
				PRODUCT_NAME = file_picker;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		98700F49EF6BB5FED287C5A443923199 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 05351FE7C3B96314E9BCD9B4BF6EE6D5 /* sqflite_darwin.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = sqflite_darwin;
				PRODUCT_NAME = sqflite_darwin;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		A0AE79133130A17B42B6BC76D2FC2178 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3FF59488A08EFCA4467233D767353363 /* SDWebImage.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SDWebImage";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = SDWebImage;
				INFOPLIST_FILE = "Target Support Files/SDWebImage/ResourceBundle-SDWebImage-SDWebImage-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = SDWebImage;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		A1202C59D5272B0DEE585943D867C807 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F44CF38DD4A526208425E4E6A8E2BDCE /* path_provider_foundation.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/path_provider_foundation/path_provider_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = path_provider_foundation;
				PRODUCT_NAME = path_provider_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		A3A20EA1666E9E4AD4ECD4EBFEC737A0 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 30F15D573E77ADBAB56D2DE6F61C0717 /* sqflite_darwin.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/sqflite_darwin";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = sqflite_darwin;
				INFOPLIST_FILE = "Target Support Files/sqflite_darwin/ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = sqflite_darwin_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		A8013ACCC7D77B433C87FCBEC40694B0 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 30F15D573E77ADBAB56D2DE6F61C0717 /* sqflite_darwin.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = sqflite_darwin;
				PRODUCT_NAME = sqflite_darwin;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		A9EF7E9D851131AF26CA4A908A637544 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 95956B095C04FF537194DCA28F66FF38 /* DKImagePickerController.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = DKImagePickerController;
				INFOPLIST_FILE = "Target Support Files/DKImagePickerController/ResourceBundle-DKImagePickerController-DKImagePickerController-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = DKImagePickerController;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		AE165606452B0DBF421A716E2599DE2F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 728A5ED1DD60BAC8C8F382A87B976F84 /* Pods-Runner.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		B500919224A678A1B65F646DA3616007 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 81C7F9DBD43A2C5DE5FFC88258C80F6D /* SwiftyGif.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SwiftyGif";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = SwiftyGif;
				INFOPLIST_FILE = "Target Support Files/SwiftyGif/ResourceBundle-SwiftyGif-SwiftyGif-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = SwiftyGif;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		B6C0FF2A4F95C6CBD6EDCD628F167188 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AC66E20519F07E1D618916DC51EBBF48 /* DKPhotoGallery.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKPhotoGallery";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = DKPhotoGallery;
				INFOPLIST_FILE = "Target Support Files/DKPhotoGallery/ResourceBundle-DKPhotoGallery-DKPhotoGallery-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = DKPhotoGallery;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		B819EC62A9CC390294AC64F9BD66CFA3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 05351FE7C3B96314E9BCD9B4BF6EE6D5 /* sqflite_darwin.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/sqflite_darwin";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = sqflite_darwin;
				INFOPLIST_FILE = "Target Support Files/sqflite_darwin/ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = sqflite_darwin_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		BF9264086606A0AD2C97FEDAED104EE5 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3FF59488A08EFCA4467233D767353363 /* SDWebImage.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SDWebImage/SDWebImage-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SDWebImage/SDWebImage-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SDWebImage/SDWebImage.modulemap";
				PRODUCT_MODULE_NAME = SDWebImage;
				PRODUCT_NAME = SDWebImage;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		C00240829FD7F4A551C6C0FA808A1AB3 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3FF59488A08EFCA4467233D767353363 /* SDWebImage.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SDWebImage";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = SDWebImage;
				INFOPLIST_FILE = "Target Support Files/SDWebImage/ResourceBundle-SDWebImage-SDWebImage-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = SDWebImage;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		C3A1FE9B0AA238CCB03B6693330C14DE /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6B9319B2649D7165C1BE2597DF519277 /* DKImagePickerController.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_MODULE_NAME = DKImagePickerController;
				PRODUCT_NAME = DKImagePickerController;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		E823BC704A0407B2804B0ADB89FABE7D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6B9319B2649D7165C1BE2597DF519277 /* DKImagePickerController.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = DKImagePickerController;
				INFOPLIST_FILE = "Target Support Files/DKImagePickerController/ResourceBundle-DKImagePickerController-DKImagePickerController-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = DKImagePickerController;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		EC8417FA0A05549EEAC0DF7B359A1CE9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 81C7F9DBD43A2C5DE5FFC88258C80F6D /* SwiftyGif.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SwiftyGif";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = SwiftyGif;
				INFOPLIST_FILE = "Target Support Files/SwiftyGif/ResourceBundle-SwiftyGif-SwiftyGif-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = SwiftyGif;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		F20C9EB39AC6CF00828BDCC10294E6A8 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 30F15D573E77ADBAB56D2DE6F61C0717 /* sqflite_darwin.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/Users/<USER>/Documents/source/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = sqflite_darwin;
				PRODUCT_NAME = sqflite_darwin;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		F85A665A341B16FDF6766B1EAA3FC5BD /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 95956B095C04FF537194DCA28F66FF38 /* DKImagePickerController.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap";
				PRODUCT_MODULE_NAME = DKImagePickerController;
				PRODUCT_NAME = DKImagePickerController;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		FB4B781D3E4B2E85C6448C7B77E40A53 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0887384FF3482C3F4C998D0C0F2D7C94 /* Flutter.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2DD09EF9AB60A917430373768FD40F6F /* Build configuration list for PBXNativeTarget "DKPhotoGallery-DKPhotoGallery" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1FDB383208B70F9609E29C0E0B74F9BA /* Debug */,
				2965280C5AFD8871A2402C716CE94565 /* Profile */,
				B6C0FF2A4F95C6CBD6EDCD628F167188 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		324AC0D6E6195CEFE4F4D9BC70B22A01 /* Build configuration list for PBXNativeTarget "file_picker" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7A011717A03E07E2CF4806BA82A3176F /* Debug */,
				8DB187F44CB861124A988725F467A7D4 /* Profile */,
				6E51CC3F72E4D37E7877F30063A73BFE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		329CA53F21C02C8ECD7D839FF41AB425 /* Build configuration list for PBXNativeTarget "DKImagePickerController-DKImagePickerController" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E823BC704A0407B2804B0ADB89FABE7D /* Debug */,
				A9EF7E9D851131AF26CA4A908A637544 /* Profile */,
				5597FAF856114ECBAA693AAD057A8D32 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		47C73998E49BB745A58B8F4B1CE770D2 /* Build configuration list for PBXNativeTarget "SDWebImage-SDWebImage" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3B1E73ADD0238142131083C61B31114A /* Debug */,
				C00240829FD7F4A551C6C0FA808A1AB3 /* Profile */,
				A0AE79133130A17B42B6BC76D2FC2178 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2B9E26EAE2CD392AD762421F663075A1 /* Debug */,
				5AF1AC88E5F655C93D1E3B5012F6FDC8 /* Profile */,
				63FAF33E1C55B71A5F5A8B3CC8749F99 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6707F929D05408792FD34CC7EC435BD0 /* Build configuration list for PBXNativeTarget "DKPhotoGallery" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6A760541CBDB99B13D4E2FA039FBBDD8 /* Debug */,
				8745F1117F0D4914948FEC5169A599E4 /* Profile */,
				279FC69031AF13CBBBE09063D8C04B9F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6EE4C99DE626F72DDA97CFB5811256BB /* Build configuration list for PBXNativeTarget "Pods-Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AE165606452B0DBF421A716E2599DE2F /* Debug */,
				07CC20E35EA1ADA2C80DB6446E94E356 /* Profile */,
				125CC82BA04753E0C6FF7F86C3A10D81 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		818723CB51B1DF95B98A3D66391E2239 /* Build configuration list for PBXNativeTarget "DKImagePickerController" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C3A1FE9B0AA238CCB03B6693330C14DE /* Debug */,
				F85A665A341B16FDF6766B1EAA3FC5BD /* Profile */,
				36AAAD1F431BC9DE7072FDF81AD31F96 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83EEBFCF2734736C883614ABD96B2C4D /* Build configuration list for PBXNativeTarget "SwiftyGif" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5451603A499CCBDDDEDF960EFDE16228 /* Debug */,
				49576D0EB8E2D44214B92672291C392C /* Profile */,
				6D2BF51933E25FBF5448F3360D8101B1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		84AAE47AFE07B5E560E9FE58ABDEEEDA /* Build configuration list for PBXNativeTarget "sqflite_darwin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				98700F49EF6BB5FED287C5A443923199 /* Debug */,
				F20C9EB39AC6CF00828BDCC10294E6A8 /* Profile */,
				A8013ACCC7D77B433C87FCBEC40694B0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8CCB4203D2C337DE5C06A7A3BDCCB375 /* Build configuration list for PBXNativeTarget "SDWebImage" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				25C30DFA7D6F466B7CA1D1B8C1F3C3D9 /* Debug */,
				BF9264086606A0AD2C97FEDAED104EE5 /* Profile */,
				590EF58CEFD38E2B2AEF344CD9B78362 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8EB075CB7C93C9D5AB6636A2D3EBADB2 /* Build configuration list for PBXNativeTarget "SwiftyGif-SwiftyGif" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				80F56863016940777A8ADFB51F222F34 /* Debug */,
				EC8417FA0A05549EEAC0DF7B359A1CE9 /* Profile */,
				B500919224A678A1B65F646DA3616007 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		94078A06C17E946ADC1F2C06726219E5 /* Build configuration list for PBXAggregateTarget "Flutter" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				70F1E3D053D812E3D7E8E74934CC9053 /* Debug */,
				76E053E47C7357CFF4EA96DF8337031F /* Profile */,
				FB4B781D3E4B2E85C6448C7B77E40A53 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D52099AA1537D0F3745166E1889F6CA3 /* Build configuration list for PBXNativeTarget "Pods-RunnerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				857BB3D5EB9F8B8635F38EE59B6FCA5D /* Debug */,
				86C5BBE7CBBA66350B4E55FDF50CE26D /* Profile */,
				2E4608C055BE4F4E675FECAEBC4CC2A4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		ED6CCBED66B7688A94EA7CA8E476F678 /* Build configuration list for PBXNativeTarget "sqflite_darwin-sqflite_darwin_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B819EC62A9CC390294AC64F9BD66CFA3 /* Debug */,
				23DBC39CF4940B5F895BDE8C353AD6ED /* Profile */,
				A3A20EA1666E9E4AD4ECD4EBFEC737A0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F66FDD50CC63E6B632FFB742A90739E9 /* Build configuration list for PBXNativeTarget "path_provider_foundation-path_provider_foundation_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				39DD04A85064F5A014919489A4D0DBFC /* Debug */,
				4C4FA66F27DB9ED633BA3E903EA4D324 /* Profile */,
				37EEF86F6DC5C8B9B50F8965DE749506 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FC3EA6EE526A2F7266A44B11E3A1AD9A /* Build configuration list for PBXNativeTarget "path_provider_foundation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1202C59D5272B0DEE585943D867C807 /* Debug */,
				0316CA68318A621B67362408814959DE /* Profile */,
				162FDF3A324F3E4E46A4AE1A1AA123BF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
