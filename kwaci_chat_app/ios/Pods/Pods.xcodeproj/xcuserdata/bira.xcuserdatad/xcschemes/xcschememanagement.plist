<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>DKImagePickerController-DKImagePickerController.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>DKImagePickerController.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>DKPhotoGallery-DKPhotoGallery.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>DKPhotoGallery.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Flutter.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-Runner.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-RunnerTests.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SDWebImage-SDWebImage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SDWebImage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SwiftyGif-SwiftyGif.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SwiftyGif.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>file_picker.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>path_provider_foundation-path_provider_foundation_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>path_provider_foundation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>sqflite_darwin-sqflite_darwin_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>sqflite_darwin.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
	</dict>
	<key>SuppressBuildableAutocreation</key>
	<dict/>
</dict>
</plist>
