# Authentication Setup Guide

This guide explains how to set up the complete authentication flow that has been implemented in the Kwaci Chat app.

## Overview

The authentication system includes:
- **Splash Screen**: Checks authentication status on app startup
- **Introduction Page**: Onboarding for new users
- **Login Page**: Email/password authentication
- **Register Page**: User registration with validation
- **Route Protection**: Authenticated access to main app features
- **Session Management**: Persistent authentication via Supabase

## Architecture

The implementation follows Clean Architecture principles:

### Domain Layer
- `User` entity
- `AuthRepository` interface
- Use cases: `SignIn`, `SignUp`, `SignOut`, `GetCurrentUser`, `CheckAuthStatus`

### Data Layer
- `UserModel` with JSON serialization
- `AuthApiService` for Supabase integration
- `AuthRepositoryImpl` implementing the repository interface

### Presentation Layer
- `AuthState` classes for state management
- `AuthNotifier` with Riverpod StateNotifier
- Auth UI pages and route protection

## Setup Instructions

### 1. Supabase Configuration

1. Create a Supabase project at [supabase.com](https://supabase.com)
2. Get your project URL and anon key from the project settings
3. Update the constants in `lib/core/constants/app_constants.dart`:

```dart
// Supabase Configuration
static const String supabaseUrl = 'YOUR_SUPABASE_URL';
static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
```

**Important**: Replace `YOUR_SUPABASE_URL` and `YOUR_SUPABASE_ANON_KEY` with your actual Supabase project credentials before running the app.

### 2. Supabase Database Setup

The authentication uses Supabase's built-in auth system. No additional database setup is required for basic authentication.

### 3. Dependencies

The following dependencies have been added:
- `supabase_flutter: ^2.5.6` - Supabase Flutter SDK

### 4. Navigation Flow

```
App Start → Splash Screen
    ↓
Check Auth Status
    ↓
┌─ Authenticated → Main App
└─ Not Authenticated → Introduction Page
    ↓
    ├─ Get Started → Register Page
    └─ Sign In → Login Page
        ↓
    Successful Auth → Main App
```

## Features

### Authentication Features
- ✅ Email/password registration
- ✅ Email/password login
- ✅ Form validation with error messages
- ✅ Loading states during auth operations
- ✅ Session persistence
- ✅ Automatic auth state management
- ✅ Route protection
- ✅ Error handling with user-friendly messages

### UI/UX Features
- ✅ Animated splash screen
- ✅ Clean onboarding experience
- ✅ Responsive form design
- ✅ Password visibility toggle
- ✅ Real-time form validation
- ✅ Toast notifications for errors
- ✅ Loading indicators

## Usage

### Sign Up Flow
1. User opens app → sees splash screen
2. If not authenticated → introduction page
3. Tap "Get Started" → register page
4. Fill email, password, confirm password
5. Tap "Create Account" → account created and logged in
6. Redirected to main app

### Sign In Flow
1. From introduction page → tap "Sign In"
2. Fill email and password
3. Tap "Sign In" → authenticated
4. Redirected to main app

### Session Management
- Authentication state is automatically persisted
- Users remain logged in across app restarts
- Auth state changes are handled automatically
- Logout functionality available in profile page

## Error Handling

The system handles various error scenarios:
- Invalid email format
- Weak passwords
- Email already exists
- Invalid credentials
- Network errors
- Server errors

## Testing

To test the authentication flow:
1. Set up your Supabase project
2. Update the configuration constants
3. Run the app
4. Test registration with a new email
5. Test login with existing credentials
6. Test error scenarios (invalid email, wrong password, etc.)

## Security

- Passwords are handled securely by Supabase
- No passwords are stored locally
- Session tokens are managed by Supabase SDK
- All auth operations use HTTPS

## Future Enhancements

Possible improvements:
- Social authentication (Google, Apple, etc.)
- Email verification
- Password reset functionality
- Multi-factor authentication
- Biometric authentication
- Remember me functionality
