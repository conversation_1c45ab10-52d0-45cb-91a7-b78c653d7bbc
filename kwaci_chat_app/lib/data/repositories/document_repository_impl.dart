import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../../domain/entities/document.dart';
import '../../domain/repositories/document_repository.dart';
import '../datasources/local/document_local_data_source.dart';
import '../datasources/remote/document_api_service.dart';
import '../models/document_model.dart';

class DocumentRepositoryImpl implements DocumentRepository {
  final DocumentApiService apiService;
  final DocumentLocalDataSource localDataSource;

  DocumentRepositoryImpl({
    required this.apiService,
    required this.localDataSource,
  });

  @override
  Future<Either<Failure, Document>> uploadDocument({
    required File file,
    String? title,
  }) async {
    try {
      final response = await apiService.uploadDocument(file, title);
      final document = response.document;
      
      // Cache the uploaded document
      await localDataSource.cacheDocument(document);
      
      return Right(document.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to upload document: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Document>>> getDocuments() async {
    try {
      final response = await apiService.getDocuments();
      final documents = response.documents;
      
      // Cache the documents
      await localDataSource.cacheDocuments(documents);
      
      return Right(documents.map((doc) => doc.toEntity()).toList());
    } on ServerException catch (e) {
      // Try to get cached documents if server fails
      try {
        final cachedDocuments = await localDataSource.getCachedDocuments();
        return Right(cachedDocuments.map((doc) => doc.toEntity()).toList());
      } catch (_) {
        return Left(ServerFailure(message: e.message, code: e.code));
      }
    } on NetworkException catch (e) {
      // Try to get cached documents if network fails
      try {
        final cachedDocuments = await localDataSource.getCachedDocuments();
        return Right(cachedDocuments.map((doc) => doc.toEntity()).toList());
      } catch (_) {
        return Left(NetworkFailure(message: e.message));
      }
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to get documents: $e'));
    }
  }

  @override
  Future<Either<Failure, Document>> getDocument(String documentId) async {
    try {
      final document = await apiService.getDocument(documentId);
      
      // Cache the document
      await localDataSource.cacheDocument(document);
      
      return Right(document.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to get document: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteDocument(String documentId) async {
    try {
      await apiService.deleteDocument(documentId);
      
      // Remove from cache
      await localDataSource.removeDocument(documentId);
      
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to delete document: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Document>>> getCachedDocuments() async {
    try {
      final cachedDocuments = await localDataSource.getCachedDocuments();
      return Right(cachedDocuments.map((doc) => doc.toEntity()).toList());
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to get cached documents: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> cacheDocuments(List<Document> documents) async {
    try {
      final documentModels = documents
          .map((doc) => DocumentModel.fromEntity(doc))
          .toList();
      await localDataSource.cacheDocuments(documentModels);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to cache documents: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearDocumentCache() async {
    try {
      await localDataSource.clearCache();
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to clear document cache: $e'));
    }
  }
}

// Provider for DocumentRepository
final documentRepositoryProvider = Provider<DocumentRepository>((ref) {
  final apiService = ref.read(documentApiServiceProvider);
  final localDataSource = ref.read(documentLocalDataSourceProvider);
  
  return DocumentRepositoryImpl(
    apiService: apiService,
    localDataSource: localDataSource,
  );
});
