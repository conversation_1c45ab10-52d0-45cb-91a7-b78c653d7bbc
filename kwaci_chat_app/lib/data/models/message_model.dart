import '../../domain/entities/message.dart';
import '../../core/utils/date_formatter.dart';

class MessageModel extends Message {
  const MessageModel({
    required super.id,
    required super.content,
    required super.isUser,
    required super.timestamp,
    super.documentReferences,
    super.citations,
    super.status,
    super.errorMessage,
  });

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    return MessageModel(
      id: json['id'] as String,
      content: json['content'] as String,
      isUser: json['is_user'] as bool,
      timestamp: DateFormatter.parseIsoDate(json['timestamp'] as String) ?? DateTime.now(),
      documentReferences: (json['document_references'] as List<dynamic>?)
          ?.map((ref) => ref as String)
          .toList() ?? [],
      citations: (json['citations'] as List<dynamic>?)
          ?.map((citation) => CitationModel.fromJson(citation as Map<String, dynamic>))
          .toList(),
      status: _parseStatus(json['status'] as String?),
      errorMessage: json['error_message'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'is_user': isUser,
      'timestamp': DateFormatter.toIsoString(timestamp),
      'document_references': documentReferences,
      'citations': citations?.map((citation) => (citation as CitationModel).toJson()).toList(),
      'status': _statusToString(status),
      'error_message': errorMessage,
    };
  }

  factory MessageModel.fromEntity(Message message) {
    return MessageModel(
      id: message.id,
      content: message.content,
      isUser: message.isUser,
      timestamp: message.timestamp,
      documentReferences: message.documentReferences,
      citations: message.citations?.map((citation) => CitationModel.fromEntity(citation)).toList(),
      status: message.status,
      errorMessage: message.errorMessage,
    );
  }

  Message toEntity() {
    return Message(
      id: id,
      content: content,
      isUser: isUser,
      timestamp: timestamp,
      documentReferences: documentReferences,
      citations: citations,
      status: status,
      errorMessage: errorMessage,
    );
  }

  static MessageStatus _parseStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'sending':
        return MessageStatus.sending;
      case 'sent':
        return MessageStatus.sent;
      case 'failed':
        return MessageStatus.failed;
      default:
        return MessageStatus.sent;
    }
  }

  static String _statusToString(MessageStatus status) {
    switch (status) {
      case MessageStatus.sending:
        return 'sending';
      case MessageStatus.sent:
        return 'sent';
      case MessageStatus.failed:
        return 'failed';
    }
  }
}

class CitationModel extends Citation {
  const CitationModel({
    required super.type,
    required super.citedText,
    required super.documentIndex,
    required super.documentTitle,
    super.pageLocation,
    super.charLocation,
  });

  factory CitationModel.fromJson(Map<String, dynamic> json) {
    return CitationModel(
      type: json['type'] as String,
      citedText: json['cited_text'] as String,
      documentIndex: json['document_index'] as int,
      documentTitle: json['document_title'] as String,
      pageLocation: json['page_location'] as String?,
      charLocation: json['char_location'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'cited_text': citedText,
      'document_index': documentIndex,
      'document_title': documentTitle,
      'page_location': pageLocation,
      'char_location': charLocation,
    };
  }

  factory CitationModel.fromEntity(Citation citation) {
    return CitationModel(
      type: citation.type,
      citedText: citation.citedText,
      documentIndex: citation.documentIndex,
      documentTitle: citation.documentTitle,
      pageLocation: citation.pageLocation,
      charLocation: citation.charLocation,
    );
  }

  Citation toEntity() {
    return Citation(
      type: type,
      citedText: citedText,
      documentIndex: documentIndex,
      documentTitle: documentTitle,
      pageLocation: pageLocation,
      charLocation: charLocation,
    );
  }
}

// API Request/Response models
class QueryRequest {
  final String question;
  final List<String> documentIds;

  const QueryRequest({
    required this.question,
    this.documentIds = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'question': question,
      if (documentIds.isNotEmpty) 'document_ids': documentIds,
    };
  }
}

class QueryResponse {
  final String answer;
  final List<ContentBlock> contentBlocks;
  final List<Source> sources;
  final double processingTime;

  const QueryResponse({
    required this.answer,
    required this.contentBlocks,
    required this.sources,
    required this.processingTime,
  });

  factory QueryResponse.fromJson(Map<String, dynamic> json) {
    return QueryResponse(
      answer: json['answer'] as String,
      contentBlocks: (json['content_blocks'] as List<dynamic>)
          .map((block) => ContentBlock.fromJson(block as Map<String, dynamic>))
          .toList(),
      sources: (json['sources'] as List<dynamic>)
          .map((source) => Source.fromJson(source as Map<String, dynamic>))
          .toList(),
      processingTime: (json['processing_time'] as num).toDouble(),
    );
  }
}

class ContentBlock {
  final String type;
  final String text;
  final List<CitationModel> citations;

  const ContentBlock({
    required this.type,
    required this.text,
    required this.citations,
  });

  factory ContentBlock.fromJson(Map<String, dynamic> json) {
    return ContentBlock(
      type: json['type'] as String,
      text: json['text'] as String,
      citations: (json['citations'] as List<dynamic>)
          .map((citation) => CitationModel.fromJson(citation as Map<String, dynamic>))
          .toList(),
    );
  }
}

class Source {
  final String documentId;
  final String documentTitle;
  final String filename;

  const Source({
    required this.documentId,
    required this.documentTitle,
    required this.filename,
  });

  factory Source.fromJson(Map<String, dynamic> json) {
    return Source(
      documentId: json['document_id'] as String,
      documentTitle: json['document_title'] as String,
      filename: json['filename'] as String,
    );
  }
}
