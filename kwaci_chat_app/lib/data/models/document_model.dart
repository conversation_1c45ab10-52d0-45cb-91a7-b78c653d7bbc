import '../../domain/entities/document.dart';
import '../../core/utils/date_formatter.dart';

class DocumentModel extends Document {
  const DocumentModel({
    required super.id,
    required super.filename,
    required super.filePath,
    required super.metadata,
    required super.status,
    super.errorMessage,
  });

  factory DocumentModel.fromJson(Map<String, dynamic> json) {
    return DocumentModel(
      id: json['id'] as String,
      filename: json['filename'] as String,
      filePath: json['file_path'] as String,
      metadata: DocumentMetadataModel.fromJson(
        json['metadata'] as Map<String, dynamic>,
      ),
      status: _parseStatus(json['status'] as String),
      errorMessage: json['error_message'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'filename': filename,
      'file_path': filePath,
      'metadata': (metadata as DocumentMetadataModel).toJson(),
      'status': _statusToString(status),
      'error_message': errorMessage,
    };
  }

  factory DocumentModel.fromEntity(Document document) {
    return DocumentModel(
      id: document.id,
      filename: document.filename,
      filePath: document.filePath,
      metadata: DocumentMetadataModel.fromEntity(document.metadata),
      status: document.status,
      errorMessage: document.errorMessage,
    );
  }

  Document toEntity() {
    return Document(
      id: id,
      filename: filename,
      filePath: filePath,
      metadata: metadata,
      status: status,
      errorMessage: errorMessage,
    );
  }

  static DocumentStatus _parseStatus(String status) {
    switch (status.toLowerCase()) {
      case 'processing':
        return DocumentStatus.processing;
      case 'completed':
        return DocumentStatus.completed;
      case 'failed':
        return DocumentStatus.failed;
      default:
        return DocumentStatus.failed;
    }
  }

  static String _statusToString(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.processing:
        return 'processing';
      case DocumentStatus.completed:
        return 'completed';
      case DocumentStatus.failed:
        return 'failed';
    }
  }
}

class DocumentMetadataModel extends DocumentMetadata {
  const DocumentMetadataModel({
    required super.originalFilename,
    required super.fileSize,
    required super.contentType,
    required super.uploadTimestamp,
    super.chunkCount,
  });

  factory DocumentMetadataModel.fromJson(Map<String, dynamic> json) {
    return DocumentMetadataModel(
      originalFilename: json['filename'] as String,
      fileSize: json['file_size'] as int,
      contentType: json['content_type'] as String,
      uploadTimestamp:
          DateFormatter.parseIsoDate(json['upload_timestamp'] as String) ??
          DateTime.now(),
      chunkCount: json['chunk_count'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'filename': originalFilename,
      'file_size': fileSize,
      'content_type': contentType,
      'upload_timestamp': DateFormatter.toIsoString(uploadTimestamp),
      'chunk_count': chunkCount,
    };
  }

  factory DocumentMetadataModel.fromEntity(DocumentMetadata metadata) {
    return DocumentMetadataModel(
      originalFilename: metadata.originalFilename,
      fileSize: metadata.fileSize,
      contentType: metadata.contentType,
      uploadTimestamp: metadata.uploadTimestamp,
      chunkCount: metadata.chunkCount,
    );
  }

  DocumentMetadata toEntity() {
    return DocumentMetadata(
      originalFilename: originalFilename,
      fileSize: fileSize,
      contentType: contentType,
      uploadTimestamp: uploadTimestamp,
      chunkCount: chunkCount,
    );
  }
}

class DocumentUploadResponse {
  final DocumentModel document;
  final String message;

  const DocumentUploadResponse({required this.document, required this.message});

  factory DocumentUploadResponse.fromJson(Map<String, dynamic> json) {
    return DocumentUploadResponse(
      document: DocumentModel.fromJson(
        json['document'] as Map<String, dynamic>,
      ),
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'document': document.toJson(), 'message': message};
  }
}

class DocumentListResponse {
  final List<DocumentModel> documents;
  final int totalCount;

  const DocumentListResponse({
    required this.documents,
    required this.totalCount,
  });

  factory DocumentListResponse.fromJson(Map<String, dynamic> json) {
    return DocumentListResponse(
      documents:
          (json['documents'] as List<dynamic>)
              .map((doc) => DocumentModel.fromJson(doc as Map<String, dynamic>))
              .toList(),
      totalCount: json['total_count'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'documents': documents.map((doc) => doc.toJson()).toList(),
      'total_count': totalCount,
    };
  }
}

class DocumentDeleteResponse {
  final String message;

  const DocumentDeleteResponse({required this.message});

  factory DocumentDeleteResponse.fromJson(Map<String, dynamic> json) {
    return DocumentDeleteResponse(message: json['message'] as String);
  }

  Map<String, dynamic> toJson() {
    return {'message': message};
  }
}
