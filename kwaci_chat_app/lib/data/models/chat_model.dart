import '../../domain/entities/chat.dart';
import '../../core/utils/date_formatter.dart';
import 'message_model.dart';

class ChatModel extends Chat {
  const ChatModel({
    required super.id,
    required super.title,
    required super.messages,
    required super.createdAt,
    required super.updatedAt,
  });

  factory ChatModel.fromJson(Map<String, dynamic> json) {
    return ChatModel(
      id: json['id'] as String,
      title: json['title'] as String,
      messages: (json['messages'] as List<dynamic>?)
          ?.map((message) => MessageModel.fromJson(message as Map<String, dynamic>))
          .toList() ?? [],
      createdAt: DateFormatter.parseIsoDate(json['created_at'] as String) ?? DateTime.now(),
      updatedAt: DateFormatter.parseIsoDate(json['updated_at'] as String) ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'messages': messages.map((message) => (message as MessageModel).toJson()).toList(),
      'created_at': DateFormatter.toIsoString(createdAt),
      'updated_at': DateFormatter.toIsoString(updatedAt),
    };
  }

  factory ChatModel.fromEntity(Chat chat) {
    return ChatModel(
      id: chat.id,
      title: chat.title,
      messages: chat.messages.map((message) => MessageModel.fromEntity(message)).toList(),
      createdAt: chat.createdAt,
      updatedAt: chat.updatedAt,
    );
  }

  Chat toEntity() {
    return Chat(
      id: id,
      title: title,
      messages: messages,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  @override
  ChatModel addMessage(message) {
    final updatedMessages = [...messages, message];
    return ChatModel(
      id: id,
      title: title,
      messages: updatedMessages,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  @override
  ChatModel updateMessage(String messageId, updatedMessage) {
    final updatedMessages = messages.map((message) {
      return message.id == messageId ? updatedMessage : message;
    }).toList();
    
    return ChatModel(
      id: id,
      title: title,
      messages: updatedMessages,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  @override
  ChatModel removeMessage(String messageId) {
    final updatedMessages = messages.where((message) => message.id != messageId).toList();
    return ChatModel(
      id: id,
      title: title,
      messages: updatedMessages,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }
}
