import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/network/dio_client.dart';
import '../../../core/errors/exceptions.dart';
import '../../models/document_model.dart';

abstract class DocumentApiService {
  Future<DocumentUploadResponse> uploadDocument(File file, String? title);
  Future<DocumentListResponse> getDocuments();
  Future<DocumentModel> getDocument(String documentId);
  Future<DocumentDeleteResponse> deleteDocument(String documentId);
  Future<Map<String, dynamic>> healthCheck();
}

class DocumentApiServiceImpl implements DocumentApiService {
  final DioClient _dioClient;

  DocumentApiServiceImpl(this._dioClient);

  @override
  Future<DocumentUploadResponse> uploadDocument(
    File file,
    String? title,
  ) async {
    try {
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(file.path),
        if (title != null) 'title': title,
      });

      final response = await _dioClient.post(
        '/documents/upload',
        data: formData,
      );
      return DocumentUploadResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  @override
  Future<DocumentListResponse> getDocuments() async {
    try {
      final response = await _dioClient.get('/documents');
      return DocumentListResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  @override
  Future<DocumentModel> getDocument(String documentId) async {
    try {
      final response = await _dioClient.get('/documents/$documentId');
      return DocumentModel.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  @override
  Future<DocumentDeleteResponse> deleteDocument(String documentId) async {
    try {
      final response = await _dioClient.delete('/documents/$documentId');
      return DocumentDeleteResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  @override
  Future<Map<String, dynamic>> healthCheck() async {
    try {
      final response = await _dioClient.get('/health');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Exception _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const NetworkException(
          message: 'Connection timeout. Please check your internet connection.',
        );
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message =
            error.response?.data?['error'] ?? 'Server error occurred';
        return ServerException(message: message, code: statusCode?.toString());
      case DioExceptionType.cancel:
        return const NetworkException(message: 'Request was cancelled');
      case DioExceptionType.connectionError:
        return const NetworkException(
          message:
              'No internet connection. Please check your network settings.',
        );
      default:
        return NetworkException(
          message: error.message ?? 'Unknown network error occurred',
        );
    }
  }
}

// Provider for DocumentApiService
final documentApiServiceProvider = Provider<DocumentApiService>((ref) {
  final dioClient = ref.read(dioClientProvider);
  return DocumentApiServiceImpl(dioClient);
});
