import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../core/errors/exceptions.dart';
import '../../models/user_model.dart';

abstract class AuthApiService {
  Future<UserModel> signUp({required String email, required String password});
  Future<UserModel> signIn({required String email, required String password});
  Future<void> signOut();
  Future<UserModel?> getCurrentUser();
  Future<bool> isAuthenticated();
  Stream<UserModel?> get authStateChanges;
}

class AuthApiServiceImpl implements AuthApiService {
  final SupabaseClient _supabaseClient;

  AuthApiServiceImpl(this._supabaseClient);

  @override
  Future<UserModel> signUp({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _supabaseClient.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw ServerException(
          message: 'Failed to create account. Please try again.',
          code: 'SIGNUP_FAILED',
        );
      }

      return UserModel.fromSupabaseUser(response.user!);
    } on AuthException catch (e) {
      throw ServerException(
        message: _getAuthErrorMessage(e),
        code: e.statusCode ?? 'AUTH_ERROR',
      );
    } catch (e) {
      throw ServerException(
        message: 'An unexpected error occurred. Please try again.',
        code: 'UNKNOWN_ERROR',
      );
    }
  }

  @override
  Future<UserModel> signIn({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _supabaseClient.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw ServerException(
          message: 'Invalid email or password.',
          code: 'SIGNIN_FAILED',
        );
      }

      return UserModel.fromSupabaseUser(response.user!);
    } on AuthException catch (e) {
      throw ServerException(
        message: _getAuthErrorMessage(e),
        code: e.statusCode ?? 'AUTH_ERROR',
      );
    } catch (e) {
      throw ServerException(
        message: 'An unexpected error occurred. Please try again.',
        code: 'UNKNOWN_ERROR',
      );
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await _supabaseClient.auth.signOut();
    } on AuthException catch (e) {
      throw ServerException(
        message: _getAuthErrorMessage(e),
        code: e.statusCode ?? 'AUTH_ERROR',
      );
    } catch (e) {
      throw ServerException(
        message: 'Failed to sign out. Please try again.',
        code: 'SIGNOUT_ERROR',
      );
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      final user = _supabaseClient.auth.currentUser;
      return user != null ? UserModel.fromSupabaseUser(user) : null;
    } catch (e) {
      throw ServerException(
        message: 'Failed to get current user.',
        code: 'GET_USER_ERROR',
      );
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      final session = _supabaseClient.auth.currentSession;
      return session != null && session.accessToken.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  @override
  Stream<UserModel?> get authStateChanges {
    return _supabaseClient.auth.onAuthStateChange.map((data) {
      final user = data.session?.user;
      return user != null ? UserModel.fromSupabaseUser(user) : null;
    });
  }

  String _getAuthErrorMessage(AuthException e) {
    switch (e.message.toLowerCase()) {
      case 'invalid login credentials':
        return 'Invalid email or password. Please check your credentials and try again.';
      case 'email not confirmed':
        return 'Please check your email and click the confirmation link before signing in.';
      case 'user already registered':
        return 'An account with this email already exists. Please sign in instead.';
      case 'password should be at least 6 characters':
        return 'Password must be at least 6 characters long.';
      case 'signup is disabled':
        return 'Account registration is currently disabled. Please contact support.';
      case 'email rate limit exceeded':
        return 'Too many requests. Please wait a moment before trying again.';
      default:
        return e.message.isNotEmpty ? e.message : 'An authentication error occurred.';
    }
  }
}

// Provider for AuthApiService
final authApiServiceProvider = Provider<AuthApiService>((ref) {
  return AuthApiServiceImpl(Supabase.instance.client);
});
