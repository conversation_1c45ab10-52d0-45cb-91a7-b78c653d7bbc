import 'package:hive/hive.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/errors/exceptions.dart';
import '../../models/chat_model.dart';

abstract class ChatLocalDataSource {
  Future<List<ChatModel>> getCachedChats();
  Future<void> cacheChats(List<ChatModel> chats);
  Future<void> cacheChat(ChatModel chat);
  Future<ChatModel?> getCachedChat(String chatId);
  Future<void> removeChat(String chatId);
  Future<void> clearCache();
}

class ChatLocalDataSourceImpl implements ChatLocalDataSource {
  static const String _chatsKey = 'cached_chats';

  @override
  Future<List<ChatModel>> getCachedChats() async {
    try {
      final box = await Hive.openBox(AppConstants.chatHistoryBox);
      final chatsJson = box.get(_chatsKey, defaultValue: <dynamic>[]) as List<dynamic>;
      
      return chatsJson
          .map((json) => ChatModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get cached chats: $e');
    }
  }

  @override
  Future<void> cacheChats(List<ChatModel> chats) async {
    try {
      final box = await Hive.openBox(AppConstants.chatHistoryBox);
      final chatsJson = chats.map((chat) => chat.toJson()).toList();
      await box.put(_chatsKey, chatsJson);
    } catch (e) {
      throw CacheException(message: 'Failed to cache chats: $e');
    }
  }

  @override
  Future<void> cacheChat(ChatModel chat) async {
    try {
      final cachedChats = await getCachedChats();
      
      // Remove existing chat with same ID if it exists
      cachedChats.removeWhere((c) => c.id == chat.id);
      
      // Add the new/updated chat
      cachedChats.add(chat);
      
      // Sort by updated date (most recent first)
      cachedChats.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
      
      // Cache the updated list
      await cacheChats(cachedChats);
    } catch (e) {
      throw CacheException(message: 'Failed to cache chat: $e');
    }
  }

  @override
  Future<ChatModel?> getCachedChat(String chatId) async {
    try {
      final cachedChats = await getCachedChats();
      return cachedChats.where((chat) => chat.id == chatId).firstOrNull;
    } catch (e) {
      throw CacheException(message: 'Failed to get cached chat: $e');
    }
  }

  @override
  Future<void> removeChat(String chatId) async {
    try {
      final cachedChats = await getCachedChats();
      cachedChats.removeWhere((chat) => chat.id == chatId);
      await cacheChats(cachedChats);
    } catch (e) {
      throw CacheException(message: 'Failed to remove chat from cache: $e');
    }
  }

  @override
  Future<void> clearCache() async {
    try {
      final box = await Hive.openBox(AppConstants.chatHistoryBox);
      await box.delete(_chatsKey);
    } catch (e) {
      throw CacheException(message: 'Failed to clear chat cache: $e');
    }
  }
}

// Extension to handle null safety for firstOrNull
extension IterableExtension<T> on Iterable<T> {
  T? get firstOrNull {
    if (isEmpty) return null;
    return first;
  }
}

// Provider for ChatLocalDataSource
final chatLocalDataSourceProvider = Provider<ChatLocalDataSource>((ref) {
  return ChatLocalDataSourceImpl();
});
