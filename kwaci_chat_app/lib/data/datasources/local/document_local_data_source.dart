import 'package:hive/hive.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/errors/exceptions.dart';
import '../../models/document_model.dart';

abstract class DocumentLocalDataSource {
  Future<List<DocumentModel>> getCachedDocuments();
  Future<void> cacheDocuments(List<DocumentModel> documents);
  Future<void> cacheDocument(DocumentModel document);
  Future<void> removeDocument(String documentId);
  Future<void> clearCache();
}

class DocumentLocalDataSourceImpl implements DocumentLocalDataSource {
  static const String _documentsKey = 'cached_documents';

  @override
  Future<List<DocumentModel>> getCachedDocuments() async {
    try {
      final box = await Hive.openBox(AppConstants.documentsBox);
      final documentsJson = box.get(_documentsKey, defaultValue: <dynamic>[]) as List<dynamic>;
      
      return documentsJson
          .map((json) => DocumentModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get cached documents: $e');
    }
  }

  @override
  Future<void> cacheDocuments(List<DocumentModel> documents) async {
    try {
      final box = await Hive.openBox(AppConstants.documentsBox);
      final documentsJson = documents.map((doc) => doc.toJson()).toList();
      await box.put(_documentsKey, documentsJson);
    } catch (e) {
      throw CacheException(message: 'Failed to cache documents: $e');
    }
  }

  @override
  Future<void> cacheDocument(DocumentModel document) async {
    try {
      final cachedDocuments = await getCachedDocuments();
      
      // Remove existing document with same ID if it exists
      cachedDocuments.removeWhere((doc) => doc.id == document.id);
      
      // Add the new/updated document
      cachedDocuments.add(document);
      
      // Cache the updated list
      await cacheDocuments(cachedDocuments);
    } catch (e) {
      throw CacheException(message: 'Failed to cache document: $e');
    }
  }

  @override
  Future<void> removeDocument(String documentId) async {
    try {
      final cachedDocuments = await getCachedDocuments();
      cachedDocuments.removeWhere((doc) => doc.id == documentId);
      await cacheDocuments(cachedDocuments);
    } catch (e) {
      throw CacheException(message: 'Failed to remove document from cache: $e');
    }
  }

  @override
  Future<void> clearCache() async {
    try {
      final box = await Hive.openBox(AppConstants.documentsBox);
      await box.delete(_documentsKey);
    } catch (e) {
      throw CacheException(message: 'Failed to clear document cache: $e');
    }
  }
}

// Provider for DocumentLocalDataSource
final documentLocalDataSourceProvider = Provider<DocumentLocalDataSource>((ref) {
  return DocumentLocalDataSourceImpl();
});
