import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/providers/app_initialization_provider.dart';
import 'auth_notifier.dart';
import 'auth_state.dart';

// Auth state provider - only creates when Supabase is ready
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  // Watch the initialization state to ensure Supabase is ready
  final isSupabaseReady = ref.watch(supabaseReadyProvider);

  if (!isSupabaseReady) {
    // Return a notifier with initial state if Supabase isn't ready yet
    return AuthNotifier(ref, initializeImmediately: false);
  }

  return AuthNotifier(ref, initializeImmediately: true);
});

// Convenience providers for specific auth states
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authProvider);
  return authState is AuthAuthenticated;
});

final isLoadingProvider = Provider<bool>((ref) {
  final authState = ref.watch(authProvider);
  return authState is AuthLoading;
});

final currentUserProvider = Provider((ref) {
  final authState = ref.watch(authProvider);
  return authState is AuthAuthenticated ? authState.user : null;
});

final authErrorProvider = Provider<String?>((ref) {
  final authState = ref.watch(authProvider);
  return authState is AuthError ? authState.message : null;
});
