import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../domain/entities/user.dart';
import '../../domain/usecases/auth/auth_use_cases.dart';
import 'auth_state.dart';

class AuthNotifier extends StateNotifier<AuthState> {
  final Ref _ref;
  StreamSubscription<User?>? _authStateSubscription;

  AuthNotifier(this._ref) : super(const AuthInitial()) {
    _initializeAuthState();
  }

  void _initializeAuthState() {
    // Listen to auth state changes from repository
    final repository = _ref.read(authRepositoryProvider);
    _authStateSubscription = repository.authStateChanges.listen(
      (user) {
        if (user != null) {
          state = AuthAuthenticated(user);
        } else {
          state = const AuthUnauthenticated();
        }
      },
      onError: (error) {
        state = AuthError('Authentication error: $error');
      },
    );

    // Check initial auth status
    _checkInitialAuthStatus();
  }

  Future<void> _checkInitialAuthStatus() async {
    try {
      final checkAuthStatus = _ref.read(checkAuthStatusProvider);
      final result = await checkAuthStatus();

      result.fold((failure) => state = AuthError(failure.message), (
        isAuthenticated,
      ) async {
        if (isAuthenticated) {
          final getCurrentUser = _ref.read(getCurrentUserProvider);
          final userResult = await getCurrentUser();
          userResult.fold(
            (failure) => state = AuthError(failure.message),
            (user) =>
                state =
                    user != null
                        ? AuthAuthenticated(user)
                        : const AuthUnauthenticated(),
          );
        } else {
          state = const AuthUnauthenticated();
        }
      });
    } catch (e) {
      state = AuthError('Failed to check authentication status: $e');
    }
  }

  Future<void> signIn({required String email, required String password}) async {
    state = const AuthLoading();

    try {
      final signIn = _ref.read(signInProvider);
      final result = await signIn(email: email, password: password);

      result.fold(
        (failure) => state = AuthError(failure.message),
        (user) => state = AuthAuthenticated(user),
      );
    } catch (e) {
      state = AuthError('Sign in failed: $e');
    }
  }

  Future<void> signUp({
    required String email,
    required String password,
    required String confirmPassword,
  }) async {
    state = const AuthLoading();

    try {
      final signUp = _ref.read(signUpProvider);
      final result = await signUp(
        email: email,
        password: password,
        confirmPassword: confirmPassword,
      );

      result.fold(
        (failure) => state = AuthError(failure.message),
        (user) => state = AuthAuthenticated(user),
      );
    } catch (e) {
      state = AuthError('Sign up failed: $e');
    }
  }

  Future<void> signOut() async {
    state = const AuthLoading();

    try {
      final signOut = _ref.read(signOutProvider);
      final result = await signOut();

      result.fold(
        (failure) => state = AuthError(failure.message),
        (_) => state = const AuthUnauthenticated(),
      );
    } catch (e) {
      state = AuthError('Sign out failed: $e');
    }
  }

  void clearError() {
    if (state is AuthError) {
      state = const AuthUnauthenticated();
    }
  }

  @override
  void dispose() {
    _authStateSubscription?.cancel();
    super.dispose();
  }
}
