import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/chat.dart';
import '../../domain/entities/message.dart';
import '../../domain/usecases/chat_use_cases.dart';
import '../../domain/usecases/get_chats.dart';
import '../../domain/usecases/create_chat.dart';
import '../../domain/usecases/send_message.dart';

// Chat list state
class ChatListState {
  final List<Chat> chats;
  final bool isLoading;
  final String? error;

  const ChatListState({
    this.chats = const [],
    this.isLoading = false,
    this.error,
  });

  ChatListState copyWith({List<Chat>? chats, bool? isLoading, String? error}) {
    return ChatListState(
      chats: chats ?? this.chats,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Chat list notifier
class ChatListNotifier extends StateNotifier<ChatListState> {
  final GetChats _getChats;
  final GetCachedChats _getCachedChats;
  final CreateChat _createChat;
  final DeleteChat _deleteChat;
  final UpdateChatTitle _updateChatTitle;

  ChatListNotifier({
    required GetChats getChats,
    required GetCachedChats getCachedChats,
    required CreateChat createChat,
    required DeleteChat deleteChat,
    required UpdateChatTitle updateChatTitle,
  }) : _getChats = getChats,
       _getCachedChats = getCachedChats,
       _createChat = createChat,
       _deleteChat = deleteChat,
       _updateChatTitle = updateChatTitle,
       super(const ChatListState());

  Future<void> loadChats() async {
    state = state.copyWith(isLoading: true, error: null);

    final result = await _getChats();

    result.fold(
      (failure) {
        // Try to load cached chats if network request fails
        _loadCachedChats();
        state = state.copyWith(isLoading: false, error: failure.message);
      },
      (chats) {
        // Sort chats by updated date (most recent first)
        chats.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
        state = state.copyWith(chats: chats, isLoading: false, error: null);
      },
    );
  }

  Future<void> _loadCachedChats() async {
    final result = await _getCachedChats();

    result.fold(
      (failure) {
        // Keep current state if cache also fails
      },
      (chats) {
        // Sort chats by updated date (most recent first)
        chats.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
        state = state.copyWith(chats: chats);
      },
    );
  }

  Future<Chat?> createNewChat({String? title}) async {
    final result = await _createChat(title: title);

    return result.fold(
      (failure) {
        state = state.copyWith(error: failure.message);
        return null;
      },
      (chat) {
        // Add the new chat to the beginning of the list
        final updatedChats = [chat, ...state.chats];
        state = state.copyWith(chats: updatedChats);
        return chat;
      },
    );
  }

  Future<void> deleteChat(String chatId) async {
    final result = await _deleteChat(chatId);

    result.fold(
      (failure) {
        throw Exception(failure.message);
      },
      (_) {
        // Remove the chat from the list
        final updatedChats =
            state.chats.where((chat) => chat.id != chatId).toList();
        state = state.copyWith(chats: updatedChats);
      },
    );
  }

  Future<void> updateChatTitle(String chatId, String title) async {
    final result = await _updateChatTitle(chatId, title);

    result.fold(
      (failure) {
        throw Exception(failure.message);
      },
      (updatedChat) {
        // Update the chat in the list
        final updatedChats =
            state.chats.map((chat) {
              return chat.id == chatId ? updatedChat : chat;
            }).toList();
        state = state.copyWith(chats: updatedChats);
      },
    );
  }

  void updateChatInList(Chat updatedChat) {
    final updatedChats =
        state.chats.map((chat) {
          return chat.id == updatedChat.id ? updatedChat : chat;
        }).toList();

    // Sort chats by updated date (most recent first)
    updatedChats.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

    state = state.copyWith(chats: updatedChats);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Chat list provider
final chatListProvider = StateNotifierProvider<ChatListNotifier, ChatListState>(
  (ref) {
    return ChatListNotifier(
      getChats: ref.read(getChatsProvider),
      getCachedChats: ref.read(getCachedChatsProvider),
      createChat: ref.read(createChatProvider),
      deleteChat: ref.read(deleteChatProvider),
      updateChatTitle: ref.read(updateChatTitleProvider),
    );
  },
);

// Current chat state
class CurrentChatState {
  final Chat? chat;
  final bool isLoading;
  final bool isSending;
  final String? error;

  const CurrentChatState({
    this.chat,
    this.isLoading = false,
    this.isSending = false,
    this.error,
  });

  CurrentChatState copyWith({
    Chat? chat,
    bool? isLoading,
    bool? isSending,
    String? error,
  }) {
    return CurrentChatState(
      chat: chat ?? this.chat,
      isLoading: isLoading ?? this.isLoading,
      isSending: isSending ?? this.isSending,
      error: error,
    );
  }
}

// Current chat notifier
class CurrentChatNotifier extends StateNotifier<CurrentChatState> {
  final GetChat _getChat;
  final SendMessage _sendMessage;

  CurrentChatNotifier({
    required GetChat getChat,
    required SendMessage sendMessage,
  }) : _getChat = getChat,
       _sendMessage = sendMessage,
       super(const CurrentChatState());

  Future<void> loadChat(String chatId) async {
    state = state.copyWith(isLoading: true, error: null);

    final result = await _getChat(chatId);

    result.fold(
      (failure) {
        state = state.copyWith(isLoading: false, error: failure.message);
      },
      (chat) {
        state = state.copyWith(chat: chat, isLoading: false, error: null);
      },
    );
  }

  Future<void> sendMessage({
    required String content,
    List<String> documentIds = const [],
  }) async {
    if (state.chat == null) return;

    state = state.copyWith(isSending: true, error: null);

    // Create user message and add to chat immediately
    final userMessage = Message(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      isUser: true,
      timestamp: DateTime.now(),
      documentReferences: documentIds,
      status: MessageStatus.sending,
    );

    final updatedChat = state.chat!.addMessage(userMessage);
    state = state.copyWith(chat: updatedChat);

    final result = await _sendMessage(
      chatId: state.chat!.id,
      content: content,
      documentIds: documentIds,
    );

    result.fold(
      (failure) {
        // Update user message status to failed
        final failedUserMessage = userMessage.copyWith(
          status: MessageStatus.failed,
        );
        final chatWithFailedMessage = state.chat!.updateMessage(
          userMessage.id,
          failedUserMessage,
        );

        state = state.copyWith(
          chat: chatWithFailedMessage,
          isSending: false,
          error: failure.message,
        );
      },
      (aiMessage) {
        // Update user message status to sent and add AI response
        final sentUserMessage = userMessage.copyWith(
          status: MessageStatus.sent,
        );
        var chatWithSentMessage = state.chat!.updateMessage(
          userMessage.id,
          sentUserMessage,
        );
        chatWithSentMessage = chatWithSentMessage.addMessage(aiMessage);

        state = state.copyWith(
          chat: chatWithSentMessage,
          isSending: false,
          error: null,
        );
      },
    );
  }

  void setChat(Chat chat) {
    state = state.copyWith(chat: chat);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Current chat provider
final currentChatProvider =
    StateNotifierProvider<CurrentChatNotifier, CurrentChatState>((ref) {
      return CurrentChatNotifier(
        getChat: ref.read(getChatProvider),
        sendMessage: ref.read(sendMessageProvider),
      );
    });
