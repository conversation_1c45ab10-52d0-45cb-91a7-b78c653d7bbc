import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/document.dart';
import '../../domain/usecases/document_use_cases.dart';
import '../../domain/usecases/upload_document.dart';
import '../../domain/usecases/get_documents.dart';
import '../../domain/usecases/delete_document.dart';

// Document list state
class DocumentListState {
  final List<Document> documents;
  final bool isLoading;
  final String? error;

  const DocumentListState({
    this.documents = const [],
    this.isLoading = false,
    this.error,
  });

  DocumentListState copyWith({
    List<Document>? documents,
    bool? isLoading,
    String? error,
  }) {
    return DocumentListState(
      documents: documents ?? this.documents,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Document list notifier
class DocumentListNotifier extends StateNotifier<DocumentListState> {
  final UploadDocument _uploadDocument;
  final GetDocuments _getDocuments;
  final GetCachedDocuments _getCachedDocuments;
  final DeleteDocument _deleteDocument;

  DocumentListNotifier({
    required UploadDocument uploadDocument,
    required GetDocuments getDocuments,
    required GetCachedDocuments getCachedDocuments,
    required DeleteDocument deleteDocument,
  }) : _uploadDocument = uploadDocument,
       _getDocuments = getDocuments,
       _getCachedDocuments = getCachedDocuments,
       _deleteDocument = deleteDocument,
       super(const DocumentListState());

  Future<void> loadDocuments() async {
    state = state.copyWith(isLoading: true, error: null);

    final result = await _getDocuments();

    result.fold(
      (failure) {
        // Try to load cached documents if network request fails
        _loadCachedDocuments();
        state = state.copyWith(isLoading: false, error: failure.message);
      },
      (documents) {
        state = state.copyWith(
          documents: documents,
          isLoading: false,
          error: null,
        );
      },
    );
  }

  Future<void> _loadCachedDocuments() async {
    final result = await _getCachedDocuments();

    result.fold(
      (failure) {
        // Keep current state if cache also fails
      },
      (documents) {
        state = state.copyWith(documents: documents);
      },
    );
  }

  Future<void> uploadDocument(File file, {String? title}) async {
    final result = await _uploadDocument(file: file, title: title);

    result.fold(
      (failure) {
        throw Exception(failure.message);
      },
      (document) {
        // Add the new document to the list
        final updatedDocuments = <Document>[document, ...state.documents];
        state = state.copyWith(documents: updatedDocuments);
      },
    );
  }

  Future<void> deleteDocument(String documentId) async {
    final result = await _deleteDocument(documentId);

    result.fold(
      (failure) {
        throw Exception(failure.message);
      },
      (_) {
        // Remove the document from the list
        final updatedDocuments =
            state.documents.where((doc) => doc.id != documentId).toList();
        state = state.copyWith(documents: updatedDocuments);
      },
    );
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Document list provider
final documentListProvider =
    StateNotifierProvider<DocumentListNotifier, DocumentListState>((ref) {
      return DocumentListNotifier(
        uploadDocument: ref.read(uploadDocumentProvider),
        getDocuments: ref.read(getDocumentsProvider),
        getCachedDocuments: ref.read(getCachedDocumentsProvider),
        deleteDocument: ref.read(deleteDocumentProvider),
      );
    });

// Upload state
class UploadState {
  final bool isUploading;
  final double progress;
  final String? error;
  final Document? uploadedDocument;

  const UploadState({
    this.isUploading = false,
    this.progress = 0.0,
    this.error,
    this.uploadedDocument,
  });

  UploadState copyWith({
    bool? isUploading,
    double? progress,
    String? error,
    Document? uploadedDocument,
  }) {
    return UploadState(
      isUploading: isUploading ?? this.isUploading,
      progress: progress ?? this.progress,
      error: error,
      uploadedDocument: uploadedDocument ?? this.uploadedDocument,
    );
  }
}

// Upload notifier
class UploadNotifier extends StateNotifier<UploadState> {
  final UploadDocument _uploadDocument;

  UploadNotifier({required UploadDocument uploadDocument})
    : _uploadDocument = uploadDocument,
      super(const UploadState());

  Future<void> uploadFile(File file, {String? title}) async {
    state = state.copyWith(
      isUploading: true,
      progress: 0.0,
      error: null,
      uploadedDocument: null,
    );

    try {
      // Simulate progress updates
      for (int i = 1; i <= 5; i++) {
        await Future.delayed(const Duration(milliseconds: 200));
        state = state.copyWith(progress: i * 0.2);
      }

      final result = await _uploadDocument(file: file, title: title);

      result.fold(
        (failure) {
          state = state.copyWith(isUploading: false, error: failure.message);
        },
        (document) {
          state = state.copyWith(
            isUploading: false,
            progress: 1.0,
            uploadedDocument: document,
          );
        },
      );
    } catch (e) {
      state = state.copyWith(isUploading: false, error: 'Upload failed: $e');
    }
  }

  void reset() {
    state = const UploadState();
  }
}

// Upload provider
final uploadProvider = StateNotifierProvider<UploadNotifier, UploadState>((
  ref,
) {
  return UploadNotifier(uploadDocument: ref.read(uploadDocumentProvider));
});
