import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/entities/message.dart';
import '../providers/chat_providers.dart';
import '../providers/document_providers.dart';
import '../widgets/message_bubble.dart';
import '../widgets/message_input.dart';
import '../widgets/typing_indicator.dart';

class ChatDetailPage extends ConsumerStatefulWidget {
  final String chatId;

  const ChatDetailPage({
    super.key,
    required this.chatId,
  });

  @override
  ConsumerState<ChatDetailPage> createState() => _ChatDetailPageState();
}

class _ChatDetailPageState extends ConsumerState<ChatDetailPage> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();
  final List<String> _selectedDocuments = [];

  @override
  void initState() {
    super.initState();
    // Load chat when page opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(currentChatProvider.notifier).loadChat(widget.chatId);
      ref.read(documentListProvider.notifier).loadDocuments();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final chatState = ref.watch(currentChatProvider);
    final chat = chatState.chat;

    return Scaffold(
      appBar: AppBar(
        title: Text(chat?.displayTitle ?? 'Chat'),
        elevation: 0,
        actions: [
          if (chat != null)
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'rename':
                    _showRenameDialog();
                    break;
                  case 'clear':
                    _showClearChatDialog();
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'rename',
                  child: Row(
                    children: [
                      Icon(Icons.edit),
                      SizedBox(width: 8),
                      Text('Rename Chat'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'clear',
                  child: Row(
                    children: [
                      Icon(Icons.clear_all),
                      SizedBox(width: 8),
                      Text('Clear Messages'),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
      body: Column(
        children: [
          // Messages List
          Expanded(
            child: _buildMessagesList(chatState),
          ),
          
          // Typing Indicator
          if (chatState.isSending)
            const Padding(
              padding: EdgeInsets.all(AppConstants.defaultPadding),
              child: TypingIndicator(),
            ),
          
          // Message Input
          MessageInput(
            controller: _messageController,
            selectedDocuments: _selectedDocuments,
            onSend: _sendMessage,
            onDocumentSelected: _onDocumentSelected,
            onDocumentRemoved: _onDocumentRemoved,
          ),
        ],
      ),
    );
  }

  Widget _buildMessagesList(CurrentChatState state) {
    if (state.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (state.error != null && state.chat == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'Error loading chat',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              state.error!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.largePadding),
            ElevatedButton(
              onPressed: () {
                ref.read(currentChatProvider.notifier).loadChat(widget.chatId);
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    final messages = state.chat?.messages ?? [];

    if (messages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'Start a conversation',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Send a message to begin chatting with the AI assistant.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: messages.length,
      itemBuilder: (context, index) {
        final message = messages[index];
        return MessageBubble(
          message: message,
          onCitationTap: _onCitationTap,
        );
      },
    );
  }

  Future<void> _sendMessage(String content) async {
    if (content.trim().isEmpty) return;

    // Clear the input
    _messageController.clear();

    // Send the message
    await ref.read(currentChatProvider.notifier).sendMessage(
      content: content,
      documentIds: List.from(_selectedDocuments),
    );

    // Clear selected documents after sending
    setState(() {
      _selectedDocuments.clear();
    });

    // Scroll to bottom
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: AppConstants.shortAnimation,
          curve: Curves.easeOut,
        );
      }
    });

    // Update chat list
    final currentChat = ref.read(currentChatProvider).chat;
    if (currentChat != null) {
      ref.read(chatListProvider.notifier).updateChatInList(currentChat);
    }
  }

  void _onDocumentSelected(String documentId) {
    setState(() {
      if (!_selectedDocuments.contains(documentId)) {
        _selectedDocuments.add(documentId);
      }
    });
  }

  void _onDocumentRemoved(String documentId) {
    setState(() {
      _selectedDocuments.remove(documentId);
    });
  }

  void _onCitationTap(Citation citation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(citation.documentTitle),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Citation Type: ${citation.type}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              citation.citedText,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (citation.pageLocation != null) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                'Page: ${citation.pageLocation}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showRenameDialog() {
    final currentTitle = ref.read(currentChatProvider).chat?.title ?? '';
    final controller = TextEditingController(text: currentTitle);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rename Chat'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Chat Title',
            hintText: 'Enter a new title for this chat',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final newTitle = controller.text.trim();
              if (newTitle.isNotEmpty) {
                Navigator.of(context).pop();
                _renameChat(newTitle);
              }
            },
            child: const Text('Rename'),
          ),
        ],
      ),
    );
  }

  Future<void> _renameChat(String newTitle) async {
    try {
      await ref.read(chatListProvider.notifier).updateChatTitle(widget.chatId, newTitle);
      
      // Reload the current chat to reflect the title change
      ref.read(currentChatProvider.notifier).loadChat(widget.chatId);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Chat renamed successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to rename chat: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _showClearChatDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Messages'),
        content: const Text(
          'This will clear all messages in this chat. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement clear messages functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Clear messages feature will be implemented soon'),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }
}
