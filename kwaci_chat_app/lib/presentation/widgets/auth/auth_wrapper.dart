import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../pages/auth/splash_page.dart';
import '../../pages/auth/introduction_page.dart';
import '../../pages/main_navigation_page.dart';
import '../../providers/auth_providers.dart';
import '../../providers/auth_state.dart';

class AuthWrapper extends ConsumerWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);

    return switch (authState) {
      AuthInitial() => const SplashPage(),
      AuthLoading() => const SplashPage(),
      AuthAuthenticated() => const MainNavigationPage(),
      AuthUnauthenticated() => const IntroductionPage(),
      AuthError() => const IntroductionPage(),
      _ => const SplashPage(),
    };
  }
}
