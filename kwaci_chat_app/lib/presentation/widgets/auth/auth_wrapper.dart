import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/app_initialization_provider.dart';
import '../../pages/auth/splash_page.dart';
import '../../pages/auth/introduction_page.dart';
import '../../pages/main_navigation_page.dart';
import '../../providers/auth_providers.dart';
import '../../providers/auth_state.dart';

class AuthWrapper extends ConsumerWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);

    // Initialize auth when Supabase is ready
    ref.listen(supabaseReadyProvider, (previous, next) {
      if (next && (previous == false || previous == null)) {
        // Supabase just became ready, initialize auth
        ref.read(authProvider.notifier).initializeWhenReady();
      }
    });

    return switch (authState) {
      AuthInitial() => const SplashPage(),
      AuthLoading() => const SplashPage(),
      AuthAuthenticated() => const MainNavigationPage(),
      AuthUnauthenticated() => const IntroductionPage(),
      AuthError() => const IntroductionPage(),
      _ => const SplashPage(),
    };
  }
}
