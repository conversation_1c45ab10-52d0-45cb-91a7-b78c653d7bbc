import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/entities/document.dart';
import '../providers/document_providers.dart';

class MessageInput extends ConsumerStatefulWidget {
  final TextEditingController controller;
  final List<String> selectedDocuments;
  final Function(String) onSend;
  final Function(String) onDocumentSelected;
  final Function(String) onDocumentRemoved;

  const MessageInput({
    super.key,
    required this.controller,
    required this.selectedDocuments,
    required this.onSend,
    required this.onDocumentSelected,
    required this.onDocumentRemoved,
  });

  @override
  ConsumerState<MessageInput> createState() => _MessageInputState();
}

class _MessageInputState extends ConsumerState<MessageInput> {
  final FocusNode _focusNode = FocusNode();
  bool _showDocumentPicker = false;

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Selected documents
          if (widget.selectedDocuments.isNotEmpty)
            _buildSelectedDocuments(),
          
          // Document picker
          if (_showDocumentPicker)
            _buildDocumentPicker(),
          
          // Message input
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildSelectedDocuments() {
    final documentState = ref.watch(documentListProvider);
    
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected Documents:',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Wrap(
            spacing: AppConstants.smallPadding,
            runSpacing: 4,
            children: widget.selectedDocuments.map((docId) {
              final document = documentState.documents
                  .where((doc) => doc.id == docId)
                  .firstOrNull;
              
              return Chip(
                label: Text(
                  document?.filename ?? 'Unknown Document',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                avatar: const Icon(Icons.attach_file, size: 16),
                deleteIcon: const Icon(Icons.close, size: 16),
                onDeleted: () => widget.onDocumentRemoved(docId),
                backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                deleteIconColor: Theme.of(context).colorScheme.onPrimaryContainer,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentPicker() {
    final documentState = ref.watch(documentListProvider);
    final availableDocuments = documentState.documents
        .where((doc) => !widget.selectedDocuments.contains(doc.id))
        .toList();

    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              children: [
                Icon(
                  Icons.attach_file,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'Select Documents',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _showDocumentPicker = false;
                    });
                  },
                  icon: const Icon(Icons.close),
                  iconSize: 20,
                ),
              ],
            ),
          ),
          
          // Document list
          Expanded(
            child: availableDocuments.isEmpty
                ? Center(
                    child: Text(
                      'No documents available',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.defaultPadding,
                    ),
                    itemCount: availableDocuments.length,
                    itemBuilder: (context, index) {
                      final document = availableDocuments[index];
                      return _DocumentPickerItem(
                        document: document,
                        onTap: () {
                          widget.onDocumentSelected(document.id);
                          setState(() {
                            _showDocumentPicker = false;
                          });
                        },
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          // Document attachment button
          IconButton(
            onPressed: () {
              setState(() {
                _showDocumentPicker = !_showDocumentPicker;
              });
            },
            icon: Icon(
              Icons.attach_file,
              color: _showDocumentPicker
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          
          // Message input field
          Expanded(
            child: TextField(
              controller: widget.controller,
              focusNode: _focusNode,
              decoration: InputDecoration(
                hintText: AppConstants.chatPlaceholder,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surfaceVariant,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.defaultPadding,
                  vertical: AppConstants.smallPadding,
                ),
              ),
              maxLines: null,
              textInputAction: TextInputAction.send,
              onSubmitted: (value) => _sendMessage(),
            ),
          ),
          
          const SizedBox(width: AppConstants.smallPadding),
          
          // Send button
          IconButton(
            onPressed: _sendMessage,
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.send,
                color: Theme.of(context).colorScheme.onPrimary,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _sendMessage() {
    final message = widget.controller.text.trim();
    if (message.isNotEmpty) {
      widget.onSend(message);
      _focusNode.requestFocus();
    }
  }
}

class _DocumentPickerItem extends StatelessWidget {
  final Document document;
  final VoidCallback onTap;

  const _DocumentPickerItem({
    required this.document,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: ListTile(
        leading: Icon(
          _getFileIcon(document.metadata.contentType),
          color: Theme.of(context).colorScheme.primary,
        ),
        title: Text(
          document.filename,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          document.metadata.originalFilename,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: Icon(
          Icons.add_circle_outline,
          color: Theme.of(context).colorScheme.primary,
        ),
        onTap: onTap,
      ),
    );
  }

  IconData _getFileIcon(String contentType) {
    if (contentType.contains('pdf')) {
      return Icons.picture_as_pdf;
    } else if (contentType.contains('text')) {
      return Icons.description;
    } else if (contentType.contains('markdown')) {
      return Icons.code;
    } else {
      return Icons.insert_drive_file;
    }
  }
}

// Extension to handle null safety for firstOrNull
extension IterableExtension<T> on Iterable<T> {
  T? get firstOrNull {
    if (isEmpty) return null;
    return first;
  }
}
