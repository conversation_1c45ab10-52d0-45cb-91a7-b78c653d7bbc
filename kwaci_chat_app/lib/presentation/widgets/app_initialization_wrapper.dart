import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../core/providers/app_initialization_provider.dart';
import 'auth/auth_wrapper.dart';

class AppInitializationWrapper extends ConsumerWidget {
  const AppInitializationWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final initializationAsync = ref.watch(appInitializationProvider);

    return initializationAsync.when(
      data: (result) {
        if (result.isCompleted) {
          // Initialization completed successfully, show the main app
          return const AuthWrapper();
        } else if (result.isError) {
          // Initialization failed, show error screen
          return _InitializationErrorScreen(
            errorMessage: result.errorMessage ?? 'Unknown error occurred',
          );
        } else {
          // This shouldn't happen, but show loading as fallback
          return const _InitializationLoadingScreen();
        }
      },
      loading: () => const _InitializationLoadingScreen(),
      error: (error, stackTrace) => _InitializationErrorScreen(
        errorMessage: 'Failed to initialize app: $error',
      ),
    );
  }
}

class _InitializationLoadingScreen extends StatelessWidget {
  const _InitializationLoadingScreen();

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        backgroundColor: const Color(0xFF6366F1), // Primary color
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App Logo
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(30),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.chat_bubble_outline,
                  size: 60,
                  color: Color(0xFF6366F1),
                ),
              ),
              
              const SizedBox(height: AppConstants.largePadding),
              
              // App Name
              const Text(
                'Kwaci Chat',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              
              const SizedBox(height: AppConstants.smallPadding),
              
              // Loading Text
              Text(
                'Initializing...',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
              
              const SizedBox(height: AppConstants.largePadding * 2),
              
              // Loading Indicator
              const SizedBox(
                width: 40,
                height: 40,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 3,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _InitializationErrorScreen extends StatelessWidget {
  final String errorMessage;

  const _InitializationErrorScreen({
    required this.errorMessage,
  });

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        backgroundColor: const Color(0xFF6366F1),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.largePadding),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Error Icon
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: const Icon(
                      Icons.error_outline,
                      size: 60,
                      color: Colors.red,
                    ),
                  ),
                  
                  const SizedBox(height: AppConstants.largePadding),
                  
                  // Error Title
                  const Text(
                    'Initialization Failed',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: AppConstants.defaultPadding),
                  
                  // Error Message
                  Text(
                    errorMessage,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white.withOpacity(0.8),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: AppConstants.largePadding * 2),
                  
                  // Retry Button
                  ElevatedButton(
                    onPressed: () {
                      // Restart the app
                      // In a real app, you might want to implement a proper restart mechanism
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: const Color(0xFF6366F1),
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.largePadding,
                        vertical: AppConstants.defaultPadding,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                      ),
                    ),
                    child: const Text(
                      'Retry',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
