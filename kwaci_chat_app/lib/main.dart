import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/theme/app_theme.dart';
import 'presentation/pages/profile_page.dart';
import 'presentation/widgets/app_initialization_wrapper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // All initialization is now handled by AppInitializationProvider
  runApp(const ProviderScope(child: KwaciChatApp()));
}

class KwaciChatApp extends ConsumerWidget {
  const KwaciChatApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeModeProvider);

    return MaterialApp(
      title: '<PERSON><PERSON><PERSON> Chat',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeMode,
      home: const AppInitializationWrapper(),
    );
  }
}
