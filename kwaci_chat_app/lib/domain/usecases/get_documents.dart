import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/document.dart';
import '../repositories/document_repository.dart';

class GetDocuments {
  final DocumentRepository repository;

  GetDocuments(this.repository);

  Future<Either<Failure, List<Document>>> call() async {
    return await repository.getDocuments();
  }
}

class GetCachedDocuments {
  final DocumentRepository repository;

  GetCachedDocuments(this.repository);

  Future<Either<Failure, List<Document>>> call() async {
    return await repository.getCachedDocuments();
  }
}
