import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../entities/user.dart';
import '../../repositories/auth_repository.dart';

class SignUp {
  final AuthRepository repository;

  SignUp(this.repository);

  Future<Either<Failure, User>> call({
    required String email,
    required String password,
    required String confirmPassword,
  }) async {
    // Email validation
    if (email.isEmpty || !email.contains('@')) {
      return Left(ValidationFailure(message: 'Please enter a valid email address'));
    }

    // Password validation
    if (password.isEmpty || password.length < 6) {
      return Left(ValidationFailure(message: 'Password must be at least 6 characters long'));
    }

    // Confirm password validation
    if (password != confirmPassword) {
      return Left(ValidationFailure(message: 'Passwords do not match'));
    }

    return await repository.signUp(email: email, password: password);
  }
}
