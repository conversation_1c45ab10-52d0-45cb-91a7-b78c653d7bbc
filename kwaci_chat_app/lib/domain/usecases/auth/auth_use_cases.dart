import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../data/repositories/auth_repository_impl.dart';
import 'check_auth_status.dart';
import 'get_current_user.dart';
import 'sign_in.dart';
import 'sign_out.dart';
import 'sign_up.dart';

// Use case providers
final signInProvider = Provider<SignIn>((ref) {
  final repository = ref.read(authRepositoryProvider);
  return SignIn(repository);
});

final signUpProvider = Provider<SignUp>((ref) {
  final repository = ref.read(authRepositoryProvider);
  return SignUp(repository);
});

final signOutProvider = Provider<SignOut>((ref) {
  final repository = ref.read(authRepositoryProvider);
  return SignOut(repository);
});

final getCurrentUserProvider = Provider<GetCurrentUser>((ref) {
  final repository = ref.read(authRepositoryProvider);
  return GetCurrentUser(repository);
});

final checkAuthStatusProvider = Provider<CheckAuthStatus>((ref) {
  final repository = ref.read(authRepositoryProvider);
  return CheckAuthStatus(repository);
});
