import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/chat_repository_impl.dart';
import 'send_message.dart';
import 'get_chats.dart';
import 'create_chat.dart';

// Use case providers
final sendMessageProvider = Provider<SendMessage>((ref) {
  final repository = ref.read(chatRepositoryProvider);
  return SendMessage(repository);
});

final getChatsProvider = Provider<GetChats>((ref) {
  final repository = ref.read(chatRepositoryProvider);
  return GetChats(repository);
});

final getCachedChatsProvider = Provider<GetCachedChats>((ref) {
  final repository = ref.read(chatRepositoryProvider);
  return GetCachedChats(repository);
});

final getChatProvider = Provider<GetChat>((ref) {
  final repository = ref.read(chatRepositoryProvider);
  return GetChat(repository);
});

final createChatProvider = Provider<CreateChat>((ref) {
  final repository = ref.read(chatRepositoryProvider);
  return CreateChat(repository);
});

final deleteChatProvider = Provider<DeleteChat>((ref) {
  final repository = ref.read(chatRepositoryProvider);
  return DeleteChat(repository);
});

final updateChatTitleProvider = Provider<UpdateChatTitle>((ref) {
  final repository = ref.read(chatRepositoryProvider);
  return UpdateChatTitle(repository);
});
