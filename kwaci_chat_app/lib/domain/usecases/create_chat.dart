import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/chat.dart';
import '../repositories/chat_repository.dart';

class CreateChat {
  final ChatRepository repository;

  CreateChat(this.repository);

  Future<Either<Failure, Chat>> call({String? title}) async {
    // Validate title if provided
    if (title != null && title.trim().isEmpty) {
      return const Left(
        ValidationFailure(
          message: 'Chat title cannot be empty if provided.',
        ),
      );
    }

    return await repository.createChat(title: title);
  }
}

class DeleteChat {
  final ChatRepository repository;

  DeleteChat(this.repository);

  Future<Either<Failure, void>> call(String chatId) async {
    if (chatId.isEmpty) {
      return const Left(
        ValidationFailure(message: 'Chat ID cannot be empty'),
      );
    }

    return await repository.deleteChat(chatId);
  }
}

class UpdateChatTitle {
  final ChatRepository repository;

  UpdateChatTitle(this.repository);

  Future<Either<Failure, Chat>> call(String chatId, String title) async {
    if (chatId.isEmpty) {
      return const Left(
        ValidationFailure(message: 'Chat ID cannot be empty'),
      );
    }

    if (title.trim().isEmpty) {
      return const Left(
        ValidationFailure(message: 'Chat title cannot be empty'),
      );
    }

    return await repository.updateChatTitle(chatId, title);
  }
}
