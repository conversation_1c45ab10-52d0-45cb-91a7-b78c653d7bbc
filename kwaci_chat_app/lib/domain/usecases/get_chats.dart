import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/chat.dart';
import '../repositories/chat_repository.dart';

class GetChats {
  final ChatRepository repository;

  GetChats(this.repository);

  Future<Either<Failure, List<Chat>>> call() async {
    return await repository.getChats();
  }
}

class GetCachedChats {
  final ChatRepository repository;

  GetCachedChats(this.repository);

  Future<Either<Failure, List<Chat>>> call() async {
    return await repository.getCachedChats();
  }
}

class GetChat {
  final ChatRepository repository;

  GetChat(this.repository);

  Future<Either<Failure, Chat>> call(String chatId) async {
    if (chatId.isEmpty) {
      return const Left(
        ValidationFailure(message: 'Chat ID cannot be empty'),
      );
    }

    return await repository.getChat(chatId);
  }
}
