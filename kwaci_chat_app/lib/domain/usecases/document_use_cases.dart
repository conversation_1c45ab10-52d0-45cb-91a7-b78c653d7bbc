import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/document_repository_impl.dart';
import 'upload_document.dart';
import 'get_documents.dart';
import 'delete_document.dart';

// Use case providers
final uploadDocumentProvider = Provider<UploadDocument>((ref) {
  final repository = ref.read(documentRepositoryProvider);
  return UploadDocument(repository);
});

final getDocumentsProvider = Provider<GetDocuments>((ref) {
  final repository = ref.read(documentRepositoryProvider);
  return GetDocuments(repository);
});

final getCachedDocumentsProvider = Provider<GetCachedDocuments>((ref) {
  final repository = ref.read(documentRepositoryProvider);
  return GetCachedDocuments(repository);
});

final deleteDocumentProvider = Provider<DeleteDocument>((ref) {
  final repository = ref.read(documentRepositoryProvider);
  return DeleteDocument(repository);
});
