import 'dart:io';
import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/utils/validators.dart';
import '../entities/document.dart';
import '../repositories/document_repository.dart';

class UploadDocument {
  final DocumentRepository repository;

  UploadDocument(this.repository);

  Future<Either<Failure, Document>> call({
    required File file,
    String? title,
  }) async {
    // Validate file
    final validationResult = _validateFile(file);
    if (validationResult != null) {
      return Left(validationResult);
    }

    // Upload document
    return await repository.uploadDocument(
      file: file,
      title: title,
    );
  }

  ValidationFailure? _validateFile(File file) {
    // Check if file exists
    if (!file.existsSync()) {
      return const ValidationFailure(
        message: 'File does not exist',
      );
    }

    // Get file info
    final fileName = file.path.split('/').last;
    final fileSize = file.lengthSync();

    // Validate file size
    if (!Validators.isValidFileSize(fileSize)) {
      return ValidationFailure(
        message: 'File size (${Validators.formatFileSize(fileSize)}) exceeds the maximum limit of 50MB',
      );
    }

    // Validate file type
    if (!Validators.isValidFileType(fileName)) {
      return const ValidationFailure(
        message: 'Unsupported file type. Please select PDF, TXT, or MD files',
      );
    }

    return null;
  }
}
