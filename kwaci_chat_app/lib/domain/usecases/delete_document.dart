import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../repositories/document_repository.dart';

class DeleteDocument {
  final DocumentRepository repository;

  DeleteDocument(this.repository);

  Future<Either<Failure, void>> call(String documentId) async {
    if (documentId.isEmpty) {
      return const Left(
        ValidationFailure(message: 'Document ID cannot be empty'),
      );
    }

    return await repository.deleteDocument(documentId);
  }
}
