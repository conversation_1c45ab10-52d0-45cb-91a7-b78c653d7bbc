import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/utils/validators.dart';
import '../entities/message.dart';
import '../repositories/chat_repository.dart';

class SendMessage {
  final ChatRepository repository;

  SendMessage(this.repository);

  Future<Either<Failure, Message>> call({
    required String chatId,
    required String content,
    List<String> documentIds = const [],
  }) async {
    // Validate message content
    if (!Validators.isValidMessage(content)) {
      return const Left(
        ValidationFailure(
          message: 'Message content is invalid. Please enter a valid message (1-1000 characters).',
        ),
      );
    }

    // Validate chat ID
    if (chatId.isEmpty) {
      return const Left(
        ValidationFailure(
          message: 'Chat ID cannot be empty.',
        ),
      );
    }

    // Send message
    return await repository.sendMessage(
      chatId: chatId,
      content: content,
      documentIds: documentIds,
    );
  }
}
