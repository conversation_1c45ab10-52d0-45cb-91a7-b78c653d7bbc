import 'package:equatable/equatable.dart';
import 'message.dart';

class Chat extends Equatable {
  final String id;
  final String title;
  final List<Message> messages;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Chat({
    required this.id,
    required this.title,
    required this.messages,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        messages,
        createdAt,
        updatedAt,
      ];

  Chat copyWith({
    String? id,
    String? title,
    List<Message>? messages,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Chat(
      id: id ?? this.id,
      title: title ?? this.title,
      messages: messages ?? this.messages,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  Message? get lastMessage {
    if (messages.isEmpty) return null;
    return messages.last;
  }

  String get displayTitle {
    if (title.isNotEmpty) return title;
    if (messages.isNotEmpty) {
      final firstUserMessage = messages.firstWhere(
        (message) => message.isUser,
        orElse: () => messages.first,
      );
      return firstUserMessage.content.length > 50
          ? '${firstUserMessage.content.substring(0, 50)}...'
          : firstUserMessage.content;
    }
    return 'New Chat';
  }

  Chat addMessage(Message message) {
    final updatedMessages = [...messages, message];
    return copyWith(
      messages: updatedMessages,
      updatedAt: DateTime.now(),
    );
  }

  Chat updateMessage(String messageId, Message updatedMessage) {
    final updatedMessages = messages.map((message) {
      return message.id == messageId ? updatedMessage : message;
    }).toList();
    
    return copyWith(
      messages: updatedMessages,
      updatedAt: DateTime.now(),
    );
  }

  Chat removeMessage(String messageId) {
    final updatedMessages = messages.where((message) => message.id != messageId).toList();
    return copyWith(
      messages: updatedMessages,
      updatedAt: DateTime.now(),
    );
  }
}
