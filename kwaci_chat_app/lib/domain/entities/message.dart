import 'package:equatable/equatable.dart';

class Message extends Equatable {
  final String id;
  final String content;
  final bool isUser;
  final DateTime timestamp;
  final List<String> documentReferences;
  final List<Citation>? citations;
  final MessageStatus status;
  final String? errorMessage;

  const Message({
    required this.id,
    required this.content,
    required this.isUser,
    required this.timestamp,
    this.documentReferences = const [],
    this.citations,
    this.status = MessageStatus.sent,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [
        id,
        content,
        isUser,
        timestamp,
        documentReferences,
        citations,
        status,
        errorMessage,
      ];

  Message copyWith({
    String? id,
    String? content,
    bool? isUser,
    DateTime? timestamp,
    List<String>? documentReferences,
    List<Citation>? citations,
    MessageStatus? status,
    String? errorMessage,
  }) {
    return Message(
      id: id ?? this.id,
      content: content ?? this.content,
      isUser: isUser ?? this.isUser,
      timestamp: timestamp ?? this.timestamp,
      documentReferences: documentReferences ?? this.documentReferences,
      citations: citations ?? this.citations,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

class Citation extends Equatable {
  final String type;
  final String citedText;
  final int documentIndex;
  final String documentTitle;
  final String? pageLocation;
  final String? charLocation;

  const Citation({
    required this.type,
    required this.citedText,
    required this.documentIndex,
    required this.documentTitle,
    this.pageLocation,
    this.charLocation,
  });

  @override
  List<Object?> get props => [
        type,
        citedText,
        documentIndex,
        documentTitle,
        pageLocation,
        charLocation,
      ];

  Citation copyWith({
    String? type,
    String? citedText,
    int? documentIndex,
    String? documentTitle,
    String? pageLocation,
    String? charLocation,
  }) {
    return Citation(
      type: type ?? this.type,
      citedText: citedText ?? this.citedText,
      documentIndex: documentIndex ?? this.documentIndex,
      documentTitle: documentTitle ?? this.documentTitle,
      pageLocation: pageLocation ?? this.pageLocation,
      charLocation: charLocation ?? this.charLocation,
    );
  }
}

enum MessageStatus {
  sending,
  sent,
  failed,
}

extension MessageStatusExtension on MessageStatus {
  String get displayName {
    switch (this) {
      case MessageStatus.sending:
        return 'Sending';
      case MessageStatus.sent:
        return 'Sent';
      case MessageStatus.failed:
        return 'Failed';
    }
  }

  bool get isSending => this == MessageStatus.sending;
  bool get isSent => this == MessageStatus.sent;
  bool get isFailed => this == MessageStatus.failed;
}
