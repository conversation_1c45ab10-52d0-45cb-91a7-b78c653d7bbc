import 'package:equatable/equatable.dart';

class User extends Equatable {
  final String id;
  final String email;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  const User({
    required this.id,
    required this.email,
    this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  @override
  List<Object?> get props => [id, email, createdAt, updatedAt, metadata];

  @override
  String toString() {
    return 'User(id: $id, email: $email, createdAt: $createdAt, updatedAt: $updatedAt, metadata: $metadata)';
  }
}
