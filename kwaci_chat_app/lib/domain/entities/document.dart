import 'package:equatable/equatable.dart';

class Document extends Equatable {
  final String id;
  final String filename;
  final String filePath;
  final DocumentMetadata metadata;
  final DocumentStatus status;
  final String? errorMessage;

  const Document({
    required this.id,
    required this.filename,
    required this.filePath,
    required this.metadata,
    required this.status,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [
        id,
        filename,
        filePath,
        metadata,
        status,
        errorMessage,
      ];

  Document copyWith({
    String? id,
    String? filename,
    String? filePath,
    DocumentMetadata? metadata,
    DocumentStatus? status,
    String? errorMessage,
  }) {
    return Document(
      id: id ?? this.id,
      filename: filename ?? this.filename,
      filePath: filePath ?? this.filePath,
      metadata: metadata ?? this.metadata,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

class DocumentMetadata extends Equatable {
  final String originalFilename;
  final int fileSize;
  final String contentType;
  final DateTime uploadTimestamp;
  final int? chunkCount;

  const DocumentMetadata({
    required this.originalFilename,
    required this.fileSize,
    required this.contentType,
    required this.uploadTimestamp,
    this.chunkCount,
  });

  @override
  List<Object?> get props => [
        originalFilename,
        fileSize,
        contentType,
        uploadTimestamp,
        chunkCount,
      ];

  DocumentMetadata copyWith({
    String? originalFilename,
    int? fileSize,
    String? contentType,
    DateTime? uploadTimestamp,
    int? chunkCount,
  }) {
    return DocumentMetadata(
      originalFilename: originalFilename ?? this.originalFilename,
      fileSize: fileSize ?? this.fileSize,
      contentType: contentType ?? this.contentType,
      uploadTimestamp: uploadTimestamp ?? this.uploadTimestamp,
      chunkCount: chunkCount ?? this.chunkCount,
    );
  }
}

enum DocumentStatus {
  processing,
  completed,
  failed,
}

extension DocumentStatusExtension on DocumentStatus {
  String get displayName {
    switch (this) {
      case DocumentStatus.processing:
        return 'Processing';
      case DocumentStatus.completed:
        return 'Completed';
      case DocumentStatus.failed:
        return 'Failed';
    }
  }

  bool get isCompleted => this == DocumentStatus.completed;
  bool get isProcessing => this == DocumentStatus.processing;
  bool get isFailed => this == DocumentStatus.failed;
}
