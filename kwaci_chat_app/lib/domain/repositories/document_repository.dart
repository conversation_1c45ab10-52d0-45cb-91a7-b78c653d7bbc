import 'dart:io';
import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/document.dart';

abstract class DocumentRepository {
  /// Upload a document file to the server
  Future<Either<Failure, Document>> uploadDocument({
    required File file,
    String? title,
  });

  /// Get list of all uploaded documents
  Future<Either<Failure, List<Document>>> getDocuments();

  /// Get a specific document by ID
  Future<Either<Failure, Document>> getDocument(String documentId);

  /// Delete a document by ID
  Future<Either<Failure, void>> deleteDocument(String documentId);

  /// Get documents from local cache
  Future<Either<Failure, List<Document>>> getCachedDocuments();

  /// Cache documents locally
  Future<Either<Failure, void>> cacheDocuments(List<Document> documents);

  /// Clear local document cache
  Future<Either<Failure, void>> clearDocumentCache();
}
