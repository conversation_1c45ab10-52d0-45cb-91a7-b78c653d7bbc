import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/chat.dart';
import '../entities/message.dart';

abstract class ChatRepository {
  /// Send a message and get AI response
  Future<Either<Failure, Message>> sendMessage({
    required String chatId,
    required String content,
    List<String> documentIds = const [],
  });

  /// Get list of all chats
  Future<Either<Failure, List<Chat>>> getChats();

  /// Get a specific chat by ID
  Future<Either<Failure, Chat>> getChat(String chatId);

  /// Create a new chat
  Future<Either<Failure, Chat>> createChat({String? title});

  /// Update chat title
  Future<Either<Failure, Chat>> updateChatTitle(String chatId, String title);

  /// Delete a chat
  Future<Either<Failure, void>> deleteChat(String chatId);

  /// Get chats from local cache
  Future<Either<Failure, List<Chat>>> getCachedChats();

  /// Cache chats locally
  Future<Either<Failure, void>> cacheChats(List<Chat> chats);

  /// Cache a single chat
  Future<Either<Failure, void>> cacheChat(Chat chat);

  /// Clear chat cache
  Future<Either<Failure, void>> clearChatCache();
}
