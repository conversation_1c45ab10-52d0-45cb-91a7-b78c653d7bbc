import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/user.dart';

abstract class AuthRepository {
  /// Sign up with email and password
  Future<Either<Failure, User>> signUp({
    required String email,
    required String password,
  });

  /// Sign in with email and password
  Future<Either<Failure, User>> signIn({
    required String email,
    required String password,
  });

  /// Sign out current user
  Future<Either<Failure, void>> signOut();

  /// Get current authenticated user
  Future<Either<Failure, User?>> getCurrentUser();

  /// Check if user is currently authenticated
  Future<Either<Failure, bool>> isAuthenticated();

  /// Stream of authentication state changes
  Stream<User?> get authStateChanges;
}
