class ServerException implements Exception {
  final String message;
  final String? code;
  
  const ServerException({
    required this.message,
    this.code,
  });
  
  @override
  String toString() => 'ServerException: $message${code != null ? ' (Code: $code)' : ''}';
}

class NetworkException implements Exception {
  final String message;
  
  const NetworkException({
    required this.message,
  });
  
  @override
  String toString() => 'NetworkException: $message';
}

class CacheException implements Exception {
  final String message;
  
  const CacheException({
    required this.message,
  });
  
  @override
  String toString() => 'CacheException: $message';
}

class ValidationException implements Exception {
  final String message;
  
  const ValidationException({
    required this.message,
  });
  
  @override
  String toString() => 'ValidationException: $message';
}

class FileException implements Exception {
  final String message;
  
  const FileException({
    required this.message,
  });
  
  @override
  String toString() => 'FileException: $message';
}
