class AppConstants {
  // API Configuration
  static const String baseUrl = 'http://localhost:8000/api/v1';
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds

  // Supabase Configuration
  static const String supabaseUrl = 'https://ebmvlcukrpolsllrrruf.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVibXZsY3VrcnBvbHNsbHJycnVmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxNDQyNTIsImV4cCI6MjA2MzcyMDI1Mn0.8iGakBC35bmOIwzkW2ow-yjdWWzyt1bsH8x7tA0zTEw';

  // File Upload Limits
  static const int maxFileSize = 50 * 1024 * 1024; // 50MB
  static const List<String> supportedFileTypes = ['pdf', 'txt', 'md'];
  static const List<String> supportedMimeTypes = [
    'application/pdf',
    'text/plain',
    'text/markdown',
  ];

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // Local Storage Keys
  static const String chatHistoryBox = 'chat_history';
  static const String documentsBox = 'documents';
  static const String settingsBox = 'settings';
  static const String themeKey = 'theme_mode';

  // Error Messages
  static const String networkError =
      'Network connection failed. Please check your internet connection.';
  static const String serverError =
      'Server error occurred. Please try again later.';
  static const String fileUploadError =
      'Failed to upload file. Please try again.';
  static const String fileSizeError =
      'File size exceeds the maximum limit of 50MB.';
  static const String fileTypeError =
      'Unsupported file type. Please select PDF, TXT, or MD files.';
  static const String queryError =
      'Failed to process your question. Please try again.';

  // Success Messages
  static const String fileUploadSuccess = 'File uploaded successfully!';
  static const String documentDeleteSuccess = 'Document deleted successfully!';

  // Placeholder Texts
  static const String chatPlaceholder = 'Type your message here...';
  static const String searchPlaceholder = 'Search documents...';
  static const String noDocumentsMessage =
      'No documents uploaded yet. Upload your first document to get started!';
  static const String noChatHistoryMessage =
      'No chat history yet. Start a conversation!';

  // Navigation
  static const String homeRoute = '/home';
  static const String chatRoute = '/chat';
  static const String profileRoute = '/profile';
  static const String chatDetailRoute = '/chat/:chatId';
  static const String documentsRoute = '/documents';

  // Auth Routes
  static const String splashRoute = '/';
  static const String introductionRoute = '/introduction';
  static const String loginRoute = '/login';
  static const String registerRoute = '/register';
  static const String mainRoute = '/main';
}
