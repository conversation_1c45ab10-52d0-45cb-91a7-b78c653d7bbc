import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../constants/app_constants.dart';

/// Represents the initialization state of the app
enum AppInitializationState {
  initializing,
  completed,
  error,
}

/// Data class to hold initialization result
class AppInitializationResult {
  final AppInitializationState state;
  final String? errorMessage;

  const AppInitializationResult({
    required this.state,
    this.errorMessage,
  });

  bool get isCompleted => state == AppInitializationState.completed;
  bool get isError => state == AppInitializationState.error;
  bool get isInitializing => state == AppInitializationState.initializing;
}

/// Provider that handles all app initialization
final appInitializationProvider = FutureProvider<AppInitializationResult>((ref) async {
  try {
    // Initialize Hive for local storage
    await Hive.initFlutter();

    // Initialize Supabase
    await Supabase.initialize(
      url: AppConstants.supabaseUrl,
      anonKey: AppConstants.supabaseAnonKey,
    );

    // Add any other initialization logic here
    // For example: Firebase, analytics, etc.

    return const AppInitializationResult(
      state: AppInitializationState.completed,
    );
  } catch (error) {
    return AppInitializationResult(
      state: AppInitializationState.error,
      errorMessage: 'Failed to initialize app: $error',
    );
  }
});

/// Provider to check if Supabase is ready
final supabaseReadyProvider = Provider<bool>((ref) {
  final initResult = ref.watch(appInitializationProvider);
  return initResult.when(
    data: (result) => result.isCompleted,
    loading: () => false,
    error: (_, __) => false,
  );
});
