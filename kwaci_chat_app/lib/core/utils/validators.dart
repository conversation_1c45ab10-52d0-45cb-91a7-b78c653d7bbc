import '../constants/app_constants.dart';

class Validators {
  // Validate file size
  static bool isValidFileSize(int fileSizeInBytes) {
    return fileSizeInBytes <= AppConstants.maxFileSize;
  }
  
  // Validate file type by extension
  static bool isValidFileType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    return AppConstants.supportedFileTypes.contains(extension);
  }
  
  // Validate file type by MIME type
  static bool isValidMimeType(String mimeType) {
    return AppConstants.supportedMimeTypes.contains(mimeType);
  }
  
  // Validate message content
  static bool isValidMessage(String message) {
    return message.trim().isNotEmpty && message.trim().length <= 1000;
  }
  
  // Validate document title
  static bool isValidDocumentTitle(String title) {
    return title.trim().isNotEmpty && title.trim().length <= 100;
  }
  
  // Get file size in human readable format
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
  
  // Get file extension from filename
  static String getFileExtension(String fileName) {
    return fileName.split('.').last.toLowerCase();
  }
  
  // Get file name without extension
  static String getFileNameWithoutExtension(String fileName) {
    final parts = fileName.split('.');
    if (parts.length > 1) {
      parts.removeLast();
    }
    return parts.join('.');
  }
  
  // Validate URL format
  static bool isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }
  
  // Sanitize filename for safe storage
  static String sanitizeFileName(String fileName) {
    // Remove or replace invalid characters
    return fileName
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '_')
        .replaceAll(RegExp(r'\s+'), '_')
        .toLowerCase();
  }
}
